import 'dart:async';
import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:remote_configuration/src/backend/remote_config_backend.dart';
import 'package:remote_configuration/src/options/remote_config_options.dart';

class FirebaseRemoteConfigBackend implements RemoteConfigBackend {
  FirebaseRemoteConfigBackend([FirebaseRemoteConfig? instance])
    : _instance = instance ?? FirebaseRemoteConfig.instance;

  final FirebaseRemoteConfig _instance;
  final _controller = StreamController<void>.broadcast();

  @override
  Future<void> initialize(RemoteConfigOptions options) async {
    await _instance.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: options.fetchTimeout,
        minimumFetchInterval: options.minimumFetchInterval,
      ),
    );
    if (options.defaults.isNotEmpty) {
      await _instance.setDefaults(options.defaults);
    }

    // Bridge onConfigUpdated to our stream
    late final StreamSubscription<void> _sub;
    _sub = _instance.onConfigUpdated.listen((event) {
      // Activate synchronously via then to satisfy lint's sync expectation
      _instance.activate().whenComplete(() {
        _controller.add(null);
      });
    });
    // Clean up subscription when controller is closed
    _controller.onCancel = () async {
      await _sub.cancel();
    };
  }

  @override
  Future<void> fetch() async {
    await _instance.fetch();
    // Do not emit here; activation will emit when values are applied.
  }

  @override
  Future<bool> activate() async {
    final changed = await _instance.activate();
    if (changed) _controller.add(null);
    return changed;
  }

  @override
  Future<bool> fetchAndActivate({bool force = false}) async {
    if (force) {
      await _instance.fetchAndActivate();
      _controller.add(null);
      return true;
    }
    final changed = await _instance.fetchAndActivate();
    if (changed) _controller.add(null);
    return changed;
  }

  @override
  String? getString(String key) {
    final all = _instance.getAll();
    if (!all.containsKey(key)) return null;
    final v = _instance.getString(key);
    return v.isEmpty ? null : v;
  }

  @override
  int? getInt(String key) {
    final all = _instance.getAll();
    if (!all.containsKey(key)) return null;
    return _instance.getInt(key);
  }

  @override
  double? getDouble(String key) {
    final all = _instance.getAll();
    if (!all.containsKey(key)) return null;
    return _instance.getDouble(key);
  }

  @override
  bool? getBool(String key) {
    final all = _instance.getAll();
    if (!all.containsKey(key)) return null;
    return _instance.getBool(key);
  }

  @override
  Map<String, dynamic>? getJson(String key) {
    final str = _instance.getString(key);
    if (str.isEmpty) return null;
    try {
      final decoded = jsonDecode(str);
      if (decoded is Map<String, dynamic>) return decoded;
    } catch (_) {
      // ignore parse errors; higher layer will handle defaults
    }
    return null;
  }

  @override
  Stream<void> get onUpdated => _controller.stream;

  /// Checks if a value for the given [key] is from a remote source.
  /// Returns true if the value exists and came from Firebase Remote Config.
  bool _isValueFromRemote(String key) {
    final all = _instance.getAll();
    return all.containsKey(key) && all[key]!.source == ValueSource.valueRemote;
  }

  /// Waits for a remote value to be available for the given [key].
  ///
  /// This method ensures that the returned value comes from Firebase Remote Config,
  /// not from default values. It's useful for first app launch scenarios where you
  /// need to guarantee that remote values are loaded before proceeding.
  ///
  /// The method works as follows:
  /// 1. Checks if the value is already from remote source - if yes, returns immediately
  /// 2. Triggers fetchAndActivate() to fetch latest values from Firebase
  /// 3. Checks again if the value is now from remote source
  /// 4. If not, waits for real-time config updates (onConfigUpdated stream)
  /// 5. Returns the value once it's confirmed to be from remote source
  ///
  /// Parameters:
  /// - [key]: The config key to retrieve
  /// - [timeout]: Optional timeout duration. If specified and exceeded, throws [RemoteValueTimeoutException]
  /// - [waitForRealtime]: If true (default), waits for real-time updates if initial fetch doesn't return remote values.
  ///                      If false, returns null immediately if fetch doesn't return remote values.
  ///
  /// Returns the value from remote config, or null if:
  /// - The key doesn't exist in remote config
  /// - [waitForRealtime] is false and initial fetch didn't return remote values
  ///
  /// Throws [RemoteValueTimeoutException] if [timeout] is specified and exceeded.
  Future<String?> getStringWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    return _getValueWhenRemote(
      key,
      getValue: () => getString(key),
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  Future<int?> getIntWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    return _getValueWhenRemote(
      key,
      getValue: () => getInt(key),
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  Future<double?> getDoubleWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    return _getValueWhenRemote(
      key,
      getValue: () => getDouble(key),
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  Future<bool?> getBoolWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    return _getValueWhenRemote(
      key,
      getValue: () => getBool(key),
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  /// Similar to [getStringWhenRemote] but for JSON values.
  Future<Map<String, dynamic>?> getJsonWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    return _getValueWhenRemote(
      key,
      getValue: () => getJson(key),
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  /// Internal helper method that implements the core logic for waiting for remote values.
  Future<T?> _getValueWhenRemote<T>(
    String key, {
    required T? Function() getValue,
    Duration? timeout,
    bool waitForRealtime = true,
  }) async {
    // Step 1: Check if already from remote source
    if (_isValueFromRemote(key)) {
      return getValue();
    }

    // Step 2: Trigger fetch and activate
    try {
      await fetchAndActivate(force: true);
    } catch (e) {
      // Fetch failed, but we might still get updates via real-time config
      // Continue to step 3
    }

    // Step 3: Check again after fetch
    if (_isValueFromRemote(key)) {
      return getValue();
    }

    // Step 4: If not waiting for real-time updates, return null
    if (!waitForRealtime) {
      return null;
    }

    // Step 5: Wait for real-time config updates
    final completer = Completer<T?>();
    late final StreamSubscription<void> subscription;
    late final Timer? timer;

    void cleanup() {
      subscription.cancel();
      timer?.cancel();
    }

    subscription = onUpdated.listen((_) {
      if (_isValueFromRemote(key)) {
        if (!completer.isCompleted) {
          completer.complete(getValue());
          cleanup();
        }
      }
    });

    // Set up timeout if specified
    if (timeout != null) {
      timer = Timer(timeout, () {
        if (!completer.isCompleted) {
          cleanup();
          completer.completeError(
            RemoteValueTimeoutException(key, timeout),
            StackTrace.current,
          );
        }
      });
    }

    return completer.future;
  }
}

/// Exception thrown when waiting for a remote value times out.
class RemoteValueTimeoutException implements Exception {
  const RemoteValueTimeoutException(this.key, this.timeout);

  final String key;
  final Duration timeout;

  @override
  String toString() =>
      'RemoteValueTimeoutException: Timed out waiting for remote value for key "$key" after ${timeout.inSeconds}s';
}
