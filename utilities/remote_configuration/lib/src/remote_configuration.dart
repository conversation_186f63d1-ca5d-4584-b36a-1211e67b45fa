import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:remote_configuration/src/backend/firebase_remote_config_backend.dart';
import 'package:remote_configuration/src/model/remote_config_update.dart';
import 'package:remote_configuration/src/backend/in_memory_remote_config_backend.dart';
import 'package:remote_configuration/src/backend/remote_config_backend.dart';
import 'package:remote_configuration/src/model/remote_config_error.dart';
import 'package:remote_configuration/src/options/remote_config_options.dart';
import 'package:remote_configuration/src/reactive/remote_config_notifier.dart';

abstract interface class RemoteConfiguration {
  static Future<RemoteConfiguration> init({
    RemoteConfigOptions options = const RemoteConfigOptions(),
    RemoteConfigBackend? backend,
    void Function(RemoteConfigError error)? onError,
  }) async {
    final impl = _RemoteConfigurationImpl(
      backend ?? FirebaseRemoteConfigBackend(),
      options,
      onError,
    );
    await impl._initialize();
    return impl;
  }

  /// Convenience for tests to build an in-memory instance.
  static Future<RemoteConfiguration> mock(Map<String, Object?> values) async {
    final backend = InMemoryRemoteConfigBackend();
    final rc = _RemoteConfigurationImpl(
      backend,
      RemoteConfigOptions(defaults: values),
      null,
    );
    await rc._initialize();
    return rc;
  }

  /// Fetch remote values from the backend. Does not activate them.
  Future<void> fetch();

  /// Activate the latest fetched values. Returns true if values changed.
  Future<bool> activate();

  /// Convenience to fetch and activate in one call.
  Future<bool> fetchAndActivate({bool force = false});

  String getString(String key, {required String defaultValue});
  int getInt(String key, {required int defaultValue});
  double getDouble(String key, {required double defaultValue});
  bool getBool(String key, {required bool defaultValue});

  Map<String, dynamic> getJson(
    String key, {
    required Map<String, dynamic> defaultValue,
  });

  T getParsed<T>(
    String key,
    T Function(String raw) parser, {
    required T defaultValue,
  });

  Stream<RemoteConfigUpdate> get updates;
  Stream<T> watchKey<T>(String key, {required T defaultValue});
  ValueListenable<T> listenableKey<T>(String key, {required T defaultValue});

  void setOverride(String key, Object? value);
  void clearOverride(String key);
  void clearAllOverrides();

  bool get isInitialized;
  void dispose();

  // ============================================================================
  // NEW METHODS: Real-time Remote Config with guaranteed remote values
  // ============================================================================

  /// Waits for a remote string value to be available for the given [key].
  ///
  /// This method ensures that the returned value comes from Firebase Remote Config,
  /// not from default values. It's useful for first app launch scenarios where you
  /// need to guarantee that remote values are loaded before proceeding.
  ///
  /// Only supported when using FirebaseRemoteConfigBackend. Throws [UnsupportedError]
  /// if used with other backends.
  ///
  /// Parameters:
  /// - [key]: The config key to retrieve
  /// - [timeout]: Optional timeout duration. If specified and exceeded, throws [RemoteValueTimeoutException]
  /// - [waitForRealtime]: If true (default), waits for real-time updates if initial fetch doesn't return remote values.
  ///
  /// Returns the value from remote config, or null if not available.
  Future<String?> getStringWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  });

  /// Similar to [getStringWhenRemote] but for integer values.
  Future<int?> getIntWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  });

  /// Similar to [getStringWhenRemote] but for double values.
  Future<double?> getDoubleWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  });

  /// Similar to [getStringWhenRemote] but for boolean values.
  Future<bool?> getBoolWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  });

  /// Similar to [getStringWhenRemote] but for JSON values.
  Future<Map<String, dynamic>?> getJsonWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  });
}

final class _RemoteConfigurationImpl implements RemoteConfiguration {
  _RemoteConfigurationImpl(this._backend, this._options, this._onError);

  final RemoteConfigBackend _backend;
  final RemoteConfigOptions _options;
  final void Function(RemoteConfigError error)? _onError;
  final RemoteConfigNotifier _notifier = RemoteConfigNotifier();

  final Map<String, Object?> _overrides = {};
  bool _isInitialized = false;
  Future<bool>? _inFlightFetch;
  StreamSubscription<void>? _backendSub;
  final List<StreamSubscription<void>> _notifierSubs =
      <StreamSubscription<void>>[];

  @override
  bool get isInitialized => _isInitialized;

  Future<void> _initialize() async {
    try {
      await _backend.initialize(_options);
      _backendSub = _backend.onUpdated.listen((_) => _notifier.emit());
      _isInitialized = true;
    } catch (e) {
      _onError?.call(InitializationFailed('Failed to initialize RC', e));
      _isInitialized = false;
      rethrow;
    }
  }

  @override
  Future<void> fetch() async {
    if (!_isInitialized) {
      _onError?.call(
        const NotInitialized('RemoteConfiguration not initialized'),
      );
      return;
    }
    try {
      await _backend.fetch();
    } catch (e) {
      _onError?.call(FetchFailed('Fetch failed', e));
    }
  }

  @override
  Future<bool> activate() async {
    if (!_isInitialized) {
      _onError?.call(
        const NotInitialized('RemoteConfiguration not initialized'),
      );
      return false;
    }
    try {
      final changed = await _backend.activate();
      if (changed) _notifier.emit();
      return changed;
    } catch (e) {
      _onError?.call(ActivationFailed('Activation failed', e));
      return false;
    }
  }

  @override
  Future<bool> fetchAndActivate({bool force = false}) async {
    if (!_isInitialized) {
      _onError?.call(
        const NotInitialized('RemoteConfiguration not initialized'),
      );
      return false;
    }

    if (_inFlightFetch != null && force == false) return _inFlightFetch!;

    final completer = Completer<bool>();
    _inFlightFetch = completer.future;

    try {
      final changed = await _backend.fetchAndActivate(force: force);
      if (changed) _notifier.emit();
      completer.complete(changed);
      return changed;
    } catch (e) {
      _onError?.call(FetchFailed('Fetch failed', e));
      completer.complete(false);
      return false;
    } finally {
      _inFlightFetch = null;
    }
  }

  @override
  String getString(String key, {required String defaultValue}) {
    final k = _options.applyPrefix(key);
    final v = _overrides[k] ?? _backend.getString(k);
    if (v is String && v.isNotEmpty) return v;
    return defaultValue;
  }

  @override
  int getInt(String key, {required int defaultValue}) {
    final k = _options.applyPrefix(key);
    final v = _overrides[k] ?? _backend.getInt(k);
    if (v is int) return v;
    if (v is num) return v.toInt();
    return defaultValue;
  }

  @override
  double getDouble(String key, {required double defaultValue}) {
    final k = _options.applyPrefix(key);
    final v = _overrides[k] ?? _backend.getDouble(k);
    if (v is double) return v;
    if (v is num) return v.toDouble();
    return defaultValue;
  }

  @override
  bool getBool(String key, {required bool defaultValue}) {
    final k = _options.applyPrefix(key);
    final v = _overrides[k] ?? _backend.getBool(k);
    if (v is bool) return v;
    return defaultValue;
  }

  @override
  Map<String, dynamic> getJson(
    String key, {
    required Map<String, dynamic> defaultValue,
  }) {
    final k = _options.applyPrefix(key);
    final v = _overrides[k] ?? _backend.getJson(k);
    if (v is Map<String, dynamic>) return v;
    return defaultValue;
  }

  @override
  T getParsed<T>(
    String key,
    T Function(String raw) parser, {
    required T defaultValue,
  }) {
    final k = _options.applyPrefix(key);
    final v = _overrides[k] ?? _backend.getString(k);
    if (v is String && v.isNotEmpty) {
      try {
        return parser(v);
      } catch (e) {
        _onError?.call(ParseFailed('Parse failed for key: $k', e));
      }
    }
    return defaultValue;
  }

  @override
  Stream<RemoteConfigUpdate> get updates => _notifier.updates;

  @override
  Stream<T> watchKey<T>(String key, {required T defaultValue}) {
    final controller = StreamController<T>.broadcast();
    StreamSubscription<Object?>? sub;
    void emit() => controller.add(_getValue<T>(key, defaultValue));
    controller.onListen = () {
      emit();
      sub = _notifier.updates.listen((_) => emit());
    };
    controller.onCancel = () async {
      await sub?.cancel();
    };
    return controller.stream;
  }

  @override
  ValueListenable<T> listenableKey<T>(String key, {required T defaultValue}) {
    final notifier = _notifier.createValueNotifier(
      _getValue<T>(key, defaultValue),
    );
    final sub = _notifier.updates.listen((_) {
      notifier.value = _getValue<T>(key, defaultValue);
    });
    _notifierSubs.add(sub);
    return notifier;
  }

  T _getValue<T>(String key, T defaultValue) {
    if (T == String)
      return getString(key, defaultValue: defaultValue as String) as T;
    if (T == int) return getInt(key, defaultValue: defaultValue as int) as T;
    if (T == double)
      return getDouble(key, defaultValue: defaultValue as double) as T;
    if (T == bool) return getBool(key, defaultValue: defaultValue as bool) as T;

    // Map<String, dynamic> special-case
    if (defaultValue is Map<String, dynamic>) {
      return getJson(key, defaultValue: defaultValue) as T;
    }

    // Fallback: try getParsed expecting a string, otherwise default
    // For unsupported types, we cannot parse without a parser; return default
    return defaultValue;
  }

  @override
  void setOverride(String key, Object? value) {
    final k = _options.applyPrefix(key);
    _overrides[k] = value;
    _notifier.emit([k]);
  }

  @override
  void clearOverride(String key) {
    final k = _options.applyPrefix(key);
    _overrides.remove(k);
    _notifier.emit([k]);
  }

  @override
  void clearAllOverrides() {
    if (_overrides.isEmpty) return;
    _overrides.clear();
    _notifier.emit();
  }

  @override
  void dispose() {
    _backendSub?.cancel();
    for (final s in _notifierSubs) {
      s.cancel();
    }
    _notifierSubs.clear();
    _notifier.dispose();
  }

  // ============================================================================
  // NEW METHODS: Real-time Remote Config with guaranteed remote values
  // ============================================================================

  @override
  Future<String?> getStringWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    if (_backend is! FirebaseRemoteConfigBackend) {
      throw UnsupportedError(
        'getStringWhenRemote is only supported with FirebaseRemoteConfigBackend',
      );
    }
    final k = _options.applyPrefix(key);
    return (_backend).getStringWhenRemote(
      k,
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  @override
  Future<int?> getIntWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    if (_backend is! FirebaseRemoteConfigBackend) {
      throw UnsupportedError(
        'getIntWhenRemote is only supported with FirebaseRemoteConfigBackend',
      );
    }
    final k = _options.applyPrefix(key);
    return (_backend).getIntWhenRemote(
      k,
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  @override
  Future<double?> getDoubleWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    if (_backend is! FirebaseRemoteConfigBackend) {
      throw UnsupportedError(
        'getDoubleWhenRemote is only supported with FirebaseRemoteConfigBackend',
      );
    }
    final k = _options.applyPrefix(key);
    return (_backend).getDoubleWhenRemote(
      k,
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  @override
  Future<bool?> getBoolWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    if (_backend is! FirebaseRemoteConfigBackend) {
      throw UnsupportedError(
        'getBoolWhenRemote is only supported with FirebaseRemoteConfigBackend',
      );
    }
    final k = _options.applyPrefix(key);
    return (_backend).getBoolWhenRemote(
      k,
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }

  @override
  Future<Map<String, dynamic>?> getJsonWhenRemote(
    String key, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) {
    if (_backend is! FirebaseRemoteConfigBackend) {
      throw UnsupportedError(
        'getJsonWhenRemote is only supported with FirebaseRemoteConfigBackend',
      );
    }
    final k = _options.applyPrefix(key);
    return (_backend).getJsonWhenRemote(
      k,
      timeout: timeout,
      waitForRealtime: waitForRealtime,
    );
  }
}
