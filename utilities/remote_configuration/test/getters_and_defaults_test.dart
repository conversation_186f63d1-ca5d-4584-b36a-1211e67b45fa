import 'package:flutter_test/flutter_test.dart';
import 'package:remote_configuration/remote_configuration.dart';
import 'package:remote_configuration/src/backend/in_memory_remote_config_backend.dart';

void main() {
  group('RemoteConfiguration getters and defaults', () {
    test('returns provided values for basic types', () async {
      final rc = await RemoteConfiguration.mock({
        's': 'hello',
        'i': 42,
        'd': 3.14,
        'b': true,
        'j': {'k': 'v'},
      });

      expect(rc.getString('s', defaultValue: 'x'), 'hello');
      expect(rc.getInt('i', defaultValue: 0), 42);
      expect(rc.getDouble('d', defaultValue: 0), 3.14);
      expect(rc.getBool('b', defaultValue: false), isTrue);
      expect(rc.getJson('j', defaultValue: const {}), {'k': 'v'});
    });

    test('falls back to defaults when missing or mismatched types', () async {
      final rc = await RemoteConfiguration.mock({});

      expect(rc.getString('s', defaultValue: 'x'), 'x');
      expect(rc.getInt('i', defaultValue: 7), 7);
      expect(rc.getDouble('d', defaultValue: 2.5), 2.5);
      expect(rc.getBool('b', defaultValue: true), isTrue);
      expect(rc.getJson('j', defaultValue: const {'a': 1}), {'a': 1});
    });

    test('keyPrefix is applied to lookups', () async {
      // Build a mock-like instance by directly using the in-memory backend
      final backend = InMemoryRemoteConfigBackend();
      await backend.initialize(
        const RemoteConfigOptions(defaults: {'prod__flag': true}),
      );
      final rc = await RemoteConfiguration.init(
        options: const RemoteConfigOptions(keyPrefix: 'prod__'),
        backend: backend,
      );
      expect(rc.getBool('flag', defaultValue: false), isTrue);
    });

    test('getParsed uses parser and returns default on parse error', () async {
      RemoteConfigError? captured;
      final backend = InMemoryRemoteConfigBackend();
      await backend.initialize(
        const RemoteConfigOptions(defaults: {'n': '2.5'}),
      );
      final rc = await RemoteConfiguration.init(
        options: const RemoteConfigOptions(),
        backend: backend,
        onError: (e) => captured = e,
      );

      final parsed = rc.getParsed<double>(
        'n',
        (raw) => double.parse(raw),
        defaultValue: 0,
      );
      expect(parsed, 2.5);

      final fb = rc.getParsed<int>(
        'n',
        (raw) => int.parse(raw),
        defaultValue: 3,
      );
      expect(fb, 3);
      expect(captured, isNotNull);
    });

    test('getParsed returns default when value is empty string', () async {
      final backend = InMemoryRemoteConfigBackend();
      await backend.initialize(
        const RemoteConfigOptions(defaults: {'empty': ''}),
      );
      final rc = await RemoteConfiguration.init(
        options: const RemoteConfigOptions(),
        backend: backend,
      );

      final result = rc.getParsed<int>(
        'empty',
        (raw) => int.parse(raw),
        defaultValue: 42,
      );
      expect(result, 42);
    });

    test('getParsed returns default when key does not exist', () async {
      final rc = await RemoteConfiguration.mock({});

      final result = rc.getParsed<int>(
        'nonexistent',
        (raw) => int.parse(raw),
        defaultValue: 99,
      );
      expect(result, 99);
    });

    test('getDouble handles int values', () async {
      final rc = await RemoteConfiguration.mock({'int_value': 42});

      // getDouble should convert int to double
      expect(rc.getDouble('int_value', defaultValue: 0.0), 42.0);
    });
  });
}
