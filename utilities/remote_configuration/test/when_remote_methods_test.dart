import 'package:flutter_test/flutter_test.dart';
import 'package:remote_configuration/remote_configuration.dart';

void main() {
  group('RemoteConfiguration getWhenRemote methods', () {
    test(
      'getStringWhenRemote throws UnsupportedError with mock backend',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 'value'});

        expect(
          () => rc.getStringWhenRemote('key'),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getIntWhenRemote throws UnsupportedError with mock backend',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 42});

        expect(
          () => rc.getIntWhenRemote('key'),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getDoubleWhenRemote throws UnsupportedError with mock backend',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 3.14});

        expect(
          () => rc.getDoubleWhenRemote('key'),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getBoolWhenRemote throws UnsupportedError with mock backend',
      () async {
        final rc = await RemoteConfiguration.mock({'key': true});

        expect(
          () => rc.getBoolWhenRemote('key'),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getJsonWhenRemote throws UnsupportedError with mock backend',
      () async {
        final rc = await RemoteConfiguration.mock({
          'key': {'a': 1},
        });

        expect(
          () => rc.getJsonWhenRemote('key'),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getWhenRemote methods throw UnsupportedError with timeout parameter',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 'value'});

        expect(
          () => rc.getStringWhenRemote(
            'key',
            timeout: const Duration(seconds: 5),
          ),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getWhenRemote methods throw UnsupportedError with waitForRealtime parameter',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 'value'});

        expect(
          () => rc.getStringWhenRemote('key', waitForRealtime: false),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'getWhenRemote methods throw UnsupportedError with all parameters',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 'value'});

        expect(
          () => rc.getStringWhenRemote(
            'key',
            timeout: const Duration(seconds: 5),
            waitForRealtime: true,
          ),
          throwsA(isA<UnsupportedError>()),
        );
      },
    );

    test(
      'UnsupportedError message mentions FirebaseRemoteConfigBackend',
      () async {
        final rc = await RemoteConfiguration.mock({'key': 'value'});

        try {
          await rc.getStringWhenRemote('key');
          fail('Expected UnsupportedError to be thrown');
        } catch (e) {
          expect(e, isA<UnsupportedError>());
          expect(e.toString(), contains('FirebaseRemoteConfigBackend'));
        }
      },
    );

    test('getWhenRemote methods work with key prefix', () async {
      final rc = await RemoteConfiguration.mock({'prefix__key': 'value'});

      // Even with prefix, should throw UnsupportedError with mock backend
      expect(
        () => rc.getStringWhenRemote('key'),
        throwsA(isA<UnsupportedError>()),
      );
    });

    test('all getWhenRemote methods throw for same reason', () async {
      final rc = await RemoteConfiguration.mock({
        'str': 'value',
        'num': 42,
        'dbl': 3.14,
        'bool': true,
        'json': {'a': 1},
      });

      // All methods should throw UnsupportedError
      expect(
        () => rc.getStringWhenRemote('str'),
        throwsA(isA<UnsupportedError>()),
      );
      expect(
        () => rc.getIntWhenRemote('num'),
        throwsA(isA<UnsupportedError>()),
      );
      expect(
        () => rc.getDoubleWhenRemote('dbl'),
        throwsA(isA<UnsupportedError>()),
      );
      expect(
        () => rc.getBoolWhenRemote('bool'),
        throwsA(isA<UnsupportedError>()),
      );
      expect(
        () => rc.getJsonWhenRemote('json'),
        throwsA(isA<UnsupportedError>()),
      );
    });
  });
}
