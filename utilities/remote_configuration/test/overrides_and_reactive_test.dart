import 'package:async/async.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:remote_configuration/remote_configuration.dart';

void main() {
  group('Overrides and reactive updates', () {
    test('setOverride and clearOverride affect getter results', () async {
      final rc = await RemoteConfiguration.mock({'flag': false});

      expect(rc.getBool('flag', defaultValue: false), isFalse);
      rc.setOverride('flag', true);
      expect(rc.getBool('flag', defaultValue: false), isTrue);
      rc.clearOverride('flag');
      expect(rc.getBool('flag', defaultValue: false), isFalse);
    });

    test('clearAllOverrides removes all overrides', () async {
      final rc = await RemoteConfiguration.mock({
        'key1': 'original1',
        'key2': 'original2',
        'key3': 'original3',
      });

      // Set multiple overrides
      rc.setOverride('key1', 'override1');
      rc.setOverride('key2', 'override2');
      rc.setOverride('key3', 'override3');

      expect(rc.getString('key1', defaultValue: ''), 'override1');
      expect(rc.getString('key2', defaultValue: ''), 'override2');
      expect(rc.getString('key3', defaultValue: ''), 'override3');

      // Clear all overrides
      rc.clearAllOverrides();

      expect(rc.getString('key1', defaultValue: ''), 'original1');
      expect(rc.getString('key2', defaultValue: ''), 'original2');
      expect(rc.getString('key3', defaultValue: ''), 'original3');
    });

    test('setOverride works with different types', () async {
      final rc = await RemoteConfiguration.mock({});

      rc.setOverride('str', 'hello');
      rc.setOverride('int', 42);
      rc.setOverride('double', 3.14);
      rc.setOverride('bool', true);
      rc.setOverride('json', {'key': 'value'});

      expect(rc.getString('str', defaultValue: ''), 'hello');
      expect(rc.getInt('int', defaultValue: 0), 42);
      expect(rc.getDouble('double', defaultValue: 0.0), 3.14);
      expect(rc.getBool('bool', defaultValue: false), isTrue);
      expect(rc.getJson('json', defaultValue: const {}), {'key': 'value'});
    });

    test('watchKey emits initial and subsequent values', () async {
      final rc = await RemoteConfiguration.mock({'flag': false});
      final q = StreamQueue(rc.watchKey<bool>('flag', defaultValue: false));

      final first = await q.next;
      expect(first, isFalse);
      rc.setOverride('flag', true);
      final second = await q.next;
      expect(second, isTrue);
      await q.cancel();
    });

    testWidgets('ValueListenable from listenableKey rebuilds on change', (
      tester,
    ) async {
      final rc = await RemoteConfiguration.mock({'flag': false});

      final listenable = rc.listenableKey<bool>('flag', defaultValue: false);

      await tester.pumpWidget(
        ListenableBuilder(
          listenable: listenable,
          builder:
              (ctx, _) => Text(
                listenable.value ? 'ON' : 'OFF',
                textDirection: TextDirection.ltr,
              ),
        ),
      );

      expect(find.text('OFF'), findsOneWidget);
      rc.setOverride('flag', true);
      await tester.pumpAndSettle();
      expect(find.text('ON'), findsOneWidget);
    });
  });
}
