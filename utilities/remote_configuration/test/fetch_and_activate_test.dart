import 'dart:async';

import 'package:flutter_test/flutter_test.dart';
import 'package:remote_configuration/src/backend/remote_config_backend.dart';
import 'package:remote_configuration/remote_configuration.dart';

class _FakeBackend implements RemoteConfigBackend {
  final _controller = StreamController<void>.broadcast();
  bool _fetchChanged = false;
  final Map<String, Object?> _values = {};

  @override
  Future<void> initialize(RemoteConfigOptions options) async {
    _values.addAll(options.defaults);
  }

  @override
  Future<void> fetch() async {
    // simulate fetching - no immediate effect
  }

  @override
  Future<bool> activate() async {
    final changed = _fetchChanged;
    _fetchChanged = false;
    if (changed) _controller.add(null);
    return changed;
  }

  @override
  Future<bool> fetchAndActivate({bool force = false}) async {
    final changed = _fetchChanged;
    _fetchChanged = false;
    if (changed) _controller.add(null);
    return changed;
  }

  void setFetchChanged() => _fetchChanged = true;

  @override
  bool? getBool(String key) => _values[key] as bool?;
  @override
  double? getDouble(String key) {
    final v = _values[key];
    if (v is double) return v;
    if (v is num) return v.toDouble();
    return null;
  }

  @override
  int? getInt(String key) => _values[key] as int?;
  @override
  Map<String, dynamic>? getJson(String key) =>
      _values[key] as Map<String, dynamic>?;
  @override
  String? getString(String key) => _values[key] as String?;

  @override
  Stream<void> get onUpdated => _controller.stream;
}

void main() {
  group('fetchAndActivate', () {
    test('merges concurrent fetches and emits update when changed', () async {
      final backend = _FakeBackend();
      final rc = await RemoteConfiguration.init(
        backend: backend,
        options: const RemoteConfigOptions(defaults: {'flag': false}),
      );

      int updates = 0;
      final sub = rc.updates.listen((_) => updates++);

      backend.setFetchChanged();
      final f1 = rc.fetchAndActivate();
      final f2 = rc.fetchAndActivate();
      final results = await Future.wait([f1, f2]);

      expect(results, [true, true]);
      // The backend emits once on fetch; the facade also emits when changed
      // Depending on timing this can be 1 or 2; assert at least once.
      await Future<void>.delayed(const Duration(milliseconds: 10));
      expect(updates >= 1, isTrue);
      await sub.cancel();
    });

    test('fetch() completes without error', () async {
      final rc = await RemoteConfiguration.mock({'key': 'value'});
      await rc.fetch();
    });

    test('activate() returns bool', () async {
      final rc = await RemoteConfiguration.mock({'key': 'value'});
      final result = await rc.activate();
      expect(result, isA<bool>());
    });

    test('fetchAndActivate() with force=true', () async {
      final rc = await RemoteConfiguration.mock({'key': 'value'});
      final result = await rc.fetchAndActivate(force: true);
      expect(result, isA<bool>());
    });

    test('fetchAndActivate() with force=false', () async {
      final rc = await RemoteConfiguration.mock({'key': 'value'});
      final result = await rc.fetchAndActivate(force: false);
      expect(result, isA<bool>());
    });
  });
}
