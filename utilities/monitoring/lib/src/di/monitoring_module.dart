import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:logger/logger.dart' as log;
import 'package:monitoring/src/logger/log_console_output.dart';
import 'package:monitoring/src/logger/logger.dart';
import 'package:monitoring/src/logger/logger_base.dart';
import 'package:monitoring/src/logger/logger_test_filter.dart';
import 'package:monitoring/src/logger/sentry_log_output.dart';
import 'package:monitoring/src/reporter/composite_reporter.dart';
import 'package:monitoring/src/reporter/measure_reporter.dart';
import 'package:monitoring/src/reporter/reporter_base.dart';
import 'package:monitoring/src/reporter/sentry_reporter.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

@module
abstract class MonitoringModule {
  @Singleton(as: ReporterBase)
  CompositeReporter reporter() => CompositeReporter([
    SentryReporter(),
    MeasureReporter(),
  ]);

  @Singleton(as: log.LogFilter)
  LoggerTestFilter get testFilter => LoggerTestFilter();

  @Singleton(as: log.LogOutput)
  SentryLogOutput sentryLogOutput(ReporterBase reporter) =>
      SentryLogOutput(reporter);

  @singleton
  PrettyDioLogger get dioLogger => PrettyDioLogger(
    requestHeader: true,
    requestBody: true,
    logPrint: (o) => debugPrint(o.toString()),
    enabled: kDebugMode,
  );

  @singleton
  log.Logger logger(log.LogOutput logOutput, log.LogFilter logFilter) =>
      log.Logger(
        filter: logFilter,
        printer: log.PrettyPrinter(noBoxingByDefault: true, methodCount: 0),
        output: kReleaseMode ? logOutput : LogConsoleOutput(),
      );

  @singleton
  LoggerBase loggerBase(log.Logger logger) => Logger(logger);
}
