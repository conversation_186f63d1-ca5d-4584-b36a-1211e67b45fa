import 'package:flutter/widgets.dart';
import 'package:measure_flutter/measure_flutter.dart';

/// Navigation observer for measure.sh that automatically tracks screen views.
/// This is a wrapper around MsrNavigatorObserver from the measure_flutter package.
class MeasureNavigationObserver extends NavigatorObserver {
  final MsrNavigatorObserver _measureObserver = MsrNavigatorObserver();

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _measureObserver.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _measureObserver.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    _measureObserver.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _measureObserver.didRemove(route, previousRoute);
  }
}
