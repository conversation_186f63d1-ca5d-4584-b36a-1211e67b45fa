import 'dart:developer';
import 'dart:io' show Platform;

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:measure_flutter/measure_flutter.dart';
import 'package:monitoring/src/reporter/reporter_base.dart';

class MeasureReporter extends ReporterBase {
  @override
  Future<void> start({Widget? app, Map<String, dynamic>? extraData}) async {
    assert(app != null);

    final env = extraData?["env"] as String;
    final iosApiKey = extraData?["measure_ios_api_key"] as String?;
    final androidApiKey = extraData?["measure_android_api_key"] as String?;
    final apiUrl =
        extraData?["measure_api_url"] as String? ?? "https://ingest.measure.sh";

    // Determine which API key to use based on platform
    String? apiKey;
    if (Platform.isIOS && iosApiKey != null && iosApiKey.isNotEmpty) {
      apiKey = iosApiKey;
    } else if (Platform.isAndroid &&
        androidApiKey != null &&
        androidApiKey.isNotEmpty) {
      apiKey = androidApiKey;
    }

    if (apiKey == null || apiKey.isEmpty) {
      log(
        "Measure.sh: API key is empty for current platform, skipping initialization",
      );
      // Only run the app if it's provided (first reporter in composite)
      if (app != null) {
        runApp(app);
      }
      return;
    }

    log("Measure.sh: Initializing with API key: ${apiKey.substring(0, 10)}...");

    try {
      if (app != null) {
        await Measure.instance.init(
          () => runApp(MeasureWidget(child: app)),
          config: MeasureConfig(
            enableLogging: !kReleaseMode,
            traceSamplingRate: env == 'prod' ? 0.2 : 1.0,
            samplingRateForErrorFreeSessions: env == 'prod' ? 0.1 : 1.0,
          ),
          clientInfo: ClientInfo(apiKey: apiKey, apiUrl: apiUrl),
        );
      } else {
        // Initialize without running the app (for composite reporter)
        await Measure.instance.init(
          () => {},
          config: MeasureConfig(
            enableLogging: !kReleaseMode,
            traceSamplingRate: env == 'prod' ? 0.2 : 1.0,
            samplingRateForErrorFreeSessions: env == 'prod' ? 0.1 : 1.0,
          ),
          clientInfo: ClientInfo(apiKey: apiKey, apiUrl: apiUrl),
        );
      }
      log("Measure.sh: Successfully initialized");
    } catch (e) {
      log("Measure.sh: Failed to initialize: $e");
      // Fallback to running the app without measure.sh if app is provided
      if (app != null) {
        runApp(app);
      }
    }
  }

  @override
  void onError(FlutterErrorDetails errorDetails) {
    try {
      // Measure.sh automatically captures crashes, but we can also manually report
      // if needed in the future
    } catch (e) {
      log("Measure.sh: Failed to report error: $e");
    }
  }

  @override
  Future<void> recordBreadcrumb(
    String name, {
    Map<String, dynamic>? eventAttributes,
  }) async {
    try {
      // Measure.sh automatically tracks navigation and user interactions
      // Custom breadcrumbs can be implemented using custom events if needed
    } catch (e) {
      log("Measure.sh: Failed to record breadcrumb: $e");
    }
  }

  @override
  Future<bool> recordCustomEvent(
    String eventType, {
    String eventName = "",
    Map<String, dynamic>? eventAttributes,
  }) async {
    try {
      // Measure.sh supports custom events through their API
      // This can be implemented when the feature becomes available
      return true;
    } catch (e) {
      log("Measure.sh: Failed to record custom event: $e");
      return false;
    }
  }

  @override
  void recordError(
    Object error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? attributes,
    bool isFatal = false,
  }) {
    try {
      // Measure.sh automatically captures crashes and exceptions
      // Manual error reporting can be added here if needed
    } catch (e) {
      log("Measure.sh: Failed to record error: $e");
    }
  }

  @override
  Future<bool?> setUserId(String userId) async {
    try {
      // Measure.sh user identification can be implemented here
      // when the feature becomes available in the SDK
      return true;
    } catch (e) {
      log("Measure.sh: Failed to set user ID: $e");
      return false;
    }
  }

  @override
  Future<bool> setAttribute(String name, Object? value) async {
    try {
      // Measure.sh custom attributes can be implemented here
      // when the feature becomes available in the SDK
      return true;
    } catch (e) {
      log("Measure.sh: Failed to set attribute: $e");
      return false;
    }
  }

  @override
  void setProduct(String productName) {
    try {
      // Measure.sh product setting can be implemented here
      // when the feature becomes available in the SDK
    } catch (e) {
      log("Measure.sh: Failed to set product: $e");
    }
  }
}
