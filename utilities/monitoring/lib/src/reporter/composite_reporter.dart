import 'dart:developer';

import 'package:flutter/widgets.dart';
import 'package:monitoring/src/reporter/reporter_base.dart';

/// A composite reporter that delegates to multiple reporters.
/// This allows us to use multiple monitoring tools (e.g., Sentry + Measure.sh) simultaneously.
class CompositeReporter extends ReporterBase {
  final List<ReporterBase> _reporters;

  CompositeReporter(this._reporters);

  @override
  Future<void> start({Widget? app, Map<String, dynamic>? extraData}) async {
    if (_reporters.isEmpty) {
      log("CompositeReporter: No reporters configured, running app directly");
      if (app != null) {
        runApp(app);
      }
      return;
    }

    // Start all reporters, but only the first one should run the app
    // Others should handle their initialization without running the app
    for (int i = 0; i < _reporters.length; i++) {
      try {
        if (i == 0) {
          // First reporter runs the app
          await _reporters[i].start(app: app, extraData: extraData);
        } else {
          // Other reporters initialize without running the app
          await _reporters[i].start(extraData: extraData);
        }
      } catch (e) {
        log("CompositeReporter: Failed to start reporter ${_reporters[i].runtimeType}: $e");
        // Continue with other reporters even if one fails
      }
    }
  }

  @override
  void onError(FlutterErrorDetails errorDetails) {
    for (final reporter in _reporters) {
      try {
        reporter.onError(errorDetails);
      } catch (e) {
        log("CompositeReporter: Failed to report error to ${reporter.runtimeType}: $e");
      }
    }
  }

  @override
  Future<void> recordBreadcrumb(
    String name, {
    Map<String, dynamic>? eventAttributes,
  }) async {
    await Future.wait(
      _reporters.map((reporter) async {
        try {
          await reporter.recordBreadcrumb(name, eventAttributes: eventAttributes);
        } catch (e) {
          log("CompositeReporter: Failed to record breadcrumb to ${reporter.runtimeType}: $e");
        }
      }),
    );
  }

  @override
  Future<bool> recordCustomEvent(
    String eventType, {
    String eventName = "",
    Map<String, dynamic>? eventAttributes,
  }) async {
    bool anySuccess = false;
    await Future.wait(
      _reporters.map((reporter) async {
        try {
          final success = await reporter.recordCustomEvent(
            eventType,
            eventName: eventName,
            eventAttributes: eventAttributes,
          );
          if (success) anySuccess = true;
        } catch (e) {
          log("CompositeReporter: Failed to record custom event to ${reporter.runtimeType}: $e");
        }
      }),
    );
    return anySuccess;
  }

  @override
  void recordError(
    Object error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? attributes,
    bool isFatal = false,
  }) {
    for (final reporter in _reporters) {
      try {
        reporter.recordError(
          error,
          stackTrace,
          attributes: attributes,
          isFatal: isFatal,
        );
      } catch (e) {
        log("CompositeReporter: Failed to record error to ${reporter.runtimeType}: $e");
      }
    }
  }

  @override
  Future<bool?> setUserId(String userId) async {
    bool? anySuccess;
    await Future.wait(
      _reporters.map((reporter) async {
        try {
          final success = await reporter.setUserId(userId);
          if (success == true) anySuccess = true;
        } catch (e) {
          log("CompositeReporter: Failed to set user ID to ${reporter.runtimeType}: $e");
        }
      }),
    );
    return anySuccess;
  }

  @override
  Future<bool> setAttribute(String name, Object? value) async {
    bool anySuccess = false;
    await Future.wait(
      _reporters.map((reporter) async {
        try {
          final success = await reporter.setAttribute(name, value);
          if (success) anySuccess = true;
        } catch (e) {
          log("CompositeReporter: Failed to set attribute to ${reporter.runtimeType}: $e");
        }
      }),
    );
    return anySuccess;
  }

  @override
  void setProduct(String productName) {
    for (final reporter in _reporters) {
      try {
        reporter.setProduct(productName);
      } catch (e) {
        log("CompositeReporter: Failed to set product to ${reporter.runtimeType}: $e");
      }
    }
  }
}
