{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "equiti_platform (dev Debug mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "args": [
        "--flavor",
        "dev",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.dev.json"
      ],
      "program": "lib/main_dev.dart"
    },
    {
      "name": "equiti_platform (rel Debug mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "args": [
        "--flavor",
        "rel",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.rel.json"
      ],
      "program": "lib/main_rel.dart"
    },
    {
      "name": "equiti_platform (dev Profile mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "args": [
        "--flavor",
        "dev",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.dev.json"
      ],
      "program": "lib/main_dev.dart"
    },
    {
      "name": "equiti_platform (rel Profile mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "args": [
        "--flavor",
        "rel",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.rel.json"
      ],
      "program": "lib/main_rel.dart"
    },
    {
      "name": "equiti_platform (stg Debug mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "args": [
        "--flavor",
        "stg",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.stg.json"
      ],
      "program": "lib/main_stg.dart"
    },
    {
      "name": "equiti_platform (stg Profile mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "args": [
        "--flavor",
        "stg",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.stg.json"
      ],
      "program": "lib/main_stg.dart"
    },
    {
      "name": "equiti_platform (prod Debug mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "args": [
        "--flavor",
        "prod",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.prod.json"
      ],
      "program": "lib/main_prod.dart"
    },
    {
      "name": "equiti_platform (prod Profile mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "args": [
        "--flavor",
        "prod",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.prod.json"
      ],
      "program": "lib/main_prod.dart"
    },
    {
      "name": "equiti_platform (prod Release mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "args": [
        "--flavor",
        "prod",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.prod.json"
      ],
      "program": "lib/main_prod.dart"
    },
    {
      "name": "equiti_platform (rel Release mode)",
      "cwd": "app/equiti_platform",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "args": [
        "--flavor",
        "rel",
        "--dart-define-from-file",
        "${workspaceFolder}/.env/config.rel.json"
      ],
      "program": "lib/main_rel.dart"
    },
    {
      "name": "trader",
      "cwd": "app/product_apps/trader",
      "request": "launch",
      "type": "dart",
      "program": "${workspaceFolder}/app/product_apps/trader/lib/main_trader.dart"
    },
    {
      "name": "trader (profile mode)",
      "cwd": "app/product_apps/trader",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "program": "${workspaceFolder}/app/product_apps/trader/lib/main_trader.dart"
    },
    {
      "name": "trader (release mode)",
      "cwd": "app/product_apps/trader",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "${workspaceFolder}/app/product_apps/trader/lib/main_trader.dart"
    },
    {
      "name": "playground",
      "cwd": "app/playground",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "${workspaceFolder}/app/playground/lib/main_playground.dart"
    },
    {
      "name": "Update goldens",
      "type": "dart",
      "request": "launch",
      "args": ["--update-goldens"], 
      "codeLens": {
        "for": [ "run-test", "run-test-file"],
        "title": "Update goldens"
      },
    },
  ]
}
