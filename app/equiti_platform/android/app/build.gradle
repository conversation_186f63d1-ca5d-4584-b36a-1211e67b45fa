plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.newrelic.agent.android"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def dartEnvironmentVariables = [];
if (project.hasProperty('dart-defines')) {
    dartEnvironmentVariables = project.property('dart-defines')
    .split(',')
    .collectEntries { entry ->
        def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
        [(pair.first()): pair.last()]
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// Read Auth0 configuration from Dart-defined environment variables
def auth0Domain = dartEnvironmentVariables.AUTH_CONFIG_DOMAIN
def auth0Scheme = dartEnvironmentVariables.AUTH_CONFIG_SCHEME

// Read UAE Pass configuration from Dart-defined environment variables
def uaePassClientId = dartEnvironmentVariables.UAE_PASS_CLIENT_ID ?: "sandbox_stage"
def uaePassClientSecret = dartEnvironmentVariables.UAE_PASS_CLIENT_SECRET ?: "sandbox_stage"
def uaePassRedirectUrl = dartEnvironmentVariables.UAE_PASS_REDIRECT_URL ?: "https://equiti-stg-uae-pass//-callback.equiti.me.uk/uaepass/callback"
def uaePassEnvironment = dartEnvironmentVariables.UAE_PASS_ENVIRONMENT ?: 1 // 0=QA, 1=Staging, 2=Production
// Read New Relic token from Dart-defined environment variables
def newRelicToken = dartEnvironmentVariables.NEW_RELIC_ANDROID_TOKEN

android {

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    flavorDimensions += "environment"

    productFlavors {
        dev {
            dimension "environment"
            applicationId "com.equiti.trader.app.development"
            resValue "string", "app_name", "Equiti Dev"
            signingConfig signingConfigs.debug
            manifestPlaceholders += [
                auth0Domain: auth0Domain,
                auth0Scheme: auth0Scheme,
                uaePassScheme: "equiti",
                uaePassHostSuccess: "uaepasssuccess",
                uaePassHostFailure: "uaepassfailed"
            ]
        }
        stg {
            dimension "environment"
            applicationId "com.equiti.trader.app.staging"
            resValue "string", "app_name", "Equiti Stg"
            signingConfig signingConfigs.debug
            manifestPlaceholders += [
                auth0Domain: auth0Domain,
                auth0Scheme: auth0Scheme,
                uaePassScheme: "equiti",
                uaePassHostSuccess: "uaepasssuccess",
                uaePassHostFailure: "uaepassfailed"
            ]
        }
        prod {
            dimension "environment"
            applicationId "com.equiti.app"
            resValue "string", "app_name", "Equiti"
            signingConfig signingConfigs.getByName("release")
            manifestPlaceholders += [
                auth0Domain: auth0Domain,
                auth0Scheme: auth0Scheme,
                uaePassScheme: "equiti",
                uaePassHostSuccess: "uaepasssuccess",
                uaePassHostFailure: "uaepassfailed"
            ]
        }
        rel {
            dimension "environment"
            applicationId "com.equiti.trader.app.release"
            resValue "string", "app_name", "Equiti Rel"
            signingConfig signingConfigs.debug
            manifestPlaceholders += [
                auth0Domain: auth0Domain,
                auth0Scheme: auth0Scheme,
                uaePassScheme: "equiti",
                uaePassHostSuccess: "uaepasssuccess",
                uaePassHostFailure: "uaepassfailed"
            ]
        }
    }
    namespace "com.equiti.app"
    compileSdk 36
    ndkVersion "29.0.13113456 rc1"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    buildFeatures {
        buildConfig true
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.equiti.trader.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 28
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // UAE Pass configuration
        buildConfigField "String", "UAE_PASS_CLIENT_ID", "\"${uaePassClientId}\""
        buildConfigField "String", "UAE_PASS_CLIENT_SECRET", "\"${uaePassClientSecret}\""
        buildConfigField "String", "UAE_PASS_REDIRECT_URL", "\"${uaePassRedirectUrl}\""
        buildConfigField "String", "UAE_PASS_SCHEME", "\"equiti\""
        buildConfigField "String", "UAE_PASS_HOST_SUCCESS", "\"uaepasssuccess\""
        buildConfigField "String", "UAE_PASS_HOST_FAILURE", "\"uaepassfailed\""
        buildConfigField "Integer", "UAE_PASS_ENVIRONMENT", "${uaePassEnvironment}"
    }


    buildTypes {
        release {
            // (Flutter’s template already turns on minify, but it’s clearer to be explicit)
            minifyEnabled true
            shrinkResources true

            // ℹ️  First the default Android rules, then your additions
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'),
                           'proguard-rules.pro'
        }
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
    }
}

flutter {
    source '../..'
}

// Task to create newrelic.properties file with actual token content
task createNewRelicProperties {
    doLast {
        def newrelicPropertiesFile = file('newrelic.properties')

        def content = """# New Relic Android Agent Configuration
# This file is automatically generated during build time
# The token is provided from the NEW_RELIC_ANDROID_TOKEN environment variable

com.newrelic.application_token=${newRelicToken}
"""

        // Create the file with the actual token content
        newrelicPropertiesFile.text = content
        println "Created newrelic.properties with token: ${newRelicToken.take(10)}..."
    }
}

// Ensure the New Relic properties are created before the build
preBuild.dependsOn createNewRelicProperties

dependencies {
    // UAE Pass library
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')

    // Required dependencies for UAE Pass
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:converter-scalars:2.9.0'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.loopj.android:android-async-http:1.4.11'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // Flavor-specific Google Pay dependencies
    devImplementation "com.equitipay.gpay:equitipay-gpay:0.2.3-test"
    stgImplementation "com.equitipay.gpay:equitipay-gpay:0.2.3-test"
    relImplementation "com.equitipay.gpay:equitipay-gpay:0.2.3-test"
    prodImplementation "com.equitipay.gpay:equitipay-gpay:0.2.3-prod"
}