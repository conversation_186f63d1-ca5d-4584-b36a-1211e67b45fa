<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSCalendarsUsageDescription</key>
	<string>This app requires access to your calendar to add events.</string>
	<key>NSContactsUsageDescription</key>
	<string>Access contacts for event attendee editing.</string>
	<key>NSCalendarsFullAccessUsageDescription</key>
	<string>Access most functions for calendar viewing and editing.</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>$(LAUNCH_SCREEN_STORYBOARD)</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to the photo library to upload profile pictures and documents for verification purposes.</string>
	<key>NSCameraUsageDescription</key>
	<string>This app requires camera access to scan documents and take profile pictures for verification purposes.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app requires location access for compliance and security verification purposes.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires microphone access for audio recording during verification processes.</string>
	<key>LSSupportsOpeningDocumentsInPlace</key><true/>
	<key>UIFileSharingEnabled</key><true/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>uaepassdev</string>
		<string>uaepassstg</string>
		<string>uaepass</string>
	</array>
  <key>FlutterDeepLinkingEnabled</key>
    <false/>
	<!-- Universal Links and Custom URL Schemes -->
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:equiti.com</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>equiti.com</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(IOS_BUNDLE_URL_SCHEME)</string>
			</array>
		</dict>
	</array>
	<!-- Measure.sh configuration -->
	<key>MeasureApiKey</key>
	<string>$(MEASURE_IOS_API_KEY)</string>
	<key>MeasureApiUrl</key>
	<string>https://ingest.measure.sh</string>
</dict>
</plist>
