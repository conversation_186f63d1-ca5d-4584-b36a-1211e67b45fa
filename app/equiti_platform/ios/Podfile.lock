PODS:
  - Alamofire (5.4.4)
  - app_links (0.0.2):
    - Flutter
  - appsflyer_sdk (6.17.3):
    - appsflyer_sdk/Core (= 6.17.3)
  - appsflyer_sdk/Core (6.17.3):
    - AppsFlyerFramework (= 6.17.5)
    - Flutter
  - AppsFlyerFramework (6.17.5):
    - AppsFlyerFramework/Main (= 6.17.5)
  - AppsFlyerFramework/Main (6.17.5)
  - audio_session (0.0.1):
    - Flutter
  - Auth0 (2.10.0):
    - JWTDecode (= 3.2.0)
    - SimpleKeychain (= 1.2.0)
  - auth0_flutter (1.10.0):
    - Auth0 (= 2.10.0)
    - Flutter
    - FlutterMacOS
    - JWTDecode (= 3.2.0)
    - SimpleKeychain (= 1.2.0)
  - cupertino_http (0.0.1):
    - Flutter
  - customer_support_chat (0.0.1):
    - Flutter
    - Messaging-InApp-UI (= 1.9.1)
  - device_calendar (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - Firebase/Messaging (12.0.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 12.0.0)
  - Firebase/RemoteConfig (12.0.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 12.0.0)
  - firebase_core (4.0.0):
    - Firebase/CoreOnly (= 12.0.0)
    - Flutter
  - firebase_messaging (16.0.0):
    - Firebase/Messaging (= 12.0.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (6.0.0):
    - Firebase/RemoteConfig (= 12.0.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - FirebaseCore (12.0.0):
    - FirebaseCoreInternal (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (12.0.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (12.0.0):
    - FirebaseABTesting (~> 12.0.0)
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - FirebaseRemoteConfigInterop (~> 12.0.0)
    - FirebaseSharedSwift (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseRemoteConfigInterop (12.0.0)
  - FirebaseSharedSwift (12.0.0)
  - Flutter (1.0.0)
  - flutter_email_sender (0.0.1):
    - Flutter
  - flutter_idensic_mobile_sdk_plugin (1.37.1):
    - Flutter
    - IdensicMobileSDK (= 1.37.1)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_libphonenumber_ios (1.1.4):
    - Flutter
    - PhoneNumberKit/PhoneNumberKitCore (= 3.8.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - IdensicMobileSDK (1.37.1):
    - IdensicMobileSDK/Default (= 1.37.1)
  - IdensicMobileSDK/Core (1.37.1)
  - IdensicMobileSDK/Default (1.37.1):
    - IdensicMobileSDK/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - JWTDecode (3.2.0)
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - Messaging-InApp-Core (1.9.1)
  - Messaging-InApp-UI (1.9.1):
    - Messaging-InApp-Core (= 1.9.1)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - newrelic_mobile (1.1.19):
    - Flutter
    - NewRelicAgent (~> 7.6.0)
  - NewRelicAgent (7.6.0)
  - open_file_ios (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PhoneNumberKit/PhoneNumberKitCore (3.8.0)
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.3):
    - SDWebImage/Core (= 5.21.3)
  - SDWebImage/Core (5.21.3)
  - Sentry/HybridSDK (8.52.1)
  - sentry_flutter (9.6.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.52.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SimpleKeychain (1.2.0)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - UAEPassClient (1.5):
    - Alamofire (= 5.4.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Alamofire (~> 5.4.4)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - auth0_flutter (from `.symlinks/plugins/auth0_flutter/darwin`)
  - cupertino_http (from `.symlinks/plugins/cupertino_http/ios`)
  - customer_support_chat (from `.symlinks/plugins/customer_support_chat/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_email_sender (from `.symlinks/plugins/flutter_email_sender/ios`)
  - flutter_idensic_mobile_sdk_plugin (from `.symlinks/plugins/flutter_idensic_mobile_sdk_plugin/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_libphonenumber_ios (from `.symlinks/plugins/flutter_libphonenumber_ios/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - newrelic_mobile (from `.symlinks/plugins/newrelic_mobile/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - UAEPassClient (from `LocalPods/UAEPassClient`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://github.com/SumSubstance/Specs.git:
    - IdensicMobileSDK
  trunk:
    - Alamofire
    - AppsFlyerFramework
    - Auth0
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseABTesting
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSharedSwift
    - GoogleDataTransport
    - GoogleUtilities
    - JWTDecode
    - Messaging-InApp-Core
    - Messaging-InApp-UI
    - nanopb
    - NewRelicAgent
    - OrderedSet
    - PhoneNumberKit
    - PromisesObjC
    - SDWebImage
    - Sentry
    - SimpleKeychain
    - SwiftyGif

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  auth0_flutter:
    :path: ".symlinks/plugins/auth0_flutter/darwin"
  cupertino_http:
    :path: ".symlinks/plugins/cupertino_http/ios"
  customer_support_chat:
    :path: ".symlinks/plugins/customer_support_chat/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_email_sender:
    :path: ".symlinks/plugins/flutter_email_sender/ios"
  flutter_idensic_mobile_sdk_plugin:
    :path: ".symlinks/plugins/flutter_idensic_mobile_sdk_plugin/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_libphonenumber_ios:
    :path: ".symlinks/plugins/flutter_libphonenumber_ios/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  newrelic_mobile:
    :path: ".symlinks/plugins/newrelic_mobile/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  UAEPassClient:
    :path: LocalPods/UAEPassClient
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Alamofire: f3b09a368f1582ab751b3fff5460276e0d2cf5c9
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  appsflyer_sdk: 23e08a1f2a2a0c3f5d58c91a5820dba768d738c8
  AppsFlyerFramework: da52a40c07c9f81a7829952e0fd854b6eacf5a7d
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  Auth0: 2876d0c36857422eda9cb580a6cc896c7d14cb36
  auth0_flutter: 5b921a0291e9cf364ffd473f7689156df88356b3
  cupertino_http: e28016db19efdfa3df3b46961e762a20a62276e5
  customer_support_chat: 8918d12e7cf75eeffd346330d03fa18c2c4ddc58
  device_calendar: b55b2c5406cfba45c95a59f9059156daee1f74ed
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: 800d487043c0557d9faed71477a38d9aafb08a41
  firebase_core: 633e1851ffe1b9ab875f6467a4f574c79cef02e4
  firebase_messaging: d17feef781edc84ebefe62624fb384358ad96361
  firebase_remote_config: ce17e27739e9fa1a5a41a70319c5ce3f792788d4
  FirebaseABTesting: 2cad22e464cd7ef4589ae29f897bc71ff83ce83b
  FirebaseCore: 055f4ab117d5964158c833f3d5e7ec6d91648d4a
  FirebaseCoreInternal: dedc28e569a4be85f38f3d6af1070a2e12018d55
  FirebaseInstallations: d4c7c958f99c8860d7fcece786314ae790e2f988
  FirebaseMessaging: af49f8d7c0a3d2a017d9302c80946f45a7777dde
  FirebaseRemoteConfig: 4cbbe0083474359025e7bb334b9d0cff16b78d3a
  FirebaseRemoteConfigInterop: bfa0ea72ba3dc5af739777296424e46bd6f42613
  FirebaseSharedSwift: 59266c22ccfcef604d725c034c568fa666ea9bda
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_email_sender: 2397f5e84aaacfb61af569637a963e7c687858d8
  flutter_idensic_mobile_sdk_plugin: 6e08ca5c7de180aaea05af914fb4603914dc1cb4
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_libphonenumber_ios: 6bd2fac9dfb8f37c5732451fb7c9a992f1088d86
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_timezone: ee50ce7786b5fde27e2fe5375bbc8c9661ffc13f
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  IdensicMobileSDK: cb6ca7e74c1386e568815437e0c8fa071ade9c24
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  JWTDecode: 7dae24cb9bf9b608eae61e5081029ec169bb5527
  local_auth_darwin: d2e8c53ef0c4f43c646462e3415432c4dab3ae19
  Messaging-InApp-Core: d6374c08126b2992eee889e14fdf9f5d4d457338
  Messaging-InApp-UI: cebd41b3f72f214e4220d39b525f302eee215c7a
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  newrelic_mobile: ed6afc044752985a411f897a0de347a0205ea556
  NewRelicAgent: 96b6033df5e4c9aed0ffe7254430ba544edc216c
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PhoneNumberKit: ec00ab8cef5342c1dc49fadb99d23fa7e66cf0ef
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: 16309af6d214ba3f77a7c6f6fdda888cb313a50a
  Sentry: 2cbbe3592f30050c60e916c63c7f5a2fa584005e
  sentry_flutter: 9b1e3e6934cb7040ffb71383c95a35f73523180c
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SimpleKeychain: 768cf43ae778b1c21816e94dddf01bb8ee96a075
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  syncfusion_flutter_pdfviewer: 90dc48305d2e33d4aa20681d1e98ddeda891bc14
  UAEPassClient: 41ca63e22d23abbf8a1d033080a55fe1dd9af41f
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 31103eaa4e4c8ec6c39c3950d76d077ecc90e234

COCOAPODS: 1.16.2
