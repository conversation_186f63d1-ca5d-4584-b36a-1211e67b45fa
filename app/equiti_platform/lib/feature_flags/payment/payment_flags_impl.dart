import 'package:feature_flags/feature_flags.dart';
import 'package:payment/payments.dart';

final class PaymentFlagsImpl implements PaymentFlags {
  const PaymentFlagsImpl(this._flags);

  final FeatureFlagService _flags;

  @override
  bool isLeanPaymentEnabled() => _flags.get(kLeanPaymentEnabled);

  @override
  Future<bool> isLeanPaymentEnabledWhenRemote({
    Duration? timeout,
    bool waitForRealtime = true,
  }) => _flags.getWhenRemote(
    kLeanPaymentEnabled,
    timeout: timeout,
    waitForRealtime: waitForRealtime,
  );
}
