import 'package:customer_support_chat/customer_support_chat.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/config/equiti_env.dart';
import 'package:firebase/firebase_manager.dart';

class StgConfig implements AppConfig {
  const StgConfig();

  @override
  String get baseUrl => const String.fromEnvironment('BASE_URL');

  @override
  String get socketBaseUrl => const String.fromEnvironment('SOCKET_BASE_URL');

  @override
  EquitiEnv get env => EquitiEnv.staging;

  @override
  FirebaseEnvirmentEnum get firebaseEnv => FirebaseEnvirmentEnum.staging;

  @override
  String get mobileBffBaseUrl =>
      const String.fromEnvironment('MOBILE_BFF_BASE_URL');

  @override
  String get sentryDSNUrl => const String.fromEnvironment('SENTRY_DSN_URL');

  @override
  String get measureIosApiKey =>
      const String.fromEnvironment('MEASURE_IOS_API_KEY'); // TODO: Add measure.sh iOS API key to config.stg.json

  @override
  String get measureAndroidApiKey =>
      const String.fromEnvironment('MEASURE_ANDROID_API_KEY'); // TODO: Add measure.sh Android API key to config.stg.json

  @override
  String get socketBaseDemoUrl =>
      const String.fromEnvironment('SOCKET_BASE_DEMO_URL');

  @override
  String get baseDemoUrl => const String.fromEnvironment('BASE_DEMO_URL');

  @override
  String get newRelicIOSToken =>
      const String.fromEnvironment('NEW_RELIC_IOS_TOKEN');

  @override
  String get newRelicAndroidToken =>
      const String.fromEnvironment('NEW_RELIC_ANDROID_TOKEN');
  @override
  String get appsFlyerIosKey =>
      const String.fromEnvironment('APPSFLYER_IOS_KEY');
  @override
  String get appsFlyerAndroidKey =>
      const String.fromEnvironment('APPSFLYER_ANDROID_KEY');

  @override
  String get iosAppId => const String.fromEnvironment('IOS_APP_ID');

  @override
  bool get isLeanInSandbox => const bool.fromEnvironment('LEAN_IS_SANDBOX');

  @override
  AuthConfig get authConfig {
    return AuthConfig(
      domain: const String.fromEnvironment("AUTH_CONFIG_DOMAIN"),
      clientId: const String.fromEnvironment("AUTH_CONFIG_CLIENT_ID"),
      scheme: const String.fromEnvironment("AUTH_CONFIG_SCHEME"),
      useHttps: const bool.fromEnvironment("AUTH_CONFIG_USE_HTTPS"),
      loginAudience: const String.fromEnvironment("AUTH_CONFIG_LOGIN_AUDIENCE"),
      withdrawAudience: const String.fromEnvironment(
        "AUTH_CONFIG_WITHDRAW_AUDIENCE",
      ),
      profileSettingsAudience: const String.fromEnvironment(
        "AUTH_CONFIG_PROFILE_SETTINGS_AUDIENCE",
      ),
      changePasswordAudience: const String.fromEnvironment(
        "AUTH_CONFIG_CHANGE_PASSWORD_AUDIENCE",
      ),
    );
  }

  @override
  UAEPassEnvConfigs get uaePassEnvConfigs {
    const envInt = int.fromEnvironment('UAE_PASS_ENVIRONMENT', defaultValue: 0);

    UAEPassEnvironment environment;
    switch (envInt) {
      case 2:
        environment = UAEPassEnvironment.production;
        break;
      case 0:
        environment = UAEPassEnvironment.qa;
        break;
      case 1:
      default:
        environment = UAEPassEnvironment.staging;
        break;
    }

    return UAEPassEnvConfigs(
      clientId: const String.fromEnvironment('UAE_PASS_CLIENT_ID'),
      clientSecret: const String.fromEnvironment('UAE_PASS_CLIENT_SECRET'),
      redirectUri: const String.fromEnvironment('UAE_PASS_REDIRECT_URI'),
      successSchemeUrl: const String.fromEnvironment(
        'UAE_PASS_SUCCESS_SCHEME_URL',
      ),
      failSchemeUrl: const String.fromEnvironment('UAE_PASS_FAIL_SCHEME_URL'),
      environment: environment,
    );
  }

  @override
  CustomerChatSupportConfig get ccSupportConfig {
    return CustomerChatSupportConfig(
      organizationId: const String.fromEnvironment('SALESFORCE_ORG_ID'),
      developerName: const String.fromEnvironment('SALESFORCE_DEVELOPER_NAME'),
      url: const String.fromEnvironment('SALESFORCE_URL'),
    );
  }
}
