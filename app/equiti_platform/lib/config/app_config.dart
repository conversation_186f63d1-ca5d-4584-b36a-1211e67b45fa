import 'package:customer_support_chat/customer_support_chat.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_platform/config/equiti_env.dart';
import 'package:firebase/firebase_manager.dart';

abstract interface class AppConfig {
  String get baseUrl;
  String get baseDemoUrl;
  String get mobileBffBaseUrl;
  String get socketBaseUrl;
  String get socketBaseDemoUrl;
  String get sentryDSNUrl;
  String get newRelicIOSToken;
  String get newRelicAndroidToken;
  EquitiEnv get env;
  FirebaseEnvirmentEnum get firebaseEnv;
  AuthConfig get authConfig;
  UAEPassEnvConfigs get uaePassEnvConfigs;
  CustomerChatSupportConfig get ccSupportConfig;
  String get appsFlyerIosKey;
  String get appsFlyerAndroidKey;
  String get iosAppId;
  bool get isLeanInSandbox;
}
