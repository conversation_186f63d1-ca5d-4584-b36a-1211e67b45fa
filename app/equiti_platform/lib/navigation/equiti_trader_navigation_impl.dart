import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:hub/hub.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:e_trader/src/navigation/switch_account_args.dart';

class EquitiTraderNavigationImpl extends EquitiTraderNavigation {
  EquitiTraderNavigationImpl();

  @override
  void navigateToSymbols() {
    diContainer<EquitiNavigatorBase>().set([
      EquitiTraderRouteSchema.navBarRoute.label,
    ]);
  }

  @override
  void navigateToProductDetail({
    required SymbolDetailViewModel symbolDetail,
    required String accountNumber,
    TradeType? tradeDirection,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.symbolsDetailRoute.label,
      arguments: SymbolDetailArgs(
        symbolDetail: symbolDetail,
        accountNumber: accountNumber,
        tradeDirection: tradeDirection,
      ),
    );
  }

  @override
  void navigateToLogin({String? email}) {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      EquitiTraderRouteSchema.loginRoute.label,
      EquitiTraderRouteSchema.loginRoute.label,
    );
  }

  void navigateToTradingPrefrences() {
    print('Navigate to trading preferences');
  }

  @override
  void navigateToPortfolio({TradeConfirmationResult? result}) {
    diContainer<EquitiNavigatorBase>().globalData = {
      EquitiTraderRouteSchema.navBarRoute.url: result,
    };
    diContainer<EquitiNavigatorBase>().popUntil(
      EquitiTraderRouteSchema.navBarRoute.label,
    );
  }

  @override
  void navigateToPerformance({TradeConfirmationResult? result}) {
    print('Navigate to Performance');
  }

  @override
  navigateToSignUpOptions() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.signupOptionsRoute.label,
      OnboardingRouteSchema.signupOptionsRoute.label,
    );
  }

  @override
  void navigateToLoginOptions() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.loginOptionsRoute.label,
      OnboardingRouteSchema.loginOptionsRoute.label,
    );
  }

  @override
  void navigateToHub() {
    diContainer<EquitiNavigatorBase>().set([HubRouteSchema.hubRoute.label]);
  }

  @override
  void navigateToSwitchAccounts() {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      EquitiTraderRouteSchema.switchAccountRoute.label,
      EquitiTraderRouteSchema.switchAccountRoute.label,
      arguments: SwitchAccountArgs(
        accountType: AccountType.trading,
        blockPop: true,
      ),
    );
  }

  @override
  void navigateToDepositOptions({
    required DepositFlowConfig depositFlowConfig,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositOptionsRoute.label,
      arguments: depositFlowConfig,
    );
  }

  @override
  void navigateToWithdrawOptions(String popUntilRoute) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawOptionsRoute.label,
      arguments: popUntilRoute,
    );
  }

  @override
  void goToTransferFundsScreen(String originRoute) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.transferFundsScreen.label,
      arguments: originRoute,
    );
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
    required void Function() thenCallback,
  }) {
    diContainer<EquitiNavigatorBase>()
        .push(
          OnboardingRouteSchema.createAccountMainRoute.label,
          arguments: CreateAccountMainArgs(
            createAccountFlow: createAccountFlow,
          ),
        )
        .then((_) => thenCallback());
  }

  @override
  void navigateToHubSettings() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.hubSettingsRoute.label,
    );
  }

  @override
  void navigateToCreateWallet() {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.createWalletRoute.label,
    );
  }
}
