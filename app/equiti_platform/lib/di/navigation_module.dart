import 'package:e_trader/fusion.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_platform/navigation/equiti_trader_navigation_impl.dart';
import 'package:equiti_platform/navigation/hub_navigation_impl.dart';
import 'package:equiti_platform/navigation/login_navigation_impl.dart';
import 'package:equiti_platform/navigation/onboarding_navigation_impl.dart';
import 'package:equiti_platform/navigation/payment_navigation_impl.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:hub/hub.dart';
import 'package:injectable/injectable.dart';
import 'package:login/login.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:prelude/prelude.dart';

@module
abstract class NavigationModule {
  @singleton
  HubLocation hubLocation() => HubLocation();

  @singleton
  LoginRouteLocation loginRouteLocation() => LoginRouteLocation();

  @singleton
  OnboardingRouteLocation onboardingRouteLocation() =>
      OnboardingRouteLocation();
  @singleton
  PaymentRouteLocation paymentRouteLocation() => PaymentRouteLocation();

  @singleton
  EquitiTraderLocation equitiTraderLocation() => EquitiTraderLocation();

  @LazySingleton(as: HubNavigation)
  HubNavigationImpl hubNavigation() => HubNavigationImpl();

  @LazySingleton(as: EquitiTraderNavigation)
  EquitiTraderNavigationImpl withdrawNavigation() =>
      EquitiTraderNavigationImpl();

  @LazySingleton(as: LoginNavigation)
  LoginNavigationImpl loginNavigation() => LoginNavigationImpl();

  @LazySingleton(as: OnboardingNavigation)
  OnboardingNavigationImpl onboardingNavigationImpl() =>
      OnboardingNavigationImpl();

  @LazySingleton(as: PaymentNavigation)
  PaymentNavigationImpl paymentNavigationImpl() => PaymentNavigationImpl();

  @singleton
  PerformanceRouteObserver performanceRouteObserver() =>
      PerformanceRouteObserver();

  @singleton
  NewRelicNavigationObserver get newRelicNavObserver =>
      NewRelicNavigationObserver();

  @singleton
  MeasureNavigationObserver get measureNavObserver =>
      MeasureNavigationObserver();

  @lazySingleton
  EquitiNavigatorBase equitiPlatformNavigator(
    HubLocation hubLocation,
    LoginRouteLocation loginRouteLocation,
    EquitiTraderLocation equitiTraderLocation,
    OnboardingRouteLocation onboardingRouteLocation,
    PaymentRouteLocation paymentRouteLocation,
    PerformanceRouteObserver performanceRouteObserver,
    NewRelicNavigationObserver newRelicNavigationObserver,
    MeasureNavigationObserver measureNavigationObserver,
  ) => EquitiNavigator(
    initialPage: onboardingRouteLocation.initialPage,
    observers: [
      SentryNavigatorObserver(),
      performanceRouteObserver,
      newRelicNavigationObserver,
      measureNavigationObserver,
    ],
    locations: [
      hubLocation,
      loginRouteLocation,
      equitiTraderLocation,
      onboardingRouteLocation,
      paymentRouteLocation,
    ],
  );

  @lazySingleton
  GlobalKey<NavigatorState> navigatorKey(EquitiNavigatorBase equitiNavigator) =>
      equitiNavigator.navigatorKey();
}
