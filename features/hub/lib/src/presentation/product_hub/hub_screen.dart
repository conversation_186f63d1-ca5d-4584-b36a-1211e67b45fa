import 'dart:developer';
import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:firebase/firebase_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_custom_carousel/flutter_custom_carousel.dart';
import 'package:hub/src/assets/assets.gen.dart' as hub;
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/navigation/hub_navigation.dart';
import 'package:hub/src/presentation/activity_notificactions/notifications_screen.dart';
import 'package:hub/src/presentation/widgets/hub_appbar.dart';
import 'package:preferences/preferences.dart';
import 'package:url_launcher/url_launcher.dart';

part 'widgets/learning_hub_section_widget.dart';
part 'widgets/news_section_widget.dart';
part 'widgets/section_item_widget.dart';

class HubScreen extends StatefulWidget {
  const HubScreen({super.key});

  @override
  State<HubScreen> createState() => _HubScreenState();
}

class _HubScreenState extends State<HubScreen> {
  @override
  void initState() {
    super.initState();

    final isTesting = Platform.environment.containsKey('FLUTTER_TEST');
    if (isTesting == false) {
      FirebaseMessages().getNotificationToken().then((token) {
        if (token == null) {
          log(
            "Notification token is null so not saving it...",
            name: "HubScreen",
          );
          return;
        }
        diContainer<EquitiPreferences>().setValue("notification_token", token);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: HubAppBar(
        onMenuPressed: () => diContainer<HubNavigation>().gotoSettings(),
        actions: [
          //implement logic later for unread notifications to show the red dot
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: InkWell(
              onTap: () => showNotificationsScreen(parentContext: context),
              child: Stack(
                children: [
                  hub.Assets.images.iconStateLayerLeft.svg(
                    colorFilter: ColorFilter.mode(
                      theme.foreground.fgPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  Positioned(
                    right: 10,
                    top: 8,
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        top: false,
        child: ListView(
          padding: const EdgeInsetsDirectional.symmetric(
            horizontal: 16,
            vertical: 32,
          ),
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                HubCardWidget(
                  tag: localization.hub_brokerage,
                  title: localization.hub_trading,
                  icon: hub.Assets.images.brokerageV2.svg(),
                  onTap: () => diContainer<HubNavigation>().goToTrading(),
                ),
                HubCardWidget(
                  tag: localization.hub_investment,
                  title: localization.hub_gold,
                  icon: hub.Assets.images.goldV2.svg(),
                  onTap: () => diContainer<HubNavigation>().gotoGold(),
                ),
                HubCardWidget(
                  tag: localization.hub_investment,
                  title: localization.hub_wealth,
                  icon: hub.Assets.images.wealthV2.svg(),
                  onTap: () => diContainer<HubNavigation>().gotoWealth(),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _PromoBannersWidget(),
            const SizedBox(height: 20),
            _NewsSectionWidget(),
            const SizedBox(height: 24),
            _LearningHubSectionWidget(),
          ],
        ),
      ),
    );
  }
}

class _PromoBannersWidget extends StatelessWidget {
  const _PromoBannersWidget();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    final carousel = CustomCarousel(
      itemCountBefore: 0,
      itemCountAfter: 1,
      scrollDirection: Axis.horizontal,
      loop: true,
      reverse: true,
      depthOrder: DepthOrder.reverse,
      scrollSpeed: 0.5,
      alignment: Alignment.bottomCenter,
      effectsBuilder: CustomCarousel.effectsBuilderFromAnimate(
        effects: EffectList()
            .fadeOut(delay: 100.ms, duration: 100.ms, curve: Curves.easeInBack)
            .slide(end: const Offset(-0.0, -0))
            .slideY(begin: -0.18, end: 0.0, curve: Curves.easeOut)
            .scale(begin: Offset(1, 1), end: Offset(0.90, 0.90))
            .slideX(delay: 0.ms, begin: 1.0),
      ),
      children: [
        PromoBannerWidget(
          title: localization.hub_takeTheWinPodcast,
          subtitle: localization.hub_takeTheWinPodcastSubtitle,
          icon: hub.Assets.images.bannerPodcast.svg(),
          onTap: () {
            _launchUrl(localization.trader_banner_take_the_win_podcast_url);
          },
        ),
        PromoBannerWidget(
          title: localization.hub_tradeWithoutSwapCharges,
          subtitle: localization.hub_tradeWithoutSwapChargesSubtitle,
          icon: Assets.images.bannerGold.svg(),
          onTap: () {
            _launchUrl(
              localization.trader_banner_trade_without_swap_charges_url,
            );
          },
        ),
      ],
    );
    return SizedBox(height: 140, child: carousel);
  }
}

void _launchUrl(String url) {
  try {
    launchUrl(Uri.parse(url));
  } catch (e) {
    debugPrint(e.toString());
  }
}
