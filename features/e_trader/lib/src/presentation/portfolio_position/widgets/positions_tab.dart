import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/portfolio_position/widgets/positions_loading.dart';
import 'package:e_trader/src/presentation/position_option/widget/show_position_option_sheet.dart';
import 'package:e_trader/src/presentation/positions_and_trades/expandable_position_header.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

class PositionsTab extends StatefulWidget {
  const PositionsTab({
    super.key,
    required this.tabController,
    required this.tabIndex,
    required this.tabSubscriptionManager,
  });

  final TabController tabController;
  final int tabIndex;
  final TabSubscriptionManager tabSubscriptionManager;

  @override
  State<PositionsTab> createState() => _PositionsTabState();
}

class _PositionsTabState extends State<PositionsTab>
    with RouteAwareAppLifecycleMixin, AutomaticKeepAliveClientMixin {
  bool _hasSubscribed = false;

  @override
  void initState() {
    super.initState();
    // Register with the centralized tab subscription manager
    // Use onFirstVisit for first-time subscription and onSubscribe for subsequent visits
    widget.tabSubscriptionManager.registerTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _subscribe,
      onUnsubscribe: _unsubscribe,
      onFirstVisit:
          () => context.read<PositionBloc>().add(PositionEvent.loadPositions()),
    );
  }

  @override
  void dispose() {
    // Unregister from the centralized tab subscription manager
    widget.tabSubscriptionManager.unregisterTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _subscribe,
      onUnsubscribe: _unsubscribe,
      onFirstVisit:
          () => context.read<PositionBloc>().add(PositionEvent.loadPositions()),
    );
    _unsubscribe();
    super.dispose();
  }

  @override
  void onAppForeground() {
    // App came to foreground - subscribe if this tab is active and route is visible
    if (!widget.tabSubscriptionManager.isActive) return;
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index == widget.tabIndex) {
      _subscribe();
    }
  }

  @override
  void onAppBackground() {
    // App went to background - unsubscribe to save resources
    if (!widget.tabSubscriptionManager.isActive) return;
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index == widget.tabIndex) {
      _unsubscribe();
    }
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    // Route visibility is automatically tracked by the mixin
    // Don't handle any route changes if parent tab subscription manager is paused or context is inactive
    if (!widget.tabSubscriptionManager.isActive) return;

    // Only handle route changes if this is the currently selected tab
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index != widget.tabIndex) return;
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    // Route visibility is automatically tracked by the mixin
    // Don't handle any route changes if parent tab subscription manager is paused
    if (!widget.tabSubscriptionManager.isActive) return;

    // Only handle route changes if this is the currently selected tab
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index != widget.tabIndex) return;
    _unsubscribe();
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      if (mounted) {
        context.read<PositionBloc>().add(
          PositionEvent.updatePositions(TradingSocketEvent.positions.subscribe),
        );
      }
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdatePositionsUseCase>().call(
          eventType: TradingSocketEvent.positions.unsubscribe,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);

    return CustomScrollView(
      slivers: [
        BlocConsumer<PositionBloc, PositionState>(
          listenWhen:
              (previous, current) =>
                  previous.processState != current.processState &&
                  current.processState is PositionConnectedState,
          listener: (listenerContext, state) {
            if (widget.tabController.indexIsChanging) return;
            if (widget.tabController.index != widget.tabIndex) return;
            _subscribe();
          },
          buildWhen: (previous, current) => previous != current,
          builder:
              (blocBuilderContext, state) => switch (state.processState) {
                PositionLoadingState() ||
                PositionConnectedState() => const PositionsLoading(),
                PositionSuccessState() => () {
                  if (state.groupedPositions.isEmpty &&
                      state.processState is PositionSuccessState) {
                    return SliverFillRemaining(
                      hasScrollBody: false,
                      child: EmptyOrErrorStateComponent.empty(
                        svgImage:
                            trader.Assets.images.portfolioEmptyPositionList
                                .svg(),
                        title: localization.trader_noOpenPositions,
                        description:
                            localization.trader_noOpenPositionsDescription,
                        isCenterlized: true,
                      ),
                    );
                  }
                  return SliverMainAxisGroup(
                    slivers: [
                      SliverList.builder(
                        itemCount: state.groupedPositions.length,
                        itemBuilder: (listViewContext, index) {
                          final groupedPosition =
                              state.groupedPositions.values.elementAtOrNull(
                                index,
                              )!;

                          return ExpandablePositionHeader(
                            productName: groupedPosition.tickerName,
                            productIcon: groupedPosition.url,
                            profit: groupedPosition.totalProfit,
                            margin: groupedPosition.totalMargin,
                            isHedging: groupedPosition.isHedged,
                            tradeType: groupedPosition.groupTradeType,
                            lots: groupedPosition.totalLotSize,
                            tradesData: groupedPosition.positions,
                            currency: state.selectedAccountCurrency,
                            onTap:
                                () => showPositionOptionSheet(
                                  blocBuilderContext,
                                  SymbolDetailViewModel(
                                    symbolName: groupedPosition.tickerName,
                                    platformName: groupedPosition.platformName,
                                    minLot:
                                        groupedPosition
                                            .positions
                                            .firstOrNull
                                            ?.minLot ??
                                        0.0,
                                    maxLot:
                                        groupedPosition
                                            .positions
                                            .firstOrNull
                                            ?.maxLot ??
                                        0.0,
                                    imageURL: groupedPosition.url,
                                    lotsSteps: 0.01,
                                    digit:
                                        groupedPosition
                                            .positions
                                            .firstOrNull
                                            ?.digits,
                                  ),
                                  state.selectedAccountCurrency,
                                ),
                          );
                        },
                      ),
                      SliverToBoxAdapter(child: SizedBox(height: 20)),
                    ],
                  );
                }(),

                PositionEmptyState() => SliverFillRemaining(
                  hasScrollBody: false,
                  child: EmptyOrErrorStateComponent.empty(
                    svgImage:
                        trader.Assets.images.portfolioEmptyPositionList.svg(),
                    title: localization.trader_noOpenPositions,
                    description: localization.trader_noOpenPositionsDescription,
                    isCenterlized: true,
                  ),
                ),
                PositionErrorState() => SliverFillRemaining(
                  hasScrollBody: false,
                  child: EmptyOrErrorStateComponent.error(
                    description: localization.trader_insightsErrorDescription,
                    title: localization.trader_somethingWentWrong,
                    svgImage: trader.Assets.images.bug.svg(),
                    retryButtonText: localization.trader_reload,
                    onTapRetry: () {
                      _hasSubscribed = false;
                      context.read<PositionBloc>().add(
                        PositionEvent.loadPositions(),
                      );
                    },
                  ),
                ),
              },
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
