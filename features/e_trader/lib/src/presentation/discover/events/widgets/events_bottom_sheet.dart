import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/events_response_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_details_view.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:theme_manager/theme_manager.dart';

class EventsBottomSheet {
  const EventsBottomSheet();

  static void showDetails({
    required BuildContext context,
    required EventDetails eventDetails,
  }) {
    final loc = EquitiLocalization.of(context);

    DuploSheet.showModalSheetV2<void>(
      context,
      isFullScreen: true,
      appBar: DuploAppBar(
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        title: loc.trader_events,
        titleWidget: DuploText(
          color: context.duploTheme.text.textPrimary,
          text: loc.trader_events,
          style: context.duploTextStyles.textLg,
          fontWeight: DuploFontWeight.bold,
        ),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon:
                diContainer<ThemeManager>().isDarkMode
                    ? Assets.images.closeIc.svg(
                      colorFilter: ColorFilter.mode(
                        DuploTheme.of(context).foreground.fgSecondary,
                        BlendMode.srcIn,
                      ),
                    )
                    : Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      content: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 16),
        child: Column(
          children: [
            Divider(color: context.duploTheme.border.borderSecondary),
            EventsDetailsView(eventDetails: eventDetails),
          ],
        ),
      ),
    );
  }
}
