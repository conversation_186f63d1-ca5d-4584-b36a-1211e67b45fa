import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/events_response_model.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/tag_list.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:prelude/prelude.dart';

class EventsDetailsView extends StatelessWidget {
  const EventsDetailsView({super.key, required this.eventDetails});
  final EventDetails eventDetails;

  @override
  Widget build(BuildContext context) {
    final duploTextStyles = DuploTextStyles.of(context);
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              eventDetails.symbols.length == 1 &&
                      eventDetails.eventType == 'Corporate'
                  ? Row(
                    children: [
                      DuploCachedNetworkImage(
                        imageUrl: eventDetails.logoUrl ?? '',
                        imageHeight: 29,
                        imageWidth: 29,
                        errorWidget: CircleAvatar(
                          radius: 15,
                          backgroundColor: theme.border.borderSecondary,
                        ),
                      ),
                      SizedBox(width: 8),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DuploText(
                            text: eventDetails.symbols.firstOrNull,
                            style: duploTextStyles.textMd,
                            textAlign: TextAlign.start,
                            fontWeight: DuploFontWeight.semiBold,
                            color: theme.text.textPrimary,
                          ),
                          DuploText(
                            text: eventDetails.productName,
                            style: duploTextStyles.textXs,
                            textAlign: TextAlign.start,
                            fontWeight: DuploFontWeight.regular,
                            color: theme.text.textTertiary,
                          ),
                        ],
                      ),
                    ],
                  )
                  : DuploText(
                    text: eventDetails.formattedDate(localization),
                    style: duploTextStyles.textXs,
                    textAlign: TextAlign.start,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textQuaternary,
                  ),
              DuploText(
                text: eventDetails.source,
                style: duploTextStyles.textXs,
                fontWeight: DuploFontWeight.medium,
                color: theme.text.textPrimary,
              ),
            ],
          ),
          eventDetails.symbols.length == 1 &&
                  eventDetails.eventType == 'Corporate'
              ? Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: DuploText(
                  text: eventDetails.formattedDate(localization),
                  style: duploTextStyles.textXs,
                  textAlign: TextAlign.start,
                  fontWeight: DuploFontWeight.medium,
                  color: theme.text.textQuaternary,
                ),
              )
              : SizedBox.shrink(),
          const SizedBox(height: 4),
          DuploText(
            text: eventDetails.name,
            style: duploTextStyles.textMd,
            textAlign: TextAlign.start,
            fontWeight: DuploFontWeight.semiBold,
            color: theme.text.textPrimary,
          ),
          const SizedBox(height: 8),
          TagList(tags: [eventDetails.eventType] + eventDetails.tags),
          if (eventDetails.description != null &&
              eventDetails.description!.isNotEmpty) ...[
            const SizedBox(height: 24),
            HtmlWidget(
              eventDetails.description!,
              textStyle: TextStyle(color: theme.text.textSecondary),
            ),
          ],
          if (eventDetails.symbols.length > 0) ...[
            const SizedBox(height: 52),
            DuploText(
              text: localization.trader_affectedMarkets,
              style: duploTextStyles.textMd,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 16),
            TagList(tags: eventDetails.symbols),
            const SizedBox(height: 24),
          ],
        ],
      ),
    );
  }
}
