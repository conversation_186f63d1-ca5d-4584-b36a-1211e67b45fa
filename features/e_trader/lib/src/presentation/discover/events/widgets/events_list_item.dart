import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/events_response_model.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/tag_list.dart';
import 'package:e_trader/src/presentation/duplo/affected_markets_indicator.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/event_details_view.dart';
import 'package:e_trader/src/presentation/discover/events/widgets/events_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class EventsListItem extends StatelessWidget {
  final EventDetails eventDetails;

  const EventsListItem({required this.eventDetails});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = DuploTextStyles.of(context);
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context).toString();
    return DuploTap(
      onTap:
          () => EventsBottomSheet.showDetails(
            context: context,
            eventDetails: eventDetails,
          ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,

              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      DuploText(
                        text: EquitiFormatter.formatDateTimeToTimeString(
                          eventDetails.parsedDate,
                        ),
                        textAlign: TextAlign.start,

                        style: duploTextStyles.textXs,
                        fontWeight: DuploFontWeight.medium,
                        color: theme.text.textQuaternary,
                      ),
                      SizedBox(height: 8),

                      (eventDetails.symbols.length == 1) &&
                              eventDetails.eventType == 'Corporate'
                          ? Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DuploText(
                                text: eventDetails.symbols.firstOrNull,
                                style: duploTextStyles.textMd,
                                textAlign: TextAlign.start,

                                fontWeight: DuploFontWeight.semiBold,
                                color: theme.text.textPrimary,
                              ),
                              DuploText(
                                text: eventDetails.productName,
                                style: duploTextStyles.textXs,
                                textAlign: TextAlign.start,

                                fontWeight: DuploFontWeight.regular,
                                color: theme.text.textTertiary,
                              ),
                              SizedBox(height: 8),
                            ],
                          )
                          : SizedBox.shrink(),
                      DuploText(
                        overflow: TextOverflow.visible,
                        text: eventDetails.name,
                        textAlign: TextAlign.start,

                        style: duploTextStyles.textMd,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 4),
                _AddToCalendarWidget(
                  title: eventDetails.name,
                  subtitle: eventDetails.description ?? '',
                  formattedDate: eventDetails.formattedDate(l10n),
                  eventId: eventDetails.id,
                  date: DateTime.parse(eventDetails.date),
                ),
              ],
            ),

            // SizedBox(height: 8),
            // Container(
            //   child: DuploText(
            //     text: eventDetails.name,
            //     style: duploTextStyles.textMd,
            //     fontWeight: DuploFontWeight.semiBold,
            //     color: theme.text.textPrimary,
            //   ),
            // ),
            const SizedBox(height: 8),
            TagList(tags: [eventDetails.eventType] + eventDetails.tags),
            if (eventDetails.eventDetails.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 24.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    EventDetailsView(
                      dividerColor: theme.foreground.fgPrimary,
                      title: l10n.trader_actual,
                      value:
                          eventDetails.eventDetails.firstOrNull!.actual != null
                              ? EquitiFormatter.formatNumber(
                                value:
                                    eventDetails
                                        .eventDetails
                                        .firstOrNull!
                                        .actual!,
                                locale: locale,
                              )
                              : '-',
                    ),
                    EventDetailsView(
                      dividerColor: theme.utility.utilitySuccess700,
                      title: l10n.trader_forecast,
                      value:
                          eventDetails.eventDetails.firstOrNull!.expected !=
                                  null
                              ? EquitiFormatter.formatNumber(
                                value:
                                    eventDetails
                                        .eventDetails
                                        .firstOrNull!
                                        .expected!,
                                locale: locale,
                              )
                              : '-',
                    ),
                    EventDetailsView(
                      dividerColor: theme.foreground.fgPrimary,
                      title: l10n.trader_previous,
                      value:
                          eventDetails.eventDetails.firstOrNull!.previous !=
                                  null
                              ? EquitiFormatter.formatNumber(
                                value:
                                    eventDetails
                                        .eventDetails
                                        .firstOrNull!
                                        .previous!,
                                locale: locale,
                              )
                              : '-',
                    ),
                  ],
                ),
              ),
            if (eventDetails.symbols.isNotEmpty &&
                eventDetails.symbols.length > 1)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DuploText(
                      text: l10n.trader_affectedMarkets,
                      style: duploTextStyles.textXs,
                      color: theme.text.textQuaternary,
                    ),
                    AffectedMarketsIndicator(
                      marketSymbols: eventDetails.symbols,
                      maxCircles: 5,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _AddToCalendarWidget extends StatelessWidget {
  const _AddToCalendarWidget({
    required this.title,
    required this.subtitle,
    required this.formattedDate,
    required this.eventId,
    required this.date,
  });

  final String title;
  final String subtitle;
  final String formattedDate;
  final String eventId;
  final DateTime date;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocListener<EventsBloc, EventsState>(
      listenWhen: (previous, current) {
        if (current.processState is EventsCalendarMessage &&
            current.processState != previous.processState) {
          final calendarMessage = current.processState as EventsCalendarMessage;
          return calendarMessage.eventId == eventId;
        }
        if (current.processState is CalendarError) {
          final calendarMessage = current.processState as CalendarError;
          return calendarMessage.eventId == eventId;
        }
        return false;
      },
      listener: (listenerContext, state) {
        if (state.processState is EventsCalendarMessage) {
          final calendarMessage = state.processState as EventsCalendarMessage;
          final message = calendarMessage.message;
          if (message.isNotEmpty) {
            final toast = DuploToast();
            toast.hidesToastMessage();
            toast.showToastMessage(
              context: context,
              widget: DuploToastDecoratorWidget(
                messageType: ToastMessageType.success,
                statusColor: context.duploTheme.utility.utilitySuccess600,
                contentWidget: const SizedBox.shrink(),
                titleMessage: message,
                onLeadingAction: toast.hidesToastMessage,
                onTap: toast.hidesToastMessage,
              ),
            );
          }
        }
        if (state.processState is CalendarError) {
          final error = state.processState as CalendarError;
          if (error.message.isNotEmpty) {
            final toast = DuploToast();
            toast.hidesToastMessage();
            toast.showToastMessage(
              context: context,
              widget: DuploToastMessage(
                messageType: ToastMessageType.error,
                descriptionMessage: error.message,
                titleMessage: error.title,
                onLeadingAction: toast.hidesToastMessage,
                onTap: toast.hidesToastMessage,
              ),
            );
          }
        }
      },
      child: InkWell(
        onTap: () {
          final l10n = EquitiLocalization.of(context);

          context.read<EventsBloc>().add(
            EventsEvent.addEventToCalendar(
              title: title,
              subtitle: subtitle,
              date: date,
              eventId: eventId,
              successMessage: l10n.trader_eventAddedToCalendar,
              errorMessage: l10n.trader_failedToAddEvent,
              permissionDeniedMessage: l10n.trader_permissionDenied,
              noCalendarFoundMessage: l10n.trader_noCalendarFound,
              operationFailedMessage: l10n.trader_failedToAddEvent,
            ),
          );
        },
        child: Container(
          height: 48,
          width: 48,
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            border: Border.all(color: theme.button.buttonSecondaryBorder),
            color: theme.button.buttonSecondaryBg,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: theme.shadow.shadowToastContainer,
                spreadRadius: -2,
                blurRadius: 2,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: trader.Assets.images.calendarAdd.svg(
            height: 20,
            width: 20,
            colorFilter: ColorFilter.mode(
              theme.button.buttonSecondaryFg,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}
