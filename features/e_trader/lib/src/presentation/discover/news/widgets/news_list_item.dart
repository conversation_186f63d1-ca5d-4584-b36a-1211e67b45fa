import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/tag_list.dart';
import 'package:e_trader/src/presentation/duplo/affected_markets_indicator.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';

class NewsListItem extends StatelessWidget {
  final NewsItemDetails newsItemDetails;

  const NewsListItem({required this.newsItemDetails});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = DuploTextStyles.of(context);
    final l10n = EquitiLocalization.of(context);
    final isDark = diContainer<ThemeManager>().isDarkMode;

    return InkWell(
      onTap:
          () => NewsBottomSheet.showDetails(
            context: context,
            newsItemDetails: newsItemDetails,
          ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                newsItemDetails.symbols.length == 1
                    ? Row(
                      children: [
                        DuploCachedNetworkImage(
                          imageUrl: newsItemDetails.logoUrl ?? '',
                          imageHeight: 29,
                          imageWidth: 29,
                          errorWidget: CircleAvatar(
                            radius: 15,
                            backgroundColor: theme.border.borderSecondary,
                          ),
                        ),
                        SizedBox(width: 8),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DuploText(
                              text: newsItemDetails.symbols.firstOrNull,
                              style: duploTextStyles.textMd,
                              textAlign: TextAlign.start,

                              fontWeight: DuploFontWeight.semiBold,
                              color: theme.text.textPrimary,
                            ),
                            DuploText(
                              text: newsItemDetails.productName,
                              style: duploTextStyles.textXs,
                              textAlign: TextAlign.start,

                              fontWeight: DuploFontWeight.regular,
                              color: theme.text.textTertiary,
                            ),
                          ],
                        ),
                      ],
                    )
                    : DuploText(
                      text: EquitiFormatter.formatDateTimeToTimeString(
                        newsItemDetails.parsedDate,
                      ),
                      textAlign: TextAlign.start,

                      style: duploTextStyles.textXs,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.text.textQuaternary,
                    ),
                newsItemDetails.source == 'Dow Jones'
                    ? Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: trader.Assets.images.djLogo.svg(
                        colorFilter:
                            isDark
                                ? ColorFilter.mode(
                                  theme.text.textPrimary,
                                  BlendMode.srcIn,
                                )
                                : null,
                      ),
                    )
                    : DuploText(
                      text: newsItemDetails.source,
                      style: duploTextStyles.textXs,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.text.textPrimary,
                    ),
              ],
            ),

            newsItemDetails.symbols.length == 1
                ? Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: DuploText(
                    text: EquitiFormatter.formatDateTimeToTimeString(
                      newsItemDetails.parsedDate,
                    ),
                    textAlign: TextAlign.start,

                    style: duploTextStyles.textXs,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textQuaternary,
                  ),
                )
                : SizedBox.shrink(),
            DuploText(
              text: newsItemDetails.title,
              style: duploTextStyles.textMd,
              textAlign: TextAlign.start,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            SizedBox(height: 8),
            TagList(tags: newsItemDetails.tags),

            if (newsItemDetails.symbols.isNotEmpty &&
                newsItemDetails.symbols.length > 1)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DuploText(
                      text: l10n.trader_affectedMarkets,

                      style: duploTextStyles.textXs,
                      color: theme.text.textQuaternary,
                    ),
                    AffectedMarketsIndicator(
                      marketSymbols: newsItemDetails.symbols,
                      maxCircles: 5,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
