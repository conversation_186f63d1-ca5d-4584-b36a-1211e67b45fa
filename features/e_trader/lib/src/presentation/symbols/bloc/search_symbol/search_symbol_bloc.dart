import 'dart:async';

import 'package:e_trader/src/data/api/linked_symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_model.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/model/sort_order.dart';
import 'package:e_trader/src/domain/model/symbol_price_info_view_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_symbols_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_list_of_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/symbol_local_data_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/model/symbol_quote_view_model.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbol_view/symbol_view_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'search_symbol_event.dart';
part 'search_symbol_state.dart';
part 'search_symbol_bloc.freezed.dart';

@injectable
class SearchSymbolBloc extends Bloc<SearchSymbolEvent, SearchSymbolState>
    with DisposableMixin {
  SearchSymbolBloc(
    this._getSymbolsUseCase,
    this._subscribeToSymbolsQuotesUseCase,
    this._symbolLocalDataUseCase,
    this._getAccountNumberUseCase,
    this._navigation,
    SymbolViewCubit _symbolViewCubit,
    this._tradingAnalyticsEvent,
  ) : super(
        _SearchSymbolState(
          symbolDetailViewModel: {},
          symbolQuoteViewModel: {},
          priceInfoViewType: _symbolViewCubit.state.viewType,
          sortOrder: _symbolViewCubit.state.sortOrder,
          currentState: SymbolsProcessState.initial(),
          previousSearches: _symbolLocalDataUseCase.getPreviousSearches(),
        ),
      ) {
    on<SearchSymbolEvent>((event, emit) {
      if (event is _ProcessSymbolPriceResult) {
        return _processSymbolPriceResult(event, emit);
      } else if (event is _OnGoToDetails) {
        return _onGoToDetails(event);
      } else if (event is _ResetAndClose) {
        return _onResetAndClose(emit);
      }
    });
    on<_OnSearchSymbols>(
      (event, emit) => _onSearchSymbols(event, emit),
      transformer: throttleTransformer(_throttleDuration),
    );
    on<_LoadMoreSymbols>((event, emit) => _onLoadMoreSymbols(event, emit));
  }
  static const _throttleDuration = Duration(milliseconds: 500);

  final GetSymbolsUseCase _getSymbolsUseCase;
  final SymbolLocalDataUseCase _symbolLocalDataUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final EquitiTraderNavigation _navigation;
  final SubscribeToListOfSymbolQuotesUseCase _subscribeToSymbolsQuotesUseCase;
  final TradingAnalytics _tradingAnalyticsEvent;

  Future<void> _onSearchSymbols(
    _OnSearchSymbols event,
    Emitter<SearchSymbolState> emit,
  ) async {
    final query = event.query.trim();

    if (query.isEmpty) {
      // Load previous searches and emit initial state when query is empty
      final previousSearches = _symbolLocalDataUseCase.getPreviousSearches();
      emit(
        state.copyWith(
          currentQuery: query,
          currentState: SymbolsProcessState.initial(),
          symbolDetailViewModel: {},
          previousSearches: previousSearches,
          currentPage: 1,
          hasReachedMax: false,
          shouldClose: false,
        ),
      );
      return;
    }

    // Emit loading state when starting search
    emit(
      state.copyWith(
        currentQuery: query,
        currentState: SymbolsProcessState.loading(),
        symbolDetailViewModel: {},
        currentPage: 1,
        hasReachedMax: false,
        shouldClose: false,
      ),
    );

    if (query.length > 1) {
      List<String> previousSearches =
          await _symbolLocalDataUseCase.getPreviousSearches();
      if (!previousSearches.contains(query)) {
        _symbolLocalDataUseCase.savePreviousSearches(query);
      }
      final response =
          await _fetchAndProcessSymbols(query)
              .flatMap(_handlePagination)
              .flatMap(
                (_) => _subscribeToSymbolsQuotesUseCase(
                  subscriberId: '${SearchSymbolBloc}_$hashCode',
                ),
              )
              .run();
      return response.fold(
        (exception) {
          _tradingAnalyticsEvent.searchSymbol(query, false, 0);
          _handleError(exception, emit);
        },
        (symbolPriceResult) {
          _tradingAnalyticsEvent.searchSymbol(query, true, state.symbolsCount);
          _handleSuccess(symbolPriceResult, emit);
        },
      );
    }
  }

  Future<void> _onLoadMoreSymbols(
    _LoadMoreSymbols _,
    Emitter<SearchSymbolState> emit,
  ) async {
    // Don't load more if already reached max or no current query
    if (state.hasReachedMax || state.currentQuery.isEmpty) return;

    final response =
        await _fetchAndProcessSymbols(state.currentQuery)
            .flatMap(_handlePagination)
            .flatMap(
              (_) => _subscribeToSymbolsQuotesUseCase(
                subscriberId: '${SearchSymbolBloc}_$hashCode',
              ),
            )
            .run();
    return response.fold(
      (exception) => _handleError(exception, emit),
      (symbolPriceResult) => _handleSuccess(symbolPriceResult, emit),
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _fetchAndProcessSymbols(
    String query,
  ) => _getSymbolsUseCase(
    categoryID: state.selectedCategoryID,
    pageNumber: state.currentPage,
    query: query,
    sortOrder: state.sortOrder,
  ).chainFirst(_updateSymbolsDetail);

  TaskEither<Exception, LinkedSymbolModel> _updateSymbolsDetail(
    LinkedSymbolModel linkedSymbols,
  ) {
    final symbols =
        linkedSymbols.symbols
            .where((symbol) => symbol.platformName != null)
            .toList();
    state.setSymbolsCount(linkedSymbols.count);
    if (state.symbolsDetail.isEmpty) {
      _createInitialSymbolsMap(symbols);
    } else {
      _updateExistingSymbolsMap(symbols);
    }

    return TaskEither.of(linkedSymbols);
  }

  void _createInitialSymbolsMap(List<SymbolModel> symbols) {
    final symbolsMap = <String, SymbolDetailViewModel>{
      for (final symbol in symbols)
        symbol.platformName!: SymbolDetailViewModel(
          symbolName: symbol.tickerName!,
          platformName: symbol.platformName!,
          imageURL: symbol.productLogoUrl,
          assetType: symbol.assetType,
          minLot: symbol.minLot,
          maxLot: symbol.maxLot,
          digit: symbol.digits ?? 5,
          isForex: symbol.isForex,
          lotsSteps: symbol.lotsSteps ?? 0,
        ),
    };
    state.setSymbolsDetail(symbolsMap);
    _createInitialQuotesFromSymbols(symbols);
  }

  void _createInitialQuotesFromSymbols(List<SymbolModel> symbols) {
    final quotesMap = state.symbolsQuote;
    for (final symbol in symbols) {
      if (symbol.platformName == null) continue;

      // create initial quote if we don't already have one
      if (quotesMap[symbol.platformName] == null) {
        quotesMap[symbol.platformName!] = SymbolQuoteViewModel(
          ask: symbol.ask,
          bid: symbol.bid,
          digits: symbol.digits ?? 5,
          spread: symbol.spread ?? 0,
          direction: 'NONE',
          dailyChange: symbol.dailyRateChange ?? 0,
          midPrice: symbol.midPrice ?? ((symbol.ask + symbol.bid) / 2),
        );
      }
    }
  }

  void _updateExistingSymbolsMap(List<SymbolModel> symbols) {
    final existingSymbolsMap = state.symbolsDetail;
    for (final symbol in symbols) {
      final existingSymbol = existingSymbolsMap[symbol.platformName];
      existingSymbolsMap[symbol.platformName!] =
          existingSymbol != null
              ? _updateExistingSymbol(existingSymbol, symbol)
              : _createNewSymbol(symbol);
    }
    state.setSymbolsDetail(existingSymbolsMap);
    _createInitialQuotesFromSymbols(symbols);
  }

  SymbolDetailViewModel _updateExistingSymbol(
    SymbolDetailViewModel existing,
    SymbolModel symbol,
  ) {
    return existing
      ..symbolName = symbol.tickerName!
      ..imageURL = symbol.productLogoUrl
      ..assetType = symbol.assetType;
  }

  SymbolDetailViewModel _createNewSymbol(SymbolModel symbol) {
    return SymbolDetailViewModel(
      symbolName: symbol.tickerName!,
      platformName: symbol.platformName!,
      imageURL: symbol.productLogoUrl,
      assetType: symbol.assetType,
      minLot: symbol.minLot,
      maxLot: symbol.maxLot,
      digit: symbol.digits ?? 5,
      isForex: symbol.isForex,
      lotsSteps: symbol.lotsSteps ?? 0,
    );
  }

  TaskEither<Exception, LinkedSymbolModel> _handlePagination(
    LinkedSymbolModel linkedSymbols,
  ) {
    if (state.symbolsDetail.length == linkedSymbols.count) {
      state.hasReachedMax = true;
    } else {
      state.currentPage = state.currentPage + 1;
    }
    return TaskEither.of(linkedSymbols);
  }

  void _handleError(Exception exception, Emitter<SearchSymbolState> emit) {
    addError(exception);
    emit(state.copyWith(currentState: SymbolsProcessState.error()));
  }

  void _handleSuccess(
    Stream<SymbolQuoteModel> symbolPriceResult,
    Emitter<SearchSymbolState> emit,
  ) {
    addSubscription(
      symbolPriceResult.listen(
        (result) {
          add(SearchSymbolEvent.processSymbolPriceResult(result: result));
        },
        onError: (Object e, StackTrace stackTrace) {
          addError(e, stackTrace);
        },
      ),
    );
    emit(state.copyWith(currentState: SymbolsProcessState.success()));
  }

  void _processSymbolPriceResult(
    _ProcessSymbolPriceResult event,
    Emitter<SearchSymbolState> emit,
  ) {
    final symbolQuoteModel = event.result;
    final existingSymbolDetail =
        state.symbolsDetail[symbolQuoteModel.platformName];
    if (existingSymbolDetail == null) return;
    final symbolQuote =
        state.symbolsQuote[symbolQuoteModel.platformName] ??
        SymbolQuoteViewModel(
          ask: symbolQuoteModel.ask,
          bid: symbolQuoteModel.bid,
          digits: symbolQuoteModel.digits,
          spread: symbolQuoteModel.spread,
          direction: symbolQuoteModel.direction,
          dailyChange: symbolQuoteModel.dailyRateChange,
          midPrice: symbolQuoteModel.midPrice,
        );

    if (state.symbolsQuote[symbolQuoteModel.platformName] != null) {
      symbolQuote
        ..ask = symbolQuoteModel.ask
        ..bid = symbolQuoteModel.bid
        ..digits = symbolQuoteModel.digits
        ..spread = symbolQuoteModel.spread
        ..direction = symbolQuoteModel.direction
        ..midPrice = symbolQuoteModel.midPrice
        ..dailyChange = symbolQuoteModel.dailyRateChange;
    }

    state.symbolsQuote[symbolQuoteModel.platformName] = symbolQuote;
    state.symbolQuoteViewModel[state.selectedCategoryID] = state.symbolsQuote;
    if (isClosed) return;
    emit(state.copyWith(currentState: SymbolsProcessState.priceSucces()));
  }

  void _onGoToDetails(_OnGoToDetails event) {
    return _getAccountNumberUseCase().fold((left) => "", (number) {
      add(SearchSymbolEvent.resetAndClose());

      _navigation.navigateToProductDetail(
        symbolDetail: event.symbolDetail,
        accountNumber: number,
        tradeDirection: event.tradeDirection,
      );
    });
  }

  void _onResetAndClose(Emitter<SearchSymbolState> emit) {
    // Set isClosing to true to prevent multiple triggers
    emit(state.copyWith(isClosing: true));

    // Load previous searches and perform reset (same logic as empty search)
    final previousSearches = _symbolLocalDataUseCase.getPreviousSearches();

    // Emit final state with reset data and shouldClose flag
    emit(
      state.copyWith(
        currentQuery: '',
        currentState: SymbolsProcessState.initial(),
        symbolDetailViewModel: {},
        symbolQuoteViewModel: {},
        previousSearches: previousSearches,
        currentPage: 1,
        hasReachedMax: false,
        shouldClose: true,
        isClosing: false,
      ),
    );
  }
}
