part of 'show_position_option_sheet.dart';

class _PositionOptionSheetContent extends StatefulWidget {
  const _PositionOptionSheetContent({
    required this.symbolDetailViewModel,
    required this.currency,
  });

  final SymbolDetailViewModel symbolDetailViewModel;
  final String? currency;

  @override
  State<_PositionOptionSheetContent> createState() =>
      _PositionOptionSheetContentState();
}

class _PositionOptionSheetContentState
    extends State<_PositionOptionSheetContent>
    with RouteAwareAppLifecycleMixin {
  bool _hasSubscribed = false;

  @override
  void onAppForeground() {
    _subscribe();
  }

  @override
  void onAppBackground() {
    _unsubscribe();
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    // Don't mark route as not visible when opening the close trade bottom sheet
    // as it still needs the subscription data and app lifecycle callbacks
    if (route.settings.name != kCloseTradeBottomSheetRouteName) {
      super.onRoutePushed(route);
      _unsubscribe();
    }
    // If it IS the close trade sheet, don't call super and don't unsubscribe
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      if (mounted) {
        context.read<PositionOptionBloc>().add(
          PositionOptionEvent.updatePositions(
            eventType: TradingSocketEvent.positions.subscribe,
            symbol: widget.symbolDetailViewModel.platformName,
          ),
        );
      }
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdatePositionsUseCase>().call(
          eventType: TradingSocketEvent.positions.unsubscribe,
          symbolName: widget.symbolDetailViewModel.platformName,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;

    return ListView(
      children: [
        BlocConsumer<PositionOptionBloc, PositionOptionState>(
          listenWhen:
              (previous, current) =>
                  previous.processState != current.processState &&
                  current.processState is PositionOptionConnectedProcessState,
          listener: (listenerContext, state) => _subscribe(),
          buildWhen: (previous, current) => previous != current,
          builder: (blocContext, state) {
            return switch (state.processState) {
              PositionOptionLoadingProcessState() => DuploShimmerListItem(
                height: 40,
              ),
              PositionOptionSuccessProcessState() ||
              PositionOptionMarketClosedProcessState() ||
              PositionOptionClosePositionsSuccessProcessState() => () {
                final groupedPosition = state.groupedPositions;
                return groupedPosition != null &&
                        groupedPosition.positions.isNotEmpty
                    ? PositionHeader(
                      productName: groupedPosition.tickerName,
                      profit: groupedPosition.totalProfit,
                      margin: groupedPosition.totalMargin,
                      productIcon: groupedPosition.url,
                      isHedging: groupedPosition.isHedged,
                      tradeType: groupedPosition.groupTradeType,
                      lots: groupedPosition.totalLotSize,
                      currency: widget.currency,
                    )
                    : const SizedBox.shrink();
              }(),
              _ => DuploShimmerListItem(height: 40),
            };
          },
        ),
        Container(
          color: theme.background.bgSecondary,
          padding: EdgeInsetsDirectional.only(start: 10, end: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12),
              TextChevronWidget(
                title: localization.trader_marketDetails,
                backgroundColor: theme.background.bgSecondary,
                onPressed: () {
                  diContainer<PositionOptionBloc>()..add(
                    PositionOptionEvent.navigateToProductDetails(
                      symbolDetail: widget.symbolDetailViewModel,
                    ),
                  );
                },
              ),
              Divider(
                height: 0,
                color: theme.border.borderSecondary,
                endIndent: 10,
                indent: 10,
              ),
              Divider(
                height: 0,
                color: theme.border.borderSecondary,
                endIndent: 10,
                indent: 10,
              ),
              SizedBox(height: 24),
              Padding(
                padding: const EdgeInsetsDirectional.only(start: 12),
                child: DuploText(
                  text: localization.trader_closePositionBy,
                  fontWeight: DuploFontWeight.semiBold,
                  style: context.duploTextStyles.textLg,
                  color: theme.text.textPrimary,
                ),
              ),
            ],
          ),
        ),
        BlocConsumer<PositionOptionBloc, PositionOptionState>(
          listenWhen:
              (previous, current) =>
                  previous.processState != current.processState,
          listener: (listenerContext, state) {
            final processState = state.processState;
            if (processState
                is PositionOptionClosePositionsSuccessProcessState) {
              final toast = DuploToast();
              toast.showToastMessage(
                context: context,
                widget: DuploToastTrade(
                  isPriceCentered: true,
                  priceColor:
                      processState.price > 0
                          ? theme.text.textSuccessPrimary
                          : theme.text.textErrorPrimary,
                  onLeadingAction: toast.hidesToastMessage,
                  titleMessage: processState.successMessageTitle,
                  trade: TradeToastModel(
                    symbolImage: processState.url,
                    symbolName: processState.symbolName,
                    lotSize: EquitiFormatter.decimalPatternDigits(
                      value: processState.lotSize,
                      digits: 2,
                      locale: Localizations.localeOf(context).toString(),
                    ),
                    price: EquitiFormatter.formatTradeProfitOrLoss(
                      value: processState.price,
                      locale: Localizations.localeOf(context).toString(),
                    ),
                    type: switch (processState.tradeType) {
                      TradeType.buy => TradeToastType.buy,
                      TradeType.sell => TradeToastType.sell,
                      null => TradeToastType.buySellCombined,
                    },
                    currency: state.accountCurrency,
                  ),
                  type: ToastMessageType.success,
                  actionButtonTitle: localization.trader_viewPortfolio,
                ),
              );
            }
            if (state.processState
                is PositionOptionClosePositionsErrorProcessState) {
              final toast = DuploToast();
              toast.hidesToastMessage();
              toast.showToastMessage(
                context: context,
                widget: DuploToastMessage(
                  titleMessage: localization.trader_issueClosingTrades,
                  descriptionMessage:
                      EquitiLocalization.of(
                        listenerContext,
                      ).trader_loadingErrorDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () => toast.hidesToastMessage(),
                ),
              );
            } else if (state.processState
                is PositionOptionMarketClosedProcessState) {
              final toast = DuploToast();
              toast.showToastMessage(
                context: context,
                widget: DuploToastMessage(
                  titleMessage: localization.trader_marketIsClosed,
                  descriptionMessage:
                      localization
                          .trader_closeAllTrades_marketIsClosedDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () => toast.hidesToastMessage(),
                ),
              );
            } else if (processState
                is PositionOptionPositionsEmptyProcessState) {
              Navigator.of(listenerContext).pop();
            }
          },
          buildWhen:
              (previous, current) =>
                  previous.groupedPositions != current.groupedPositions,
          builder: (buildContext, state) {
            return switch (state.processState) {
              PositionOptionLoadingProcessState() => DuploShimmerList(
                hasLeading: false,
                itemCount: 5,
                height: 24,
              ),
              _ => Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (state.buyTrades case final buyTrades
                          when buyTrades.isNotEmpty)
                        Container(
                          padding: EdgeInsetsDirectional.only(
                            start: 10,
                            end: 8,
                          ),
                          color: theme.background.bgSecondary,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextChevronWidget(
                                semanticsIdentifier: 'all_buy_trades',
                                title: localization.trader_allBuyTrades,
                                backgroundColor: theme.background.bgSecondary,
                                onPressed: () {
                                  if (state.shouldHideClosePositionsDialog) {
                                    buildContext.read<PositionOptionBloc>().add(
                                      PositionOptionEvent.closeBuyTrades(
                                        localization.trader_closedAllBuyTrades,
                                      ),
                                    );
                                  } else {
                                    _showCloseTradeBottomSheet(
                                      buildContext,
                                      FilteredPositionType.buy,
                                    );
                                  }
                                },
                              ),
                              Divider(
                                height: 0,
                                color: theme.border.borderSecondary,
                                endIndent: 10,
                                indent: 10,
                              ),
                            ],
                          ),
                        ),
                      if (state.sellTrades case final sellTrades
                          when sellTrades.isNotEmpty)
                        Container(
                          padding: EdgeInsetsDirectional.only(
                            start: 10,
                            end: 8,
                          ),
                          color: theme.background.bgSecondary,
                          child: Column(
                            children: [
                              TextChevronWidget(
                                backgroundColor: theme.background.bgSecondary,
                                title: localization.trader_allSellTrades,
                                onPressed: () {
                                  if (state.shouldHideClosePositionsDialog) {
                                    buildContext.read<PositionOptionBloc>().add(
                                      PositionOptionEvent.closeSellTrades(
                                        localization.trader_closedAllSellTrades,
                                      ),
                                    );
                                  } else {
                                    _showCloseTradeBottomSheet(
                                      buildContext,
                                      FilteredPositionType.sell,
                                    );
                                  }
                                },
                              ),
                              Divider(
                                height: 0,
                                color: theme.border.borderSecondary,
                                endIndent: 10,
                                indent: 10,
                              ),
                            ],
                          ),
                        ),
                      if (state.losingTrades case final losingTrades
                          when losingTrades.isNotEmpty)
                        Container(
                          padding: EdgeInsetsDirectional.only(
                            start: 10,
                            end: 8,
                          ),
                          color: theme.background.bgSecondary,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextChevronWidget(
                                title: localization.trader_allLosingTrades,
                                backgroundColor: theme.background.bgSecondary,
                                onPressed: () {
                                  if (state.shouldHideClosePositionsDialog) {
                                    buildContext.read<PositionOptionBloc>().add(
                                      PositionOptionEvent.closeLosingTrades(
                                        localization
                                            .trader_closedAllLosingTrades,
                                      ),
                                    );
                                  } else {
                                    _showCloseTradeBottomSheet(
                                      buildContext,
                                      FilteredPositionType.losing,
                                    );
                                  }
                                },
                              ),
                              Divider(
                                height: 0,
                                color: theme.border.borderSecondary,
                                endIndent: 10,
                                indent: 10,
                              ),
                            ],
                          ),
                        ),
                      if (state.winningTrades case final winningTrades
                          when winningTrades.isNotEmpty)
                        Container(
                          padding: EdgeInsetsDirectional.only(
                            start: 10,
                            end: 8,
                          ),
                          color: theme.background.bgSecondary,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextChevronWidget(
                                title: localization.trader_allWinningTrades,
                                backgroundColor: theme.background.bgSecondary,
                                onPressed: () {
                                  if (state.shouldHideClosePositionsDialog) {
                                    buildContext.read<PositionOptionBloc>().add(
                                      PositionOptionEvent.closeWinningTrades(
                                        localization
                                            .trader_closedAllWinningTrades,
                                      ),
                                    );
                                  } else {
                                    _showCloseTradeBottomSheet(
                                      buildContext,
                                      FilteredPositionType.winning,
                                    );
                                  }
                                },
                              ),
                              Divider(
                                height: 0,
                                color: theme.border.borderSecondary,
                                endIndent: 10,
                                indent: 10,
                              ),
                            ],
                          ),
                        ),
                      if (state.groupedPositions?.positions ?? []
                          case final positions when positions.isNotEmpty)
                        Container(
                          padding: EdgeInsetsDirectional.only(
                            start: 10,
                            end: 8,
                          ),
                          color: theme.background.bgSecondary,
                          child: TextChevronWidget(
                            title: localization.trader_allTrades,
                            backgroundColor: theme.background.bgSecondary,
                            onPressed: () {
                              if (state.shouldHideClosePositionsDialog) {
                                buildContext.read<PositionOptionBloc>().add(
                                  PositionOptionEvent.closeAllTrades(
                                    localization.trader_closedAllTrades,
                                  ),
                                );
                              } else {
                                _showCloseTradeBottomSheet(
                                  buildContext,
                                  FilteredPositionType.all,
                                );
                              }
                            },
                          ),
                        ),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsetsDirectional.only(start: 12),
                        child: DuploText(
                          text:
                              "${EquitiFormatter.formatNumber(value: state.groupedPositions?.positions.length ?? 0, locale: Localizations.localeOf(context).toString())} ${localization.trader_open_trades}",
                          fontWeight: DuploFontWeight.semiBold,
                          style: context.duploTextStyles.textLg,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ...?state.groupedPositions?.positions.map((position) {
                    return DuploTap(
                      onTap: () {
                        showTradeOptionsSheet(
                          context: context,
                          position: position,
                          currency: state.accountCurrency,
                          platformName: position.platformName,
                        );
                      },
                      child: TradeTile(
                        digits: position.digits,
                        lots: position.lotSize,
                        tradeType: position.positionType,
                        profit: position.profit ?? 0.0,
                        priceChange:
                            position.positionType == TradeType.buy
                                ? position.buyPercentage
                                : position.sellPercentage,
                        currentPrice: position.currentPrice,
                        openPrice: position.openPrice,
                        tpValue: position.takeProfit,
                        slValue: position.stopLoss,
                        productName: state.groupedPositions!.tickerName,
                        productIcon: state.groupedPositions!.url,
                        currency: state.accountCurrency,
                      ),
                    );
                  }).toList(),
                  const SizedBox(height: 24),
                ],
              ),
            };
          },
        ),
      ],
    );
  }
}
