// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'position_option_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PositionOptionEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionEvent()';
}


}

/// @nodoc
class $PositionOptionEventCopyWith<$Res>  {
$PositionOptionEventCopyWith(PositionOptionEvent _, $Res Function(PositionOptionEvent) __);
}


/// @nodoc


class _OnSubscribeToGroupedPositions implements PositionOptionEvent {
  const _OnSubscribeToGroupedPositions(this.symbol);
  

 final  String symbol;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnSubscribeToGroupedPositionsCopyWith<_OnSubscribeToGroupedPositions> get copyWith => __$OnSubscribeToGroupedPositionsCopyWithImpl<_OnSubscribeToGroupedPositions>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnSubscribeToGroupedPositions&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,symbol);

@override
String toString() {
  return 'PositionOptionEvent.onSubscribeToGroupedPositions(symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$OnSubscribeToGroupedPositionsCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$OnSubscribeToGroupedPositionsCopyWith(_OnSubscribeToGroupedPositions value, $Res Function(_OnSubscribeToGroupedPositions) _then) = __$OnSubscribeToGroupedPositionsCopyWithImpl;
@useResult
$Res call({
 String symbol
});




}
/// @nodoc
class __$OnSubscribeToGroupedPositionsCopyWithImpl<$Res>
    implements _$OnSubscribeToGroupedPositionsCopyWith<$Res> {
  __$OnSubscribeToGroupedPositionsCopyWithImpl(this._self, this._then);

  final _OnSubscribeToGroupedPositions _self;
  final $Res Function(_OnSubscribeToGroupedPositions) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbol = null,}) {
  return _then(_OnSubscribeToGroupedPositions(
null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _NavigateToProductDetails implements PositionOptionEvent {
  const _NavigateToProductDetails({this.symbolDetail});
  

 final  SymbolDetailViewModel? symbolDetail;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NavigateToProductDetailsCopyWith<_NavigateToProductDetails> get copyWith => __$NavigateToProductDetailsCopyWithImpl<_NavigateToProductDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToProductDetails&&(identical(other.symbolDetail, symbolDetail) || other.symbolDetail == symbolDetail));
}


@override
int get hashCode => Object.hash(runtimeType,symbolDetail);

@override
String toString() {
  return 'PositionOptionEvent.navigateToProductDetails(symbolDetail: $symbolDetail)';
}


}

/// @nodoc
abstract mixin class _$NavigateToProductDetailsCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$NavigateToProductDetailsCopyWith(_NavigateToProductDetails value, $Res Function(_NavigateToProductDetails) _then) = __$NavigateToProductDetailsCopyWithImpl;
@useResult
$Res call({
 SymbolDetailViewModel? symbolDetail
});


$SymbolDetailViewModelCopyWith<$Res>? get symbolDetail;

}
/// @nodoc
class __$NavigateToProductDetailsCopyWithImpl<$Res>
    implements _$NavigateToProductDetailsCopyWith<$Res> {
  __$NavigateToProductDetailsCopyWithImpl(this._self, this._then);

  final _NavigateToProductDetails _self;
  final $Res Function(_NavigateToProductDetails) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolDetail = freezed,}) {
  return _then(_NavigateToProductDetails(
symbolDetail: freezed == symbolDetail ? _self.symbolDetail : symbolDetail // ignore: cast_nullable_to_non_nullable
as SymbolDetailViewModel?,
  ));
}

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolDetailViewModelCopyWith<$Res>? get symbolDetail {
    if (_self.symbolDetail == null) {
    return null;
  }

  return $SymbolDetailViewModelCopyWith<$Res>(_self.symbolDetail!, (value) {
    return _then(_self.copyWith(symbolDetail: value));
  });
}
}

/// @nodoc


class _CloseBuyTrades implements PositionOptionEvent {
  const _CloseBuyTrades(this.successMessageTitle);
  

 final  String successMessageTitle;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CloseBuyTradesCopyWith<_CloseBuyTrades> get copyWith => __$CloseBuyTradesCopyWithImpl<_CloseBuyTrades>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseBuyTrades&&(identical(other.successMessageTitle, successMessageTitle) || other.successMessageTitle == successMessageTitle));
}


@override
int get hashCode => Object.hash(runtimeType,successMessageTitle);

@override
String toString() {
  return 'PositionOptionEvent.closeBuyTrades(successMessageTitle: $successMessageTitle)';
}


}

/// @nodoc
abstract mixin class _$CloseBuyTradesCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$CloseBuyTradesCopyWith(_CloseBuyTrades value, $Res Function(_CloseBuyTrades) _then) = __$CloseBuyTradesCopyWithImpl;
@useResult
$Res call({
 String successMessageTitle
});




}
/// @nodoc
class __$CloseBuyTradesCopyWithImpl<$Res>
    implements _$CloseBuyTradesCopyWith<$Res> {
  __$CloseBuyTradesCopyWithImpl(this._self, this._then);

  final _CloseBuyTrades _self;
  final $Res Function(_CloseBuyTrades) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? successMessageTitle = null,}) {
  return _then(_CloseBuyTrades(
null == successMessageTitle ? _self.successMessageTitle : successMessageTitle // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _CloseSellTrades implements PositionOptionEvent {
  const _CloseSellTrades(this.successMessageTitle);
  

 final  String successMessageTitle;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CloseSellTradesCopyWith<_CloseSellTrades> get copyWith => __$CloseSellTradesCopyWithImpl<_CloseSellTrades>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseSellTrades&&(identical(other.successMessageTitle, successMessageTitle) || other.successMessageTitle == successMessageTitle));
}


@override
int get hashCode => Object.hash(runtimeType,successMessageTitle);

@override
String toString() {
  return 'PositionOptionEvent.closeSellTrades(successMessageTitle: $successMessageTitle)';
}


}

/// @nodoc
abstract mixin class _$CloseSellTradesCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$CloseSellTradesCopyWith(_CloseSellTrades value, $Res Function(_CloseSellTrades) _then) = __$CloseSellTradesCopyWithImpl;
@useResult
$Res call({
 String successMessageTitle
});




}
/// @nodoc
class __$CloseSellTradesCopyWithImpl<$Res>
    implements _$CloseSellTradesCopyWith<$Res> {
  __$CloseSellTradesCopyWithImpl(this._self, this._then);

  final _CloseSellTrades _self;
  final $Res Function(_CloseSellTrades) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? successMessageTitle = null,}) {
  return _then(_CloseSellTrades(
null == successMessageTitle ? _self.successMessageTitle : successMessageTitle // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _CloseLosingTrades implements PositionOptionEvent {
  const _CloseLosingTrades(this.successMessageTitle);
  

 final  String successMessageTitle;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CloseLosingTradesCopyWith<_CloseLosingTrades> get copyWith => __$CloseLosingTradesCopyWithImpl<_CloseLosingTrades>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseLosingTrades&&(identical(other.successMessageTitle, successMessageTitle) || other.successMessageTitle == successMessageTitle));
}


@override
int get hashCode => Object.hash(runtimeType,successMessageTitle);

@override
String toString() {
  return 'PositionOptionEvent.closeLosingTrades(successMessageTitle: $successMessageTitle)';
}


}

/// @nodoc
abstract mixin class _$CloseLosingTradesCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$CloseLosingTradesCopyWith(_CloseLosingTrades value, $Res Function(_CloseLosingTrades) _then) = __$CloseLosingTradesCopyWithImpl;
@useResult
$Res call({
 String successMessageTitle
});




}
/// @nodoc
class __$CloseLosingTradesCopyWithImpl<$Res>
    implements _$CloseLosingTradesCopyWith<$Res> {
  __$CloseLosingTradesCopyWithImpl(this._self, this._then);

  final _CloseLosingTrades _self;
  final $Res Function(_CloseLosingTrades) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? successMessageTitle = null,}) {
  return _then(_CloseLosingTrades(
null == successMessageTitle ? _self.successMessageTitle : successMessageTitle // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _CloseWinningTrades implements PositionOptionEvent {
  const _CloseWinningTrades(this.successMessageTitle);
  

 final  String successMessageTitle;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CloseWinningTradesCopyWith<_CloseWinningTrades> get copyWith => __$CloseWinningTradesCopyWithImpl<_CloseWinningTrades>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseWinningTrades&&(identical(other.successMessageTitle, successMessageTitle) || other.successMessageTitle == successMessageTitle));
}


@override
int get hashCode => Object.hash(runtimeType,successMessageTitle);

@override
String toString() {
  return 'PositionOptionEvent.closeWinningTrades(successMessageTitle: $successMessageTitle)';
}


}

/// @nodoc
abstract mixin class _$CloseWinningTradesCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$CloseWinningTradesCopyWith(_CloseWinningTrades value, $Res Function(_CloseWinningTrades) _then) = __$CloseWinningTradesCopyWithImpl;
@useResult
$Res call({
 String successMessageTitle
});




}
/// @nodoc
class __$CloseWinningTradesCopyWithImpl<$Res>
    implements _$CloseWinningTradesCopyWith<$Res> {
  __$CloseWinningTradesCopyWithImpl(this._self, this._then);

  final _CloseWinningTrades _self;
  final $Res Function(_CloseWinningTrades) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? successMessageTitle = null,}) {
  return _then(_CloseWinningTrades(
null == successMessageTitle ? _self.successMessageTitle : successMessageTitle // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _CloseAllTrades implements PositionOptionEvent {
  const _CloseAllTrades(this.successMessageTitle);
  

 final  String successMessageTitle;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CloseAllTradesCopyWith<_CloseAllTrades> get copyWith => __$CloseAllTradesCopyWithImpl<_CloseAllTrades>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseAllTrades&&(identical(other.successMessageTitle, successMessageTitle) || other.successMessageTitle == successMessageTitle));
}


@override
int get hashCode => Object.hash(runtimeType,successMessageTitle);

@override
String toString() {
  return 'PositionOptionEvent.closeAllTrades(successMessageTitle: $successMessageTitle)';
}


}

/// @nodoc
abstract mixin class _$CloseAllTradesCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$CloseAllTradesCopyWith(_CloseAllTrades value, $Res Function(_CloseAllTrades) _then) = __$CloseAllTradesCopyWithImpl;
@useResult
$Res call({
 String successMessageTitle
});




}
/// @nodoc
class __$CloseAllTradesCopyWithImpl<$Res>
    implements _$CloseAllTradesCopyWith<$Res> {
  __$CloseAllTradesCopyWithImpl(this._self, this._then);

  final _CloseAllTrades _self;
  final $Res Function(_CloseAllTrades) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? successMessageTitle = null,}) {
  return _then(_CloseAllTrades(
null == successMessageTitle ? _self.successMessageTitle : successMessageTitle // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ToggleClosePositionsDialogVisibility implements PositionOptionEvent {
  const _ToggleClosePositionsDialogVisibility(this.value);
  

 final  bool value;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ToggleClosePositionsDialogVisibilityCopyWith<_ToggleClosePositionsDialogVisibility> get copyWith => __$ToggleClosePositionsDialogVisibilityCopyWithImpl<_ToggleClosePositionsDialogVisibility>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToggleClosePositionsDialogVisibility&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,value);

@override
String toString() {
  return 'PositionOptionEvent.toggleClosePositionDialogVisibility(value: $value)';
}


}

/// @nodoc
abstract mixin class _$ToggleClosePositionsDialogVisibilityCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$ToggleClosePositionsDialogVisibilityCopyWith(_ToggleClosePositionsDialogVisibility value, $Res Function(_ToggleClosePositionsDialogVisibility) _then) = __$ToggleClosePositionsDialogVisibilityCopyWithImpl;
@useResult
$Res call({
 bool value
});




}
/// @nodoc
class __$ToggleClosePositionsDialogVisibilityCopyWithImpl<$Res>
    implements _$ToggleClosePositionsDialogVisibilityCopyWith<$Res> {
  __$ToggleClosePositionsDialogVisibilityCopyWithImpl(this._self, this._then);

  final _ToggleClosePositionsDialogVisibility _self;
  final $Res Function(_ToggleClosePositionsDialogVisibility) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? value = null,}) {
  return _then(_ToggleClosePositionsDialogVisibility(
null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _UpdatePositions implements PositionOptionEvent {
  const _UpdatePositions({required this.eventType, required this.symbol});
  

 final  EventType eventType;
 final  String symbol;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdatePositionsCopyWith<_UpdatePositions> get copyWith => __$UpdatePositionsCopyWithImpl<_UpdatePositions>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdatePositions&&(identical(other.eventType, eventType) || other.eventType == eventType)&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,eventType,symbol);

@override
String toString() {
  return 'PositionOptionEvent.updatePositions(eventType: $eventType, symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$UpdatePositionsCopyWith<$Res> implements $PositionOptionEventCopyWith<$Res> {
  factory _$UpdatePositionsCopyWith(_UpdatePositions value, $Res Function(_UpdatePositions) _then) = __$UpdatePositionsCopyWithImpl;
@useResult
$Res call({
 EventType eventType, String symbol
});




}
/// @nodoc
class __$UpdatePositionsCopyWithImpl<$Res>
    implements _$UpdatePositionsCopyWith<$Res> {
  __$UpdatePositionsCopyWithImpl(this._self, this._then);

  final _UpdatePositions _self;
  final $Res Function(_UpdatePositions) _then;

/// Create a copy of PositionOptionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? eventType = null,Object? symbol = null,}) {
  return _then(_UpdatePositions(
eventType: null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as EventType,symbol: null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$PositionOptionState {

 GroupedPositions? get groupedPositions; bool get shouldHideClosePositionsDialog; PositionOptionProcessState get processState; String? get accountCurrency;
/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PositionOptionStateCopyWith<PositionOptionState> get copyWith => _$PositionOptionStateCopyWithImpl<PositionOptionState>(this as PositionOptionState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionState&&(identical(other.groupedPositions, groupedPositions) || other.groupedPositions == groupedPositions)&&(identical(other.shouldHideClosePositionsDialog, shouldHideClosePositionsDialog) || other.shouldHideClosePositionsDialog == shouldHideClosePositionsDialog)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,groupedPositions,shouldHideClosePositionsDialog,processState,accountCurrency);

@override
String toString() {
  return 'PositionOptionState(groupedPositions: $groupedPositions, shouldHideClosePositionsDialog: $shouldHideClosePositionsDialog, processState: $processState, accountCurrency: $accountCurrency)';
}


}

/// @nodoc
abstract mixin class $PositionOptionStateCopyWith<$Res>  {
  factory $PositionOptionStateCopyWith(PositionOptionState value, $Res Function(PositionOptionState) _then) = _$PositionOptionStateCopyWithImpl;
@useResult
$Res call({
 GroupedPositions? groupedPositions, bool shouldHideClosePositionsDialog, PositionOptionProcessState processState, String? accountCurrency
});


$GroupedPositionsCopyWith<$Res>? get groupedPositions;$PositionOptionProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class _$PositionOptionStateCopyWithImpl<$Res>
    implements $PositionOptionStateCopyWith<$Res> {
  _$PositionOptionStateCopyWithImpl(this._self, this._then);

  final PositionOptionState _self;
  final $Res Function(PositionOptionState) _then;

/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? groupedPositions = freezed,Object? shouldHideClosePositionsDialog = null,Object? processState = null,Object? accountCurrency = freezed,}) {
  return _then(_self.copyWith(
groupedPositions: freezed == groupedPositions ? _self.groupedPositions : groupedPositions // ignore: cast_nullable_to_non_nullable
as GroupedPositions?,shouldHideClosePositionsDialog: null == shouldHideClosePositionsDialog ? _self.shouldHideClosePositionsDialog : shouldHideClosePositionsDialog // ignore: cast_nullable_to_non_nullable
as bool,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as PositionOptionProcessState,accountCurrency: freezed == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$GroupedPositionsCopyWith<$Res>? get groupedPositions {
    if (_self.groupedPositions == null) {
    return null;
  }

  return $GroupedPositionsCopyWith<$Res>(_self.groupedPositions!, (value) {
    return _then(_self.copyWith(groupedPositions: value));
  });
}/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionOptionProcessStateCopyWith<$Res> get processState {
  
  return $PositionOptionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}


/// @nodoc


class _PositionOptionState extends PositionOptionState {
   _PositionOptionState({this.groupedPositions, this.shouldHideClosePositionsDialog = false, this.processState = const PositionOptionProcessState.loading(), this.accountCurrency}): super._();
  

@override final  GroupedPositions? groupedPositions;
@override@JsonKey() final  bool shouldHideClosePositionsDialog;
@override@JsonKey() final  PositionOptionProcessState processState;
@override final  String? accountCurrency;

/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PositionOptionStateCopyWith<_PositionOptionState> get copyWith => __$PositionOptionStateCopyWithImpl<_PositionOptionState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PositionOptionState&&(identical(other.groupedPositions, groupedPositions) || other.groupedPositions == groupedPositions)&&(identical(other.shouldHideClosePositionsDialog, shouldHideClosePositionsDialog) || other.shouldHideClosePositionsDialog == shouldHideClosePositionsDialog)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,groupedPositions,shouldHideClosePositionsDialog,processState,accountCurrency);

@override
String toString() {
  return 'PositionOptionState(groupedPositions: $groupedPositions, shouldHideClosePositionsDialog: $shouldHideClosePositionsDialog, processState: $processState, accountCurrency: $accountCurrency)';
}


}

/// @nodoc
abstract mixin class _$PositionOptionStateCopyWith<$Res> implements $PositionOptionStateCopyWith<$Res> {
  factory _$PositionOptionStateCopyWith(_PositionOptionState value, $Res Function(_PositionOptionState) _then) = __$PositionOptionStateCopyWithImpl;
@override @useResult
$Res call({
 GroupedPositions? groupedPositions, bool shouldHideClosePositionsDialog, PositionOptionProcessState processState, String? accountCurrency
});


@override $GroupedPositionsCopyWith<$Res>? get groupedPositions;@override $PositionOptionProcessStateCopyWith<$Res> get processState;

}
/// @nodoc
class __$PositionOptionStateCopyWithImpl<$Res>
    implements _$PositionOptionStateCopyWith<$Res> {
  __$PositionOptionStateCopyWithImpl(this._self, this._then);

  final _PositionOptionState _self;
  final $Res Function(_PositionOptionState) _then;

/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? groupedPositions = freezed,Object? shouldHideClosePositionsDialog = null,Object? processState = null,Object? accountCurrency = freezed,}) {
  return _then(_PositionOptionState(
groupedPositions: freezed == groupedPositions ? _self.groupedPositions : groupedPositions // ignore: cast_nullable_to_non_nullable
as GroupedPositions?,shouldHideClosePositionsDialog: null == shouldHideClosePositionsDialog ? _self.shouldHideClosePositionsDialog : shouldHideClosePositionsDialog // ignore: cast_nullable_to_non_nullable
as bool,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as PositionOptionProcessState,accountCurrency: freezed == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$GroupedPositionsCopyWith<$Res>? get groupedPositions {
    if (_self.groupedPositions == null) {
    return null;
  }

  return $GroupedPositionsCopyWith<$Res>(_self.groupedPositions!, (value) {
    return _then(_self.copyWith(groupedPositions: value));
  });
}/// Create a copy of PositionOptionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionOptionProcessStateCopyWith<$Res> get processState {
  
  return $PositionOptionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}
}

/// @nodoc
mixin _$PositionOptionProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState()';
}


}

/// @nodoc
class $PositionOptionProcessStateCopyWith<$Res>  {
$PositionOptionProcessStateCopyWith(PositionOptionProcessState _, $Res Function(PositionOptionProcessState) __);
}


/// @nodoc


class PositionOptionLoadingProcessState implements PositionOptionProcessState {
  const PositionOptionLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.loading()';
}


}




/// @nodoc


class PositionOptionConnectedProcessState implements PositionOptionProcessState {
  const PositionOptionConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.connected()';
}


}




/// @nodoc


class PositionOptionErrorProcessState implements PositionOptionProcessState {
  const PositionOptionErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.error()';
}


}




/// @nodoc


class PositionOptionSuccessProcessState implements PositionOptionProcessState {
  const PositionOptionSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.success()';
}


}




/// @nodoc


class PositionOptionPositionsEmptyProcessState implements PositionOptionProcessState {
  const PositionOptionPositionsEmptyProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionPositionsEmptyProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.positionsEmpty()';
}


}




/// @nodoc


class PositionOptionMarketClosedProcessState implements PositionOptionProcessState {
  const PositionOptionMarketClosedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionMarketClosedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.marketClosed()';
}


}




/// @nodoc


class PositionOptionClosePositionsSuccessProcessState implements PositionOptionProcessState {
  const PositionOptionClosePositionsSuccessProcessState({required this.symbolName, required this.url, required this.lotSize, required this.price, required this.digits, required this.tradeType, required this.successMessageTitle});
  

 final  String symbolName;
 final  String url;
 final  double lotSize;
 final  double price;
 final  int digits;
 final  TradeType? tradeType;
 final  String successMessageTitle;

/// Create a copy of PositionOptionProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PositionOptionClosePositionsSuccessProcessStateCopyWith<PositionOptionClosePositionsSuccessProcessState> get copyWith => _$PositionOptionClosePositionsSuccessProcessStateCopyWithImpl<PositionOptionClosePositionsSuccessProcessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionClosePositionsSuccessProcessState&&(identical(other.symbolName, symbolName) || other.symbolName == symbolName)&&(identical(other.url, url) || other.url == url)&&(identical(other.lotSize, lotSize) || other.lotSize == lotSize)&&(identical(other.price, price) || other.price == price)&&(identical(other.digits, digits) || other.digits == digits)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType)&&(identical(other.successMessageTitle, successMessageTitle) || other.successMessageTitle == successMessageTitle));
}


@override
int get hashCode => Object.hash(runtimeType,symbolName,url,lotSize,price,digits,tradeType,successMessageTitle);

@override
String toString() {
  return 'PositionOptionProcessState.closePositionsSuccess(symbolName: $symbolName, url: $url, lotSize: $lotSize, price: $price, digits: $digits, tradeType: $tradeType, successMessageTitle: $successMessageTitle)';
}


}

/// @nodoc
abstract mixin class $PositionOptionClosePositionsSuccessProcessStateCopyWith<$Res> implements $PositionOptionProcessStateCopyWith<$Res> {
  factory $PositionOptionClosePositionsSuccessProcessStateCopyWith(PositionOptionClosePositionsSuccessProcessState value, $Res Function(PositionOptionClosePositionsSuccessProcessState) _then) = _$PositionOptionClosePositionsSuccessProcessStateCopyWithImpl;
@useResult
$Res call({
 String symbolName, String url, double lotSize, double price, int digits, TradeType? tradeType, String successMessageTitle
});




}
/// @nodoc
class _$PositionOptionClosePositionsSuccessProcessStateCopyWithImpl<$Res>
    implements $PositionOptionClosePositionsSuccessProcessStateCopyWith<$Res> {
  _$PositionOptionClosePositionsSuccessProcessStateCopyWithImpl(this._self, this._then);

  final PositionOptionClosePositionsSuccessProcessState _self;
  final $Res Function(PositionOptionClosePositionsSuccessProcessState) _then;

/// Create a copy of PositionOptionProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbolName = null,Object? url = null,Object? lotSize = null,Object? price = null,Object? digits = null,Object? tradeType = freezed,Object? successMessageTitle = null,}) {
  return _then(PositionOptionClosePositionsSuccessProcessState(
symbolName: null == symbolName ? _self.symbolName : symbolName // ignore: cast_nullable_to_non_nullable
as String,url: null == url ? _self.url : url // ignore: cast_nullable_to_non_nullable
as String,lotSize: null == lotSize ? _self.lotSize : lotSize // ignore: cast_nullable_to_non_nullable
as double,price: null == price ? _self.price : price // ignore: cast_nullable_to_non_nullable
as double,digits: null == digits ? _self.digits : digits // ignore: cast_nullable_to_non_nullable
as int,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,successMessageTitle: null == successMessageTitle ? _self.successMessageTitle : successMessageTitle // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class PositionOptionClosePositionsErrorProcessState implements PositionOptionProcessState {
  const PositionOptionClosePositionsErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionOptionClosePositionsErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionOptionProcessState.closePositionsError()';
}


}




// dart format on
