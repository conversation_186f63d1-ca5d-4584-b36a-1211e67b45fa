import 'dart:async';

import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/model/grouped_positions.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/close_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_close_positions_dialog_use_case.dart';
import 'package:e_trader/src/domain/usecase/should_show_close_positions_dialog_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_grouped_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'position_option_bloc.freezed.dart';
part 'position_option_event.dart';
part 'position_option_state.dart';

class PositionOptionBloc
    extends Bloc<PositionOptionEvent, PositionOptionState> {
  PositionOptionBloc(
    this._subscribeToGroupedPositionsUseCase,
    this._getAccountNumberUseCase,
    this._closeTradeUseCase,
    this._shouldShowClosePositionsDialogUseCase,
    this._saveClosePositionsDialogUseCase,
    this._equitiTraderNavigation,
    this._getSelectedAccountUseCase,
    this._tradingAnalyticsEvent,
    this._updatePositionsUseCase,
  ) : super(PositionOptionState()) {
    on<PositionOptionEvent>(
      (event, emit) => switch (event) {
        _OnSubscribeToGroupedPositions val => _onSubscribeToPosition(val, emit),
        _NavigateToProductDetails val => _navigateToProductDetails(val),
        _CloseBuyTrades() => _closeBuyTrades(emit, event),
        _CloseSellTrades() => _closeSellTrades(emit, event),
        _CloseLosingTrades() => _closeLosingTrades(emit, event),
        _CloseWinningTrades() => _closeWinningTrades(emit, event),
        _CloseAllTrades() => _closeAllTrades(emit, event),
        _ToggleClosePositionsDialogVisibility val =>
          _toggleClosePositionDialogVisibility(val, emit),
        _UpdatePositions val => _onUpdatePositions(val, emit),
      },
    );
  }
  final SubscribeToGroupedPositionsUseCase _subscribeToGroupedPositionsUseCase;
  final ShouldShowClosePositionsDialogUseCase
  _shouldShowClosePositionsDialogUseCase;
  final SaveClosePositionsDialogUseCase _saveClosePositionsDialogUseCase;
  final CloseTradeUseCase _closeTradeUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final EquitiTraderNavigation _equitiTraderNavigation;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final TradingAnalytics _tradingAnalyticsEvent;
  final UpdatePositionsUseCase _updatePositionsUseCase;

  FutureOr<void> _onUpdatePositions(
    _UpdatePositions event,
    Emitter<PositionOptionState> _,
  ) => _updatePositionsUseCase(
    eventType: event.eventType,
    symbolName: event.symbol,
  );

  FutureOr<void> _navigateToProductDetails(_NavigateToProductDetails event) {
    return _getAccountNumberUseCase().fold(
      (error) => addError(error),
      (accountNumber) => _equitiTraderNavigation.navigateToProductDetail(
        accountNumber: accountNumber,
        symbolDetail: event.symbolDetail!,
      ),
    );
  }

  FutureOr<void> _onSubscribeToPosition(
    _OnSubscribeToGroupedPositions event,
    Emitter<PositionOptionState> emit,
  ) async {
    emit(
      state.copyWith(processState: const PositionOptionProcessState.loading()),
    );
    final interactionId = await _tradingAnalyticsEvent.startInteraction(
      TradeAnalyticsEvent.loadPortfolioPositionDetails.eventName,
    );
    _tradingAnalyticsEvent.loadPortfolioPositionDetails(interactionId);

    if (state.accountCurrency == null) {
      final accountCurrency =
          _getSelectedAccountUseCase()?.homeCurrency ?? "USD";
      emit(state.copyWith(accountCurrency: accountCurrency));
    }
    final result =
        await _getAccountNumberUseCase()
            .chainFirst((accountNumber) {
              final value = _shouldShowClosePositionsDialogUseCase();
              emit(state.copyWith(shouldHideClosePositionsDialog: value));
              return Either.of(accountNumber);
            })
            .toTaskEither()
            .flatMap(
              (accountNumber) =>
                  TaskEither<Exception, Stream<List<GroupedPositions>>>.Do((
                    $,
                  ) async {
                    final positionsResult = await $(
                      _subscribeToGroupedPositionsUseCase(
                        accountNumber: accountNumber,
                        symbolName: event.symbol,
                        subscriberId: '${PositionOptionBloc}_$hashCode',
                        eventType: TradingSocketEvent.positions.register,
                      ),
                    );
                    return positionsResult;
                  }),
            )
            .run();

    await result.fold(
      (error) async {
        addError(error, StackTrace.current);
        emit(
          state.copyWith(
            processState: const PositionOptionProcessState.error(),
          ),
        );
      },
      (positionsStream) async {
        _tradingAnalyticsEvent.doneLoadingPositionDetails(
          event.symbol,
          interactionId,
        );
        emit(
          state.copyWith(
            processState: const PositionOptionProcessState.connected(),
          ),
        );
        return emit.forEach<List<GroupedPositions>>(
          positionsStream,
          onData: (groupedPositionList) {
            if (groupedPositionList.isEmpty) {
              return state.copyWith(
                groupedPositions: null,
                processState: const PositionOptionProcessState.positionsEmpty(),
              );
            } else
              for (var groupedPosition in groupedPositionList) {
                if (groupedPosition.platformName == event.symbol) {
                  final sortedPositions = List<PositionModel>.of(
                    groupedPosition.positions,
                  )..sort((a, b) => b.positionId.compareTo(a.positionId));

                  final sortedGroupedPosition = groupedPosition.copyWith(
                    positions: sortedPositions,
                  );
                  return state.copyWith(
                    groupedPositions: sortedGroupedPosition,
                    processState: const PositionOptionProcessState.success(),
                  );
                }
              }
            return state.copyWith(
              processState: const PositionOptionProcessState.error(),
            );
          },
          onError: (error, stackTrace) {
            addError(error, stackTrace);
            _tradingAnalyticsEvent.failedLoadingPositionDetails(
              error.toString(),
              interactionId,
            );
            return state.copyWith(
              processState: const PositionOptionProcessState.error(),
            );
          },
        );
      },
    );
    _tradingAnalyticsEvent.endInteraction(interactionId);
  }

  FutureOr<void> _closeBuyTrades(
    Emitter<PositionOptionState> emit,
    _CloseBuyTrades event,
  ) async {
    final buyTrades = state.groupedPositions?.buyTrades ?? [];
    await _closeTrades(
      emit,
      (groupedPositions) => buyTrades,
      state.groupedPositions?.totalBuyLotSize ?? 0,
      state.groupedPositions?.totalBuyPrice ?? 0,
      TradeType.buy,
      buyTrades.firstOrNull?.digits ?? 0,
      event.successMessageTitle,
      state.groupedPositions?.platformName ?? '',
    );
  }

  FutureOr<void> _closeSellTrades(
    Emitter<PositionOptionState> emit,
    _CloseSellTrades event,
  ) async {
    final sellTrades = state.groupedPositions?.sellTrades ?? [];
    await _closeTrades(
      emit,
      (groupedPositions) => sellTrades,
      state.groupedPositions?.totalSellLotSize ?? 0,
      state.groupedPositions?.totalSellPrice ?? 0,
      TradeType.sell,
      sellTrades.firstOrNull?.digits ?? 0,
      event.successMessageTitle,
      state.groupedPositions?.platformName ?? '',
    );
  }

  FutureOr<void> _closeLosingTrades(
    Emitter<PositionOptionState> emit,
    _CloseLosingTrades event,
  ) async {
    final losingTrades = state.groupedPositions?.losingTrades ?? [];
    final tradeType =
        losingTrades.isEmpty
            ? null
            : (losingTrades.every(
              (trade) =>
                  trade.positionType == losingTrades.firstOrNull?.positionType,
            ))
            ? losingTrades.firstOrNull?.positionType
            : null;

    await _closeTrades(
      emit,
      (groupedPositions) => losingTrades,
      state.groupedPositions?.totalLosingLotSize ?? 0,
      state.groupedPositions?.totalLosingPrice ?? 0,
      tradeType,
      losingTrades.firstOrNull?.digits ?? 0,
      event.successMessageTitle,
      state.groupedPositions?.platformName ?? '',
    );
  }

  FutureOr<void> _closeWinningTrades(
    Emitter<PositionOptionState> emit,
    _CloseWinningTrades event,
  ) async {
    final winningTrades = state.groupedPositions?.winningTrades ?? [];
    final tradeType =
        winningTrades.isEmpty
            ? null
            : (winningTrades.every(
              (trade) =>
                  trade.positionType == winningTrades.firstOrNull?.positionType,
            ))
            ? winningTrades.firstOrNull?.positionType
            : null;

    await _closeTrades(
      emit,
      (groupedPositions) => groupedPositions?.winningTrades ?? [],
      state.groupedPositions?.totalWinningLotSize ?? 0,
      state.groupedPositions?.totalWinningPrice ?? 0,
      tradeType,
      winningTrades.firstOrNull?.digits ?? 0,
      event.successMessageTitle,
      state.groupedPositions?.platformName ?? '',
    );
  }

  FutureOr<void> _closeAllTrades(
    Emitter<PositionOptionState> emit,
    _CloseAllTrades event,
  ) async {
    final allTrades = state.groupedPositions?.positions ?? [];
    final tradeType =
        allTrades.isEmpty
            ? null
            : (allTrades.every(
              (trade) =>
                  trade.positionType == allTrades.firstOrNull?.positionType,
            ))
            ? allTrades.firstOrNull?.positionType
            : null;

    await _closeTrades(
      emit,
      (groupedPositions) => groupedPositions?.positions ?? [],
      state.groupedPositions?.totalLotSize ?? 0,
      state.groupedPositions?.price ?? 0,
      tradeType,
      allTrades.firstOrNull?.digits ?? 0,
      event.successMessageTitle,
      state.groupedPositions?.platformName ?? '',
    );
  }

  FutureOr<void> _closeTrades(
    Emitter<PositionOptionState> emit,
    List<PositionModel> Function(GroupedPositions? groupedPositions)
    getTradesList,
    double lotSize,
    double price,
    TradeType? tradeType,
    int digits,
    String successMessageTitle,
    String symbolName,
  ) async {
    final trades = getTradesList(state.groupedPositions);
    final tickerName = state.groupedPositions?.tickerName ?? '';
    final symbolUrl = state.groupedPositions?.url ?? '';
    final interactionId = await _tradingAnalyticsEvent.startInteraction(
      TradeAnalyticsEvent.startCloseTrade.eventName,
    );
    final accountNumberEither = _getAccountNumberUseCase();
    final accountNumber = accountNumberEither.fold(
      (exception) => throw exception,
      (account) => account,
    );

    final closeTradeRequestModel = CloseTradeRequestModel(
      accountNumber: accountNumber,
      positions:
          trades
              .map(
                (trade) => ClosePositionItemModel(
                  id: trade.positionId,
                  volume: trade.volume,
                ),
              )
              .toList(),
    );

    _tradingAnalyticsEvent.startCloseTrade(
      closeTradeRequestModel,
      CloseTradeType.close,
      symbolName,
      interactionId,
    );
    _saveClosePositionsDialogUseCase(
      value: state.shouldHideClosePositionsDialog,
    );
    final result = await _closeTradeUseCase(closeTradeRequestModel).run();

    result.fold(
      (exception) {
        if (exception is PositionsAndOrdersException &&
            exception.errors.containsKey("IsMarketOpen")) {
          final isMarketOpenError =
              exception.errors["IsMarketOpen"]?.firstOrNull;
          if (isMarketOpenError?.toLowerCase() == "false") {
            emit(
              state.copyWith(
                processState: PositionOptionProcessState.marketClosed(),
              ),
            );
            return;
          }
        } else {
          emit(
            state.copyWith(
              processState: PositionOptionProcessState.closePositionsError(),
            ),
          );
          emit(
            state.copyWith(
              processState: const PositionOptionProcessState.error(),
            ),
          );
        }

        addError(exception);
        _tradingAnalyticsEvent.closeTradeResult(
          null,
          CloseTradeType.close.name,
          symbolName,
          interactionId,
          exception.toString(),
        );
      },
      (r) {
        final updatedPositions = _getUpdatedGroupedPositionsAfterClose(
          state.groupedPositions,
          trades,
        );
        _tradingAnalyticsEvent.closeTradeResult(
          r,
          CloseTradeType.close.name,
          symbolName,
          interactionId,
          null,
        );

        emit(
          state.copyWith(
            processState: PositionOptionProcessState.closePositionsSuccess(
              symbolName: tickerName,
              url: symbolUrl,
              lotSize: lotSize,
              price: price,
              tradeType: tradeType,
              digits: digits,
              successMessageTitle: successMessageTitle,
            ),
            groupedPositions: updatedPositions,
          ),
        );

        emit(
          state.copyWith(
            processState: const PositionOptionProcessState.success(),
          ),
        );
      },
    );
    _tradingAnalyticsEvent.endInteraction(interactionId);
  }

  GroupedPositions? _getUpdatedGroupedPositionsAfterClose(
    GroupedPositions? grouped,
    List<PositionModel> closedTrades,
  ) {
    final closedIds = closedTrades.map((t) => t.positionId).toSet();

    final updatedPositions =
        grouped?.positions
            .where((pos) => !closedIds.contains(pos.positionId))
            .toList();
    return grouped?.copyWith(positions: updatedPositions ?? []);
  }

  FutureOr<void> _toggleClosePositionDialogVisibility(
    _ToggleClosePositionsDialogVisibility val,
    Emitter<PositionOptionState> emit,
  ) {
    emit(state.copyWith(shouldHideClosePositionsDialog: val.value));
  }
}
