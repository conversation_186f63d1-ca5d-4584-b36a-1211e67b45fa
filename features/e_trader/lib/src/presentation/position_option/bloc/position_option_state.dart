part of 'position_option_bloc.dart';

@freezed
sealed class PositionOptionState with _$PositionOptionState {
  const PositionOptionState._();
  factory PositionOptionState({
    GroupedPositions? groupedPositions,
    @Default(false) bool shouldHideClosePositionsDialog,
    @Default(PositionOptionProcessState.loading())
    PositionOptionProcessState processState,
    String? accountCurrency,
  }) = _PositionOptionState;

  List<PositionModel> get buyTrades => groupedPositions?.buyTrades ?? [];
  List<PositionModel> get sellTrades => groupedPositions?.sellTrades ?? [];
  List<PositionModel> get losingTrades => groupedPositions?.losingTrades ?? [];
  List<PositionModel> get winningTrades =>
      groupedPositions?.winningTrades ?? [];
}

@freezed
sealed class PositionOptionProcessState with _$PositionOptionProcessState {
  const factory PositionOptionProcessState.loading() =
      PositionOptionLoadingProcessState;
  const factory PositionOptionProcessState.connected() =
      PositionOptionConnectedProcessState;
  const factory PositionOptionProcessState.error() =
      PositionOptionErrorProcessState;
  const factory PositionOptionProcessState.success() =
      PositionOptionSuccessProcessState;
  const factory PositionOptionProcessState.positionsEmpty() =
      PositionOptionPositionsEmptyProcessState;
  const factory PositionOptionProcessState.marketClosed() =
      PositionOptionMarketClosedProcessState;
  const factory PositionOptionProcessState.closePositionsSuccess({
    required String symbolName,
    required String url,
    required double lotSize,
    required double price,
    required int digits,
    required TradeType? tradeType,
    required String successMessageTitle,
  }) = PositionOptionClosePositionsSuccessProcessState;
  const factory PositionOptionProcessState.closePositionsError() =
      PositionOptionClosePositionsErrorProcessState;
}
