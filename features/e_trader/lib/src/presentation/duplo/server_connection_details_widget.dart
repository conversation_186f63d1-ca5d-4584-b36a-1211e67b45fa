import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:domain/domain.dart';

import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ServerConnectionDetailsWidget extends StatelessWidget {
  const ServerConnectionDetailsWidget({
    super.key,
    required this.tradingAccountModel,
    required this.title,
    required this.subtitle,
  });

  final TradingAccountModel tradingAccountModel;
  final DuploText title;
  final DuploText subtitle;

  void _showCopiedSnackBar(BuildContext context) {
    if (Platform.isIOS) {
      final localization = EquitiLocalization.of(context);
      final theme = context.duploTheme;
      final duploTextStyle = context.duploTextStyles;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: DuploText(
            text: localization.duplo_copied,
            style: duploTextStyle.textSm,
            fontWeight: DuploFontWeight.medium,
            color: theme.foreground.fgWhite,
          ),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          title,
          subtitle,
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: theme.background.bgPrimary,
              borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Builder(
                  builder:
                      (ctx) => _ServerDetailItemWidget(
                        title: localization.trader_server,
                        subTitle: tradingAccountModel.serverName,
                        onTap: () {
                          Clipboard.setData(
                            ClipboardData(text: tradingAccountModel.serverName),
                          );
                          _showCopiedSnackBar(ctx);
                        },
                      ),
                ),
                Builder(
                  builder:
                      (ctx) => _ServerDetailItemWidget(
                        title: localization.trader_login(
                          tradingAccountModel.platformType.displayName,
                        ),
                        subTitle: tradingAccountModel.accountNumber,
                        onTap: () {
                          Clipboard.setData(
                            ClipboardData(
                              text: tradingAccountModel.accountNumber,
                            ),
                          );
                          _showCopiedSnackBar(ctx);
                        },
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ServerDetailItemWidget extends StatelessWidget {
  const _ServerDetailItemWidget({
    required this.title,
    required this.subTitle,
    required this.onTap,
  });

  final String title;
  final String subTitle;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    return DuploTap(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  DuploText(
                    text: title,
                    style: textStyles.textXs,
                    color: theme.text.textSecondary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  DuploText(
                    text: subTitle,
                    style: textStyles.textXs,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textSecondary,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            trader.Assets.images.copy.svg(
              height: 24,
              width: 24,
              colorFilter: ColorFilter.mode(
                theme.foreground.fgSecondary,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
