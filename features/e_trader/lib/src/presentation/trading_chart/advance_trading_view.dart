import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:e_trader/src/presentation/trading_chart/bloc/trading_chart_view_bloc.dart';
import 'package:e_trader/src/presentation/trading_chart/full_screen_portal.dart';
import 'package:e_trader/src/presentation/trading_chart/widgets/chart_state/chart_error_view.dart';
import 'package:e_trader/src/presentation/trading_chart/widgets/web_view/chart_webview.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AdvanceTradingView extends StatefulWidget {
  final String platformName;
  final String tickerName;
  final int digit;
  final SheetController? sheetController;
  final bool interactionsEnabled;
  final TabController? tabController;
  final int? tabIndex;
  final TabSubscriptionManager? tabSubscriptionManager;

  const AdvanceTradingView({
    super.key,
    required this.platformName,
    required this.tickerName,
    required this.digit,
    this.sheetController,
    required this.interactionsEnabled,
    this.tabController,
    this.tabIndex,
    this.tabSubscriptionManager,
  });

  @override
  State<AdvanceTradingView> createState() => _AdvanceTradingViewState();
}

class _AdvanceTradingViewState extends State<AdvanceTradingView>
    with
        WidgetsBindingObserver,
        PerformanceObserverMixin,
        AutomaticKeepAliveClientMixin {
  final GlobalKey<ChartWebviewState> chartKey = GlobalKey();

  Widget? _dynamicWidget;
  final portalController = FullscreenPortalController();
  bool fullScreenMode = false;
  bool _chartIsVisible = true;

  Orientation? lastOrientation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setupSheetListener();
  }

  void _setupSheetListener() {
    if (widget.sheetController != null) {
      widget.sheetController!.addListener(_onSheetChanged);
    }
  }

  void _onSheetChanged() {
    final isSheetExpanded = _isSheetFullyExpanded();
    final shouldAllowFullscreen = !isSheetExpanded;

    if (_chartIsVisible != shouldAllowFullscreen) {
      _chartIsVisible = shouldAllowFullscreen;
      changeFullScreenMode(shouldAllowFullscreen);
    }
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    // Only handle route changes if this is the currently selected tab
    if (widget.tabController != null && widget.tabIndex != null) {
      if (widget.tabController!.indexIsChanging) return;
      if (widget.tabController!.index != widget.tabIndex) return;
    }
    changeFullScreenMode(true);
    super.onRoutePopped(route);
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    // Only handle route changes if this is the currently selected tab
    if (widget.tabController != null && widget.tabIndex != null) {
      if (widget.tabController!.indexIsChanging) return;
      if (widget.tabController!.index != widget.tabIndex) return;
    }
    changeFullScreenMode(false);
    super.onRoutePushed(route);
  }

  void changeFullScreenMode(bool newFullScreenMode) {
    if (fullScreenMode != newFullScreenMode) {
      fullScreenMode = newFullScreenMode;
      if (fullScreenMode) {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeRight,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.portraitUp,
        ]);
      } else {
        SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      }
    }
  }

  bool _isSheetFullyExpanded() {
    if (widget.sheetController == null) return false;

    final metrics = widget.sheetController!.metrics;
    if (metrics == null) return false;

    final currentOffset = metrics.offset;
    final maxOffset = metrics.maxOffset;

    // Consider the sheet fully expanded if it's at 90% or more of max offset
    // This provides a small buffer for floating point precision
    return currentOffset >= (maxOffset * 0.9);
  }

  @override
  void dispose() {
    widget.sheetController?.removeListener(_onSheetChanged);
    changeFullScreenMode(false);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final orientation = MediaQuery.orientationOf(context);
    if (lastOrientation != orientation) {
      switch (orientation) {
        case Orientation.landscape:
          portalController.enter(context);
        case Orientation.portrait:
          portalController.exit();
      }
      lastOrientation = orientation;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = context.duploTheme;
    return BlocProvider(
      create:
          (createContext) =>
              diContainer<TradingChartViewBloc>()..add(
                TradingChartViewEvent.onGetInitialSetting(widget.platformName),
              ),
      child: BlocConsumer<TradingChartViewBloc, TradingChartViewState>(
        listener: (listnerCtx, state) {
          if (widget.tabController?.indexIsChanging ?? false) return;
          if (widget.tabController?.index != widget.tabIndex) return;
          chartKey.currentState?.handleParentStateChange();
        },
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          if (state.processState == ChartViewProcessState.error()) {
            return ChartErrorView(
              onReload:
                  () => builderContext.read<TradingChartViewBloc>().add(
                    TradingChartViewEvent.onGetInitialSetting(
                      widget.platformName,
                    ),
                  ),
            );
          } else if (state.processState == ChartViewProcessState.loading()) {
            return Center(
              child: CupertinoActivityIndicator(
                color: theme.foreground.fgPrimary,
                radius: 16,
              ),
            );
          }
          if (_dynamicWidget == null) {
            if (Platform.environment.containsKey('FLUTTER_TEST')) {
              _dynamicWidget = Container(color: Colors.yellow);
            } else {
              _dynamicWidget = ChartWebview(
                key: chartKey,
                accountNumber: state.accountNumber,
                platformName: widget.platformName,
                tickerName: widget.tickerName,
                digits: widget.digit,
                chartType: state.chartType,
                resolution: state.resolution,
                showPositions: state.isPositionToggle,
                tabSubscriptionManager: widget.tabSubscriptionManager!,
                tabIndex: widget.tabIndex ?? 0,
                tabController: widget.tabController!,
                onResolutionChanged: (newResolution) {
                  // Handle resolution change from TradingView
                  builderContext.read<TradingChartViewBloc>().add(
                    TradingChartViewEvent.onChangeResolution(newResolution),
                  );
                },
                onChartTypeChanged: (newChartType) {
                  // Handle chart type change from TradingView
                  builderContext.read<TradingChartViewBloc>().add(
                    TradingChartViewEvent.onChangeChartType(newChartType),
                  );
                },
                onPositionToggleChanged: (newPositionToggle) {
                  builderContext.read<TradingChartViewBloc>().add(
                    TradingChartViewEvent.onShowHidePositions(
                      newPositionToggle,
                    ),
                  );
                },
                onError: () {
                  builderContext.read<TradingChartViewBloc>().add(
                    TradingChartViewEvent.onError(),
                  );
                },
              );
            }
          } else {
            chartKey.currentState?.updateConfig(
              chartType: state.chartType,
              resolution: state.resolution,
              interactionsEnabled: widget.interactionsEnabled,
              toggle: state.isPositionToggle,
            );
          }

          return VisibilityDetector(
            key: const Key('advance_trading_view'),
            onVisibilityChanged: (info) {
              // Only update fullscreen mode based on actual chart visibility
              // Sheet state is handled separately by the sheet listener
              if (info.visibleFraction < 1) {
                changeFullScreenMode(false);
              } else if (_chartIsVisible) {
                // Only allow fullscreen if chart is visible and sheet allows it
                changeFullScreenMode(true);
              }
            },
            child: FullscreenPortal(
              controller: portalController,
              child: Container(child: _dynamicWidget),
              fullscreenBuilder:
                  (exitFullscreen) => Scaffold(
                    backgroundColor: theme.background.bgPrimary,
                    body: Container(
                      color: theme.background.bgPrimary,
                      child: SafeArea(child: _dynamicWidget!),
                    ),
                  ),
            ),
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
