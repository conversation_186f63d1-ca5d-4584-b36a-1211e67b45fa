// ignore_for_file: prefer-number-format

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:io';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:e_trader/src/presentation/trading_chart/bloc/trading_chart_view_bloc.dart';
import 'package:e_trader/src/presentation/trading_chart/widgets/web_view/close_position_sheet.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:url_launcher/url_launcher.dart';

class ChartWebview extends StatefulWidget {
  final String accountNumber;
  final int digits;
  final String tickerName;
  final String platformName;
  final String resolution;
  final bool showPositions;
  final int chartType;
  final int tabIndex;
  final TabController tabController;
  final TabSubscriptionManager tabSubscriptionManager;

  final void Function(ChartViewProcessState)? onChangeChartState;
  final void Function(String)? onResolutionChanged;
  final void Function(int)? onChartTypeChanged;
  final void Function(bool)? onPositionToggleChanged;
  final VoidCallback? onParentStateChange;
  final VoidCallback? onError;

  const ChartWebview({
    super.key,
    required this.accountNumber,
    required this.tickerName,
    required this.platformName,
    required this.digits,
    required this.chartType,
    required this.resolution,
    required this.showPositions,
    this.onChangeChartState,
    this.onResolutionChanged,
    this.onPositionToggleChanged,
    this.onError,
    this.onChartTypeChanged,
    required this.tabSubscriptionManager,
    required this.tabIndex,
    required this.tabController,
    this.onParentStateChange,
  });

  @override
  State<ChartWebview> createState() => ChartWebviewState();
}

class ChartWebviewState extends State<ChartWebview>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  @override
  bool get wantKeepAlive => true;

  String _timeZone = 'Etc/UTC';
  late int _currentChartType;
  late String _currentResolution;
  late bool _currentPositionToggle;
  late bool _interactionsEnabled;
  late TradingChartViewBloc _tradingChartViewBloc;
  bool _isInBackground = false;
  bool _hasSubscribed = false;
  DuploToast? toast;
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    _tradingChartViewBloc = context.read<TradingChartViewBloc>();
    _currentChartType = widget.chartType;
    _currentResolution = widget.resolution;
    _currentPositionToggle = false;
    _interactionsEnabled = true;
    _localhostServerFuture = _startLocalhostServer();
    widget.tabSubscriptionManager.registerTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _subscribe,
      onUnsubscribe: _unsubscribe,
      onFirstVisit:
          () => _tradingChartViewBloc.add(
            TradingChartViewEvent.onLoadPositions(
              platformName: widget.platformName,
            ),
          ),
    );
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      _tradingChartViewBloc.add(
        TradingChartViewEvent.updatePositions(
          eventType: TradingSocketEvent.positions.subscribe,
          platformName: widget.platformName,
        ),
      );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdatePositionsUseCase>().call(
          eventType: TradingSocketEvent.positions.unsubscribe,
          symbolName: widget.platformName,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }

  void handleParentStateChange() {
    _subscribe();
    displayPositions();
  }

  void updateConfig({
    required int chartType,
    required String resolution,
    required bool toggle,
    required bool interactionsEnabled,
  }) {
    if (_currentChartType != chartType) {
      _currentChartType = chartType;
      _evaluateJavaScript(
        source: """
        window.tvWidget.chart().setChartType($chartType);
      """,
      );
    }

    if (_currentResolution != resolution) {
      _currentResolution = resolution;
      _evaluateJavaScript(
        source: """
        window.tvWidget.chart().setResolution("$_currentResolution");
  """,
      );
    }
    if (_currentPositionToggle != toggle) {
      _currentPositionToggle = toggle;
    }

    if (_interactionsEnabled != interactionsEnabled) {
      _interactionsEnabled = interactionsEnabled;
      _evaluateJavaScript(
        source: """
        // Enable/disable chart interactions
        if (window.tvWidget && window.tvWidget.chart) {
          const chartContainer = document.querySelector('#tv_chart_container') ||
                                document.querySelector('.tradingview-widget-container') ||
                                document.body;

          if (${_interactionsEnabled ? 'true' : 'false'}) {
            // Enable interactions
            if (chartContainer) {
              chartContainer.style.pointerEvents = 'auto';
              chartContainer.style.userSelect = 'auto';
              chartContainer.style.touchAction = 'auto';
              chartContainer.style.touchEnd = 'auto';
              chartContainer.style.touchMove = 'auto';
              chartContainer.style.touchStart = 'auto';
            }
          } else {
            // Disable interactions
            if (chartContainer) {
              chartContainer.style.pointerEvents = 'none';
              chartContainer.style.userSelect = 'none';
              chartContainer.style.touchAction = 'none';
              chartContainer.style.touchEnd = 'none';
              chartContainer.style.touchMove = 'none';
              chartContainer.style.touchStart = 'none';
            }
          }
        } else {
          console.log('TradingView widget not ready for interaction control');
        }
  """,
      );
    }
  }

  StreamSubscription<SymbolQuoteModel>? _prodcutSubscription;

  /// We are setting it to null in dispose mnethod.
  /// disposing won't be appropriate because this `_webViewController` is not created by this class.
  // ignore: dispose-fields
  InAppWebViewController? _webViewController;
  late InAppLocalhostServer _localhostServer;
  late Future<InAppLocalhostServer> _localhostServerFuture;

  Future<InAppLocalhostServer> _startLocalhostServer() async {
    final portNumber = await _findAvailablePort();
    final localhostServer = InAppLocalhostServer(port: portNumber);
    await localhostServer.start();
    _localhostServer = localhostServer;
    return _localhostServer;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _webViewController = null;

    if (_localhostServer.isRunning()) {
      _localhostServer.close();
    }
    _prodcutSubscription?.cancel();
    widget.tabSubscriptionManager.unregisterTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _subscribe,
      onUnsubscribe: _unsubscribe,
      onFirstVisit:
          () => _tradingChartViewBloc.add(
            TradingChartViewEvent.onLoadPositions(
              platformName: widget.platformName,
            ),
          ),
    );
    _unsubscribe();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.hidden:
        _isInBackground = true;
        break;
      case AppLifecycleState.resumed:
        if (_isInBackground) {
          _isInBackground = false;
          _handleAppResumed();
        }
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  void _handleAppResumed() {
    if (!mounted) {
      return;
    }
    _prodcutSubscription?.cancel();
    _reloadChartData();
  }

  void _reloadChartData() {
    if (_webViewController == null) {
      return;
    }
    _webViewController?.reload();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return FutureBuilder(
      future: _localhostServerFuture,
      builder: (builderContext, asyncSnapshot) {
        if (asyncSnapshot.hasData) {
          return InAppWebView(
            key: ValueKey(widget.platformName),
            initialSettings: InAppWebViewSettings(
              hardwareAcceleration: true,
              useHybridComposition: true,
              supportZoom: false,
              cacheEnabled: true,
              domStorageEnabled: true,
              javaScriptEnabled: true,
              transparentBackground: true,
            ),
            gestureRecognizers: {const Factory(EagerGestureRecognizer.new)},
            initialUrlRequest: URLRequest(
              url: WebUri.uri(
                Uri.parse(
                  'http://localhost:${_localhostServer.port}/${trader.Assets.chart.index}',
                ),
              ),
            ),
            onWebViewCreated: (controller) => _webViewController = controller,

            onLoadStop: (controller, _) => _setupJavaScriptHandlers(controller),
            onPageCommitVisible: _handlePageCommitVisible,
            onConsoleMessage: (controller, consoleMessage) {
              diContainer<LoggerBase>().logInfo(consoleMessage.message);
            },
          );
        }
        return Center(child: CircularProgressIndicator());
      },
    );
  }

  void _handlePageCommitVisible(
    InAppWebViewController controller,
    WebUri? uri,
  ) async {
    if (uri!.toString().contains('https://www.tradingview.com/?utm_source')) {
      await _webViewController?.goBack();
      await launchUrl(Uri.parse('https://www.tradingview.com/'));
    }
  }

  void _setupJavaScriptHandlers(InAppWebViewController controller) {
    controller
      ..addJavaScriptHandler(
        handlerName: 'init',
        callback: (_) => _buildInitHandlerData(),
      )
      ..addJavaScriptHandler(
        handlerName: 'resolveSymbol',
        callback: (_) => _buildResolveSymbolHandlerData(),
      )
      ..addJavaScriptHandler(
        handlerName: 'getBars',
        callback: (args) => _fetchBarsData(args),
      )
      ..addJavaScriptHandler(
        handlerName: 'onLoadingFinished',
        callback: (_) async {
          // Setup orientation detection after chart is loaded
          _setupOrientationDetection();
          await displayPositions();
        },
      )
      ..addJavaScriptHandler(
        handlerName: 'onResolutionChanged',
        callback: (args) => _handleResolutionChanged(args),
      )
      ..addJavaScriptHandler(
        handlerName: 'onChartTypeChanged',
        callback: (args) => _handleChartTypeChanged(args),
      )
      ..addJavaScriptHandler(
        handlerName: 'onShapeDeleted',
        callback: (args) async {
          if (_webViewController == null) return;
          final positionMap = args.safeFirst<Map<String, dynamic>>();

          final positionId = positionMap?['positionId'].toString();

          if (positionId != null) {
            final platformName = positionMap?['platformName'].toString();
            await _webViewController?.evaluateJavascript(
              source: "document.body.style.pointerEvents = 'none';",
            );

            await showChartClosepositionSheet(
              context: context,
              positionId: positionId,
              platformName: platformName ?? '',
              onTap: () {
                _removePosition(positionId);
              },
            );

            await _webViewController?.evaluateJavascript(
              source: "document.body.style.pointerEvents = 'auto';",
            );
          }
        },
      )
      ..addJavaScriptHandler(
        handlerName: 'onTogglePositions',
        callback: (args) async {
          _currentPositionToggle = args.safeFirst<bool>() ?? false;
          widget.onPositionToggleChanged?.call(_currentPositionToggle);
          if (_currentPositionToggle) {
            await displayPositions();
          } else {
            // Remove all positions
            if (_webViewController != null) {
              final removeAllJs = '''
                (function() {
                  if (!window.positionsMap) return;
                  Object.keys(window.positionsMap).forEach(function(key) {
                    var line = window.positionsMap[key];
                    if (line && typeof line.remove === "function") {
                      line.remove();
                    }
                  });
                  window.positionsMap = {};
                })();
              ''';
              await _evaluateJavaScript(source: removeAllJs);
            }
          }
        },
      );

    ;

    _subscribePriceUpdates();
  }

  String toJsSafeJson(Map<String, dynamic> obj) => jsonEncode(obj);

  void _removePosition(String id) async {
    if (_webViewController == null) return;
    final js = """
    (function() {
      if (window.positionsMap && window.positionsMap["$id"]) {
        window.positionsMap["$id"].remove();
        delete window.positionsMap["$id"];
      }
    })();
  """;

    await _evaluateJavaScript(source: js);
  }

  /// Setup orientation detection in JavaScript
  Future<void> _setupOrientationDetection() async {
    final durationMs = 200;
    final curve = "ease-in-out";
    await _evaluateJavaScript(
      source: '''
      (function() {
        console.log('Setting up orientation detection...');
        console.log('Animation config: ${durationMs}ms, $curve');

        let currentOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
        let isAnimating = false;
        console.log('Initial orientation:', currentOrientation);

        function animateChartSize() {
          if (isAnimating) {
            console.log('Animation already in progress, skipping...');
            return;
          }

          const chartContainer = document.querySelector('#tradingview_widget') ||
                                document.querySelector('.tradingview-widget-container') ||
                                document.querySelector('iframe') ||
                                document.body;

          if (!chartContainer) {
            console.log('Chart container not found');
            return;
          }

          const newOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';

          if (newOrientation !== currentOrientation) {
            console.log('Orientation changed from', currentOrientation, 'to', newOrientation);
            isAnimating = true;
            currentOrientation = newOrientation;

            // Setup animation
            chartContainer.style.willChange = 'width, height, transform';
            chartContainer.style.transition = 'all ${durationMs}ms $curve';
            chartContainer.style.backfaceVisibility = 'hidden';

            // Always animate to current window dimensions
            const targetWidth = window.innerWidth + 'px';
            const targetHeight = window.innerHeight + 'px';

            console.log('Animating to new size:', targetWidth, 'x', targetHeight);

            requestAnimationFrame(() => {
              if (newOrientation === 'landscape') {
                // Landscape: Use fullscreen positioning
                chartContainer.style.position = 'fixed';
                chartContainer.style.top = '0';
                chartContainer.style.left = '0';
                chartContainer.style.zIndex = '1000';
              } else {
                // Portrait: Use relative positioning
                chartContainer.style.position = 'relative';
                chartContainer.style.top = 'auto';
                chartContainer.style.left = 'auto';
                chartContainer.style.zIndex = 'auto';
              }

              // Always animate to current window size
              chartContainer.style.width = targetWidth;
              chartContainer.style.height = targetHeight;
              chartContainer.style.transform = 'translateZ(0)';
            });

            console.log('Animating to', newOrientation, 'mode with size:', targetWidth, 'x', targetHeight);

            // Clean up after animation
            setTimeout(() => {
              chartContainer.style.willChange = 'auto';
              chartContainer.style.backfaceVisibility = 'visible';
              chartContainer.style.transform = 'none';
              chartContainer.style.transition = '';
              isAnimating = false;

              // Trigger resize
              window.dispatchEvent(new Event('resize'));
              if (window.tvWidget && window.tvWidget.resize) {
                window.tvWidget.resize();
              }

              console.log('Animation complete and cleaned up');
            }, ${durationMs} + 100);
          }
        }

        // Listen for orientation change
        window.addEventListener('orientationchange', () => {
          console.log('Orientation change detected via orientationchange');
          setTimeout(animateChartSize, 150);
        });

        console.log('Orientation detection setup complete');
      })();
    ''',
    );
  }

  Map<String, dynamic> _buildInitHandlerData() {
    final isDark = diContainer<ThemeManager>().isDarkMode;
    return {
      "identifier": '${widget.accountNumber}${widget.platformName}',
      "symbol": widget.tickerName,
      "defaultInterval": _currentResolution,
      "disabledFeatures": _fullChartDisabledFeatures,
      "theme": isDark ? 'Dark' : 'Light',
      "timezone": _timeZone,
      "chartType": _currentChartType,
      "positionsVisible": _currentPositionToggle,
    };
  }

  Map<String, dynamic> _buildResolveSymbolHandlerData() {
    return {
      "ticker": widget.platformName,
      "name": widget.tickerName,
      "description": widget.tickerName,
      "session": '24x7',
      "exchange": 'FX',
      "timezone": _timeZone,
      "minmov": 1,
      "pricescale": pow(10, widget.digits),
      "has_intraday": true,
      "visible_plots_set": "ohlc",
      "volume_precision": 5,
      "data_status": 'streaming',
    };
  }

  Future<List<Map<String, Object?>>> _fetchBarsData(List<Object?> args) async {
    final Map<String, Object?>? request =
        args.safeFirst<Map<String, Object?>>();
    if (request != null) {
      try {
        if (!mounted) return [];
        final result = await _tradingChartViewBloc.onGetChart(
          symbol: widget.platformName,
          fromDate: request["from"] as String,
          toDate: request["to"] as String,
          timeframMinutes: request["resolution"] as String,
        );
        return result;
      } catch (e) {
        diContainer<LoggerBase>().logError(e.toString());
        widget.onError?.call();
        return [];
      }
    }

    return [];
  }

  void _subscribePriceUpdates() async {
    final result =
        await _tradingChartViewBloc
            .getSymbolQuotesStream(symbol: widget.platformName)
            .run();

    result.fold(
      (_) => widget.onChangeChartState?.call(ChartViewProcessState.error()),
      (stream) {
        _prodcutSubscription = stream.listen(
          (data) {
            if (!mounted) return;

            final jsonStr = jsonEncode(data.toJson());
            _evaluateJavaScript(
              source: """
                window.dispatchEvent(new CustomEvent("PricingEvent", { detail: $jsonStr }));
              """,
            );
          },
          onError:
              (_, __) => widget.onChangeChartState?.call(
                ChartViewProcessState.error(),
              ),
        );
      },
    );
  }

  Future<void> displayPositions() async {
    if (_webViewController == null) return;

    // Check if TradingView chart is ready
    final isReady = await _webViewController!.evaluateJavascript(
      source: '''
      (function() {
        return !!(window.tvWidget && typeof window.tvWidget.chart === 'function' && window.tvWidget.chart());
      })();
    ''',
    );
    if (isReady != true && isReady != 'true') return;

    final blocPositions = _tradingChartViewBloc.state.positions;
    final ids = blocPositions.keys.toList();
    final currency = _tradingChartViewBloc.state.selectedAccountCurrency ?? '';
    // 1️⃣ Remove only deleted positions
    final removeJs = '''
    (function() {
      window.positionsMap = window.positionsMap || {};
      const currentIds = ${ids.map((e) => '"$e"').toList()};
      for (const key of Object.keys(window.positionsMap)) {
        if (!currentIds.includes(key) && window.positionsMap[key]?.remove) {
          window.positionsMap[key].remove();
          delete window.positionsMap[key];
        }
      }
    })();
  ''';
    await _evaluateJavaScript(source: removeJs);

    if (!_currentPositionToggle) return;

    // 2️⃣ Update existing or create new positions
    for (final pos in blocPositions.values) {
      final id = pos.positionId;
      final color =
          pos.positionType == TradeType.buy
              ? "rgba(38, 166, 154, 1)"
              : "rgba(239, 83, 80, 1)";
      final profit = EquitiFormatter.formatTradeProfitOrLoss(
        value: pos.profit ?? 0,
        locale: 'en',
      );

      final posJson = jsonEncode({
        "positionId": id,
        "lotSize": pos.lotSize,
        "openPrice": pos.openPrice,
        "volume": pos.volume,
        "platformName": pos.platformName,
      });

      final js = '''
      (function() {
        const chart = window.tvWidget.chart();
        if (!chart) return;
        window.positionsMap = window.positionsMap || {};

        // Update existing line if it exists
        let posLine = window.positionsMap["$id"];
        if (posLine) {
          posLine.setText("$profit ${currency}")
                 .setQuantity("${pos.lotSize}");
        } else {
          // Create new position line
          posLine = chart.createPositionLine();
          posLine
            .setText("$profit ${currency}")
            .setQuantity("${pos.lotSize}")
            .setPrice(${pos.openPrice})
            .setLineLength(25)
            .setExtendLeft(false)
            .setLineStyle(0)
            .setLineColor("$color")
            .setBodyBorderColor("$color")
            .setCloseButtonBorderColor("$color")
            .setCloseButtonIconColor("$color")
            .setQuantityBackgroundColor("$color")
            .setQuantityBorderColor("$color")
            .onClose(() => {
              window.flutter_inappwebview.callHandler('onShapeDeleted', JSON.parse('$posJson'));
            });

          window.positionsMap["$id"] = posLine;
        }
      })();
    ''';

      await _evaluateJavaScript(source: js);
    }
  }

  Future<void> _handleResolutionChanged(List<Object?> args) async {
    try {
      final resolution = args.safeFirst<String>();
      if (resolution != null && resolution != _currentResolution) {
        _currentResolution = resolution;
        widget.onResolutionChanged?.call(resolution);
        await Future.delayed(Duration(seconds: 1), () async {
          await displayPositions();
        });
      }
    } catch (e) {
      debugPrint("Error handling resolution change: $e");
    }
  }

  void _handleChartTypeChanged(List<Object?> args) {
    try {
      final chartType = args.safeFirst<int>();
      if (chartType != null && chartType != _currentChartType) {
        _currentChartType = chartType;
        widget.onChartTypeChanged?.call(chartType);
      }
    } catch (e) {
      debugPrint("Error handling chart type change: $e");
    }
  }

  Future<void> _evaluateJavaScript({required String source}) async {
    if (_webViewController == null) return;
    try {
      await _webViewController!.evaluateJavascript(source: source);
    } catch (e) {
      debugPrint("Evaluate javascript error: $e");
    }
  }

  List<String> get _fullChartDisabledFeatures => const [
    "header_screenshot",
    "header_fullscreen_button",
    "header_compare",
    "header_symbol_search",
    "symbol_search_hot_key",
    "legend_inplace_edit",
    "display_market_status",
    "control_bar",
    "header_fullscreen_button",
    "header_layouttoggle",
    "border_around_the_chart",
    "use_localstorage_for_settings",
  ];

  Future<int> _findAvailablePort() async {
    final socket = await ServerSocket.bind('localhost', 0);
    final port = socket.port;
    await socket.close();
    return port;
  }
}

extension SafeListAccess on List<Object?> {
  T? safeFirst<T>() {
    if (isEmpty || first == null) return null;
    return first as T?;
  }
}
