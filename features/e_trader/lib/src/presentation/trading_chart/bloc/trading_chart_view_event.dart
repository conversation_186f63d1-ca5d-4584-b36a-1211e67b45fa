part of 'trading_chart_view_bloc.dart';

@freezed
sealed class TradingChartViewEvent with _$TradingChartViewEvent {
  const factory TradingChartViewEvent.onGetInitialSetting(String symbol) =
      _OnGetInitialSetting;

  const factory TradingChartViewEvent.onChangeResolution(String resolution) =
      _OnChangeResolution;
  const factory TradingChartViewEvent.onShowHidePositions(
    bool isPositionToggle,
  ) = _OnShowHidePositions;
  const factory TradingChartViewEvent.onChangeChartType(int chartType) =
      _OnChangeChartType;
  const factory TradingChartViewEvent.onError() = _OnChartError;

  const factory TradingChartViewEvent.onLoadPositions({
    required String platformName,
  }) = _OnLoadPositions;
  const factory TradingChartViewEvent.processPositions({
    PositionResponse? positionResponse,
    bool? isChart,
    required String platformName,
  }) = _ProcessPositions;
  const factory TradingChartViewEvent.updatePositions({
    required EventType eventType,
    required String platformName,
  }) = _UpdatePositions;
}
