import 'dart:async';
import 'dart:collection';

import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/data/socket/position_response.dart';
import 'package:e_trader/src/domain/model/grouped_positions.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_chart_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:preferences/preferences.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'trading_chart_view_bloc.freezed.dart';
part 'trading_chart_view_event.dart';
part 'trading_chart_view_state.dart';

class TradingChartViewBloc
    extends Bloc<TradingChartViewEvent, TradingChartViewState>
    with DisposableMixin {
  final LoggerBase _logger;
  final EquitiPreferences _preferences;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final GetTradingChartUseCase _getTradingChartUseCase;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;
  final SubscribeToPositionsUseCase _subscribeToPositionsUseCase;
  final UpdatePositionsUseCase _updatePositionsUseCase;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;

  Timer? _positionDeleteBatchTimer;
  Timer? _positionBatchTimer;
  final _positionsById = <String, PositionResponse>{};

  TradingChartViewBloc({
    required LoggerBase logger,
    required EquitiPreferences preferences,
    required GetAccountNumberUseCase getAccountNumberUseCase,
    required GetSelectedAccountUseCase getSelectedAccountUseCase,
    required GetTradingChartUseCase getTradingChartUseCase,
    required SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
    required SubscribeToPositionsUseCase subscribeToPositionsUseCase,
    required UpdatePositionsUseCase updatePositionsUseCase,
  }) : _logger = logger,
       _preferences = preferences,
       _getAccountNumberUseCase = getAccountNumberUseCase,
       _getTradingChartUseCase = getTradingChartUseCase,
       _subscribeToSymbolQuotesUseCase = subscribeToSymbolQuotesUseCase,
       _subscribeToPositionsUseCase = subscribeToPositionsUseCase,
       _updatePositionsUseCase = updatePositionsUseCase,
       _getSelectedAccountUseCase = getSelectedAccountUseCase,
       super(TradingChartViewState(groupedPositions: {}, positions: {})) {
    on<_OnGetInitialSetting>(_onGetInitialSetting);
    on<_OnChangeResolution>(_onChangeResolution);
    on<_OnChangeChartType>(_onChangeChartType);
    on<_OnChartError>(_onChartError);
    ;
    on<_OnLoadPositions>(
      (event, emit) async => await _onLoadPositions(event, emit),
    );
    on<_UpdatePositions>(
      (event, emit) =>
          _updatePositions(event.eventType, event.platformName, emit),
    );
    on<_ProcessPositions>((event, emit) => _processPositions(event, emit));
    on<_OnShowHidePositions>(
      (event, emit) => _onShowHidePositions(event, emit),
    );
  }

  @override
  Future<void> close() {
    _positionBatchTimer?.cancel();
    _positionDeleteBatchTimer?.cancel();
    return super.close();
  }

  FutureOr<void> _onGetInitialSetting(
    _OnGetInitialSetting event,
    Emitter<TradingChartViewState> emit,
  ) async {
    final String savedResolution = _preferences.getValue<String>(
      "chart_resolution_${event.symbol}",
      "60", // Default to 1 Min
    );
    final int savedChartType = _preferences.getValue<int>(
      "chart_type_${event.symbol}",
      1, // Default to 1
    );
    final bool isPositionToggle = _preferences.getValue<bool>(
      "toggle_${event.symbol}",
      true, // Default to true
    );
    await _getAccountNumberUseCase().toTaskEither().chainFirst((accountNumber) {
      if (!isClosed)
        emit(
          state.copyWith(
            resolution: savedResolution,
            chartType: savedChartType,
            accountNumber: accountNumber,
            symbol: event.symbol,
            isPositionToggle: isPositionToggle,
            processState: ChartViewProcessState.success(),
          ),
        );
      return TaskEither.of(accountNumber);
    }).run();
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }

  FutureOr<void> _onChangeResolution(
    _OnChangeResolution event,
    Emitter<TradingChartViewState> emit,
  ) async {
    emit(state.copyWith(resolution: event.resolution));
    await _preferences.setValue(
      "chart_resolution_${state.symbol}",
      event.resolution,
    );
  }

  FutureOr<void> _onShowHidePositions(
    _OnShowHidePositions event,
    Emitter<TradingChartViewState> emit,
  ) async {
    emit(state.copyWith(isPositionToggle: event.isPositionToggle));
    await _preferences.setValue(
      "toggle_${state.symbol}",
      event.isPositionToggle,
    );
  }

  FutureOr<void> _onChangeChartType(
    _OnChangeChartType event,
    Emitter<TradingChartViewState> emit,
  ) async {
    emit(state.copyWith(chartType: event.chartType));
    await _preferences.setValue("chart_type_${state.symbol}", event.chartType);
  }

  FutureOr<void> _onChartError(
    _OnChartError event,
    Emitter<TradingChartViewState> emit,
  ) {
    emit(state.copyWith(processState: ChartViewProcessState.error()));
  }

  // ignore: avoid-bloc-public-methods
  Future<List<Map<String, Object?>>> onGetChart({
    required String symbol,
    required String fromDate,
    required String toDate,
    required String timeframMinutes,
  }) async {
    final result =
        await _getAccountNumberUseCase().toTaskEither().flatMap((
          accountNumber,
        ) {
          return _getTradingChartUseCase(
            accountNumber: accountNumber,
            symbolCode: symbol,
            fromDate: fromDate,
            toDate: toDate,
            timeframMinutes: timeframMinutes,
          );
        }).run();

    final List<Map<String, Object?>> listOfResult = result.fold(
      (exception) => throw exception,
      (listOfCandles) {
        return listOfCandles.map((e) {
          return {
            "time":
                DateTime.parse("${e.dateTime}Z").millisecondsSinceEpoch.abs(),
            "low": e.low,
            "high": e.high,
            "close": e.close,
            "open": e.open,
          };
        }).toList();
      },
    );
    return listOfResult;
  }

  // ignore: avoid-bloc-public-methods
  TaskEither<Exception, Stream<SymbolQuoteModel>> getSymbolQuotesStream({
    required String symbol,
  }) => _subscribeToSymbolQuotesUseCase(
    symbol: symbol,
    subscriberId: '${TradingChartViewBloc}_$hashCode',
  );

  FutureOr<void> _onLoadPositions(
    _OnLoadPositions event,
    Emitter<TradingChartViewState> emit,
  ) async {
    emit(
      state.copyWith(positionProcessState: ChartPositionProcessState.loading()),
    );
    if (state.selectedAccountCurrency == null) {
      final accountCurrency =
          _getSelectedAccountUseCase()?.homeCurrency ?? "USD";
      emit(state.copyWith(selectedAccountCurrency: accountCurrency));
    }
    final result =
        await _subscribeToPositionsUseCase(
          subscriberId: '${TradingChartViewBloc}_$hashCode',
          eventType: TradingSocketEvent.positions.register,
          symbolName: event.platformName,
        ).run();

    result.fold(
      (error) {
        addError(error);

        emit(
          state.copyWith(
            positionProcessState: ChartPositionProcessState.error(),
          ),
        );
      },
      (stream) {
        if (!isClosed)
          emit(
            state.copyWith(
              positionProcessState: ChartPositionProcessState.connected(),
            ),
          );
        addSubscription(
          stream.listen(
            (positionResponse) => add(
              TradingChartViewEvent.processPositions(
                positionResponse: positionResponse,
                isChart: true,
                platformName: event.platformName,
              ),
            ),
            onError: (Object? error) {
              emit(
                state.copyWith(
                  positionProcessState: ChartPositionProcessState.error(),
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _processPositions(
    _ProcessPositions event,
    Emitter<TradingChartViewState> emit,
  ) {
    final positionResponse = event.positionResponse;

    if (positionResponse == null) {
      emit(
        state.copyWith(
          groupedPositions: {},
          positionProcessState: ChartPositionProcessState.empty(),
        ),
      );
      return;
    }

    if (event.platformName.isNotEmpty &&
        positionResponse.position.platformName != event.platformName) {
      return;
    }

    final position = positionResponse.position;
    final symbol = position.platformName;

    // ========== DELETE HANDLING ==========
    if (positionResponse.target == 'PositionDeleted') {
      final platformName = symbol;
      final positionId = position.positionId;

      _positionsById.remove(positionId);

      if (state.groupedPositions.containsKey(platformName)) {
        final grouped = state.groupedPositions[platformName]!;
        final updated =
            grouped.positions.where((p) => p.positionId != positionId).toList();

        if (updated.isEmpty) {
          state.groupedPositions.remove(platformName);
        } else {
          state.groupedPositions[platformName] = grouped.copyWith(
            positions: updated,
          );
        }
      }

      emit(
        state.copyWith(
          positionProcessState: ChartPositionProcessState.success(),
          positions:
              LinkedHashMap<String, PositionModel>()..addAll(
                _positionsById.map(
                  (key, r) => MapEntry(r.position.positionId, r.position),
                ),
              ),
        ),
      );
      return;
    }

    // ========== UPDATE OR ADD ==========
    _positionsById[position.positionId] = positionResponse;

    // Initial LOAD
    if (state.groupedPositions.isEmpty) {
      final groupedMap = _positionsById.values
          .where((r) => r.target != 'PositionDeleted')
          .map((r) => r.position)
          .groupBy((p) => p.platformName);

      final newGrouped = groupedMap.map<String, GroupedPositions>((key, value) {
        final list = value.toList();

        return MapEntry(
          key,
          GroupedPositions(
            platformName: key,
            tickerName: list.firstOrNull?.tickerName ?? '',
            url: list.firstOrNull?.productLogoUrl ?? '',
            positions: list,
          ),
        );
      });

      emit(
        state.copyWith(
          groupedPositions: newGrouped,
          positionProcessState: ChartPositionProcessState.success(),
          positions:
              LinkedHashMap<String, PositionModel>()..addAll(
                _positionsById.map(
                  (key, r) => MapEntry(r.position.positionId, r.position),
                ),
              ),
        ),
      );
      return;
    }

    // Subsequent updates
    if (state.groupedPositions.containsKey(symbol)) {
      final grouped = state.groupedPositions[symbol]!;
      final idx = grouped.positions.indexWhere(
        (p) => p.positionId == position.positionId,
      );

      final updated = [...grouped.positions];

      if (idx >= 0) {
        updated[idx] = position;
      } else {
        updated.add(position);
      }

      state.groupedPositions[symbol] = grouped.copyWith(positions: updated);
    } else {
      state.groupedPositions[symbol] = GroupedPositions(
        platformName: symbol,
        tickerName: position.tickerName,
        url: position.productLogoUrl,
        positions: [position],
      );
    }

    emit(
      state.copyWith(
        positionProcessState: ChartPositionProcessState.success(),
        positions:
            LinkedHashMap<String, PositionModel>()..addAll(
              _positionsById.map(
                (key, r) => MapEntry(r.position.positionId, r.position),
              ),
            ),
      ),
    );
  }

  void _updatePositions(
    EventType eventType,
    String platformName,
    Emitter<TradingChartViewState> emit,
  ) {
    // Clear the map when coming to foreground, to remove stale data
    if (eventType is SubscribeEvent) {
      _positionsById.clear();
      emit(
        state.copyWith(
          positions: SplayTreeMap(),
          positionProcessState: ChartPositionProcessState.loading(),
        ),
      );
    }

    _updatePositionsUseCase(eventType: eventType, symbolName: platformName);
  }
}
