// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'trading_chart_view_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TradingChartViewEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TradingChartViewEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TradingChartViewEvent()';
}


}

/// @nodoc
class $TradingChartViewEventCopyWith<$Res>  {
$TradingChartViewEventCopyWith(TradingChartViewEvent _, $Res Function(TradingChartViewEvent) __);
}


/// @nodoc


class _OnGetInitialSetting implements TradingChartViewEvent {
  const _OnGetInitialSetting(this.symbol);
  

 final  String symbol;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGetInitialSettingCopyWith<_OnGetInitialSetting> get copyWith => __$OnGetInitialSettingCopyWithImpl<_OnGetInitialSetting>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGetInitialSetting&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,symbol);

@override
String toString() {
  return 'TradingChartViewEvent.onGetInitialSetting(symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$OnGetInitialSettingCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$OnGetInitialSettingCopyWith(_OnGetInitialSetting value, $Res Function(_OnGetInitialSetting) _then) = __$OnGetInitialSettingCopyWithImpl;
@useResult
$Res call({
 String symbol
});




}
/// @nodoc
class __$OnGetInitialSettingCopyWithImpl<$Res>
    implements _$OnGetInitialSettingCopyWith<$Res> {
  __$OnGetInitialSettingCopyWithImpl(this._self, this._then);

  final _OnGetInitialSetting _self;
  final $Res Function(_OnGetInitialSetting) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbol = null,}) {
  return _then(_OnGetInitialSetting(
null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnChangeResolution implements TradingChartViewEvent {
  const _OnChangeResolution(this.resolution);
  

 final  String resolution;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnChangeResolutionCopyWith<_OnChangeResolution> get copyWith => __$OnChangeResolutionCopyWithImpl<_OnChangeResolution>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnChangeResolution&&(identical(other.resolution, resolution) || other.resolution == resolution));
}


@override
int get hashCode => Object.hash(runtimeType,resolution);

@override
String toString() {
  return 'TradingChartViewEvent.onChangeResolution(resolution: $resolution)';
}


}

/// @nodoc
abstract mixin class _$OnChangeResolutionCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$OnChangeResolutionCopyWith(_OnChangeResolution value, $Res Function(_OnChangeResolution) _then) = __$OnChangeResolutionCopyWithImpl;
@useResult
$Res call({
 String resolution
});




}
/// @nodoc
class __$OnChangeResolutionCopyWithImpl<$Res>
    implements _$OnChangeResolutionCopyWith<$Res> {
  __$OnChangeResolutionCopyWithImpl(this._self, this._then);

  final _OnChangeResolution _self;
  final $Res Function(_OnChangeResolution) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? resolution = null,}) {
  return _then(_OnChangeResolution(
null == resolution ? _self.resolution : resolution // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnShowHidePositions implements TradingChartViewEvent {
  const _OnShowHidePositions(this.isPositionToggle);
  

 final  bool isPositionToggle;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnShowHidePositionsCopyWith<_OnShowHidePositions> get copyWith => __$OnShowHidePositionsCopyWithImpl<_OnShowHidePositions>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnShowHidePositions&&(identical(other.isPositionToggle, isPositionToggle) || other.isPositionToggle == isPositionToggle));
}


@override
int get hashCode => Object.hash(runtimeType,isPositionToggle);

@override
String toString() {
  return 'TradingChartViewEvent.onShowHidePositions(isPositionToggle: $isPositionToggle)';
}


}

/// @nodoc
abstract mixin class _$OnShowHidePositionsCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$OnShowHidePositionsCopyWith(_OnShowHidePositions value, $Res Function(_OnShowHidePositions) _then) = __$OnShowHidePositionsCopyWithImpl;
@useResult
$Res call({
 bool isPositionToggle
});




}
/// @nodoc
class __$OnShowHidePositionsCopyWithImpl<$Res>
    implements _$OnShowHidePositionsCopyWith<$Res> {
  __$OnShowHidePositionsCopyWithImpl(this._self, this._then);

  final _OnShowHidePositions _self;
  final $Res Function(_OnShowHidePositions) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isPositionToggle = null,}) {
  return _then(_OnShowHidePositions(
null == isPositionToggle ? _self.isPositionToggle : isPositionToggle // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _OnChangeChartType implements TradingChartViewEvent {
  const _OnChangeChartType(this.chartType);
  

 final  int chartType;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnChangeChartTypeCopyWith<_OnChangeChartType> get copyWith => __$OnChangeChartTypeCopyWithImpl<_OnChangeChartType>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnChangeChartType&&(identical(other.chartType, chartType) || other.chartType == chartType));
}


@override
int get hashCode => Object.hash(runtimeType,chartType);

@override
String toString() {
  return 'TradingChartViewEvent.onChangeChartType(chartType: $chartType)';
}


}

/// @nodoc
abstract mixin class _$OnChangeChartTypeCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$OnChangeChartTypeCopyWith(_OnChangeChartType value, $Res Function(_OnChangeChartType) _then) = __$OnChangeChartTypeCopyWithImpl;
@useResult
$Res call({
 int chartType
});




}
/// @nodoc
class __$OnChangeChartTypeCopyWithImpl<$Res>
    implements _$OnChangeChartTypeCopyWith<$Res> {
  __$OnChangeChartTypeCopyWithImpl(this._self, this._then);

  final _OnChangeChartType _self;
  final $Res Function(_OnChangeChartType) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? chartType = null,}) {
  return _then(_OnChangeChartType(
null == chartType ? _self.chartType : chartType // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _OnChartError implements TradingChartViewEvent {
  const _OnChartError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnChartError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TradingChartViewEvent.onError()';
}


}




/// @nodoc


class _OnLoadPositions implements TradingChartViewEvent {
  const _OnLoadPositions({required this.platformName});
  

 final  String platformName;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLoadPositionsCopyWith<_OnLoadPositions> get copyWith => __$OnLoadPositionsCopyWithImpl<_OnLoadPositions>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLoadPositions&&(identical(other.platformName, platformName) || other.platformName == platformName));
}


@override
int get hashCode => Object.hash(runtimeType,platformName);

@override
String toString() {
  return 'TradingChartViewEvent.onLoadPositions(platformName: $platformName)';
}


}

/// @nodoc
abstract mixin class _$OnLoadPositionsCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$OnLoadPositionsCopyWith(_OnLoadPositions value, $Res Function(_OnLoadPositions) _then) = __$OnLoadPositionsCopyWithImpl;
@useResult
$Res call({
 String platformName
});




}
/// @nodoc
class __$OnLoadPositionsCopyWithImpl<$Res>
    implements _$OnLoadPositionsCopyWith<$Res> {
  __$OnLoadPositionsCopyWithImpl(this._self, this._then);

  final _OnLoadPositions _self;
  final $Res Function(_OnLoadPositions) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? platformName = null,}) {
  return _then(_OnLoadPositions(
platformName: null == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ProcessPositions implements TradingChartViewEvent {
  const _ProcessPositions({this.positionResponse, this.isChart, required this.platformName});
  

 final  PositionResponse? positionResponse;
 final  bool? isChart;
 final  String platformName;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessPositionsCopyWith<_ProcessPositions> get copyWith => __$ProcessPositionsCopyWithImpl<_ProcessPositions>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessPositions&&(identical(other.positionResponse, positionResponse) || other.positionResponse == positionResponse)&&(identical(other.isChart, isChart) || other.isChart == isChart)&&(identical(other.platformName, platformName) || other.platformName == platformName));
}


@override
int get hashCode => Object.hash(runtimeType,positionResponse,isChart,platformName);

@override
String toString() {
  return 'TradingChartViewEvent.processPositions(positionResponse: $positionResponse, isChart: $isChart, platformName: $platformName)';
}


}

/// @nodoc
abstract mixin class _$ProcessPositionsCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$ProcessPositionsCopyWith(_ProcessPositions value, $Res Function(_ProcessPositions) _then) = __$ProcessPositionsCopyWithImpl;
@useResult
$Res call({
 PositionResponse? positionResponse, bool? isChart, String platformName
});


$PositionResponseCopyWith<$Res>? get positionResponse;

}
/// @nodoc
class __$ProcessPositionsCopyWithImpl<$Res>
    implements _$ProcessPositionsCopyWith<$Res> {
  __$ProcessPositionsCopyWithImpl(this._self, this._then);

  final _ProcessPositions _self;
  final $Res Function(_ProcessPositions) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? positionResponse = freezed,Object? isChart = freezed,Object? platformName = null,}) {
  return _then(_ProcessPositions(
positionResponse: freezed == positionResponse ? _self.positionResponse : positionResponse // ignore: cast_nullable_to_non_nullable
as PositionResponse?,isChart: freezed == isChart ? _self.isChart : isChart // ignore: cast_nullable_to_non_nullable
as bool?,platformName: null == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionResponseCopyWith<$Res>? get positionResponse {
    if (_self.positionResponse == null) {
    return null;
  }

  return $PositionResponseCopyWith<$Res>(_self.positionResponse!, (value) {
    return _then(_self.copyWith(positionResponse: value));
  });
}
}

/// @nodoc


class _UpdatePositions implements TradingChartViewEvent {
  const _UpdatePositions({required this.eventType, required this.platformName});
  

 final  EventType eventType;
 final  String platformName;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdatePositionsCopyWith<_UpdatePositions> get copyWith => __$UpdatePositionsCopyWithImpl<_UpdatePositions>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdatePositions&&(identical(other.eventType, eventType) || other.eventType == eventType)&&(identical(other.platformName, platformName) || other.platformName == platformName));
}


@override
int get hashCode => Object.hash(runtimeType,eventType,platformName);

@override
String toString() {
  return 'TradingChartViewEvent.updatePositions(eventType: $eventType, platformName: $platformName)';
}


}

/// @nodoc
abstract mixin class _$UpdatePositionsCopyWith<$Res> implements $TradingChartViewEventCopyWith<$Res> {
  factory _$UpdatePositionsCopyWith(_UpdatePositions value, $Res Function(_UpdatePositions) _then) = __$UpdatePositionsCopyWithImpl;
@useResult
$Res call({
 EventType eventType, String platformName
});




}
/// @nodoc
class __$UpdatePositionsCopyWithImpl<$Res>
    implements _$UpdatePositionsCopyWith<$Res> {
  __$UpdatePositionsCopyWithImpl(this._self, this._then);

  final _UpdatePositions _self;
  final $Res Function(_UpdatePositions) _then;

/// Create a copy of TradingChartViewEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? eventType = null,Object? platformName = null,}) {
  return _then(_UpdatePositions(
eventType: null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as EventType,platformName: null == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$TradingChartViewState {

 ChartViewProcessState get processState; set processState(ChartViewProcessState value); ChartPositionProcessState get positionProcessState; set positionProcessState(ChartPositionProcessState value); int get chartType; set chartType(int value); String get resolution; set resolution(String value);// Default to 1 Minute
 String get accountNumber;// Default to 1 Minute
 set accountNumber(String value); String get symbol; set symbol(String value); int get errorCounter; set errorCounter(int value); Map<String, GroupedPositions> get groupedPositions; String? get selectedAccountCurrency; set selectedAccountCurrency(String? value); bool get isPositionToggle; set isPositionToggle(bool value); Map<String, PositionModel> get positions;
/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TradingChartViewStateCopyWith<TradingChartViewState> get copyWith => _$TradingChartViewStateCopyWithImpl<TradingChartViewState>(this as TradingChartViewState, _$identity);





@override
String toString() {
  return 'TradingChartViewState(processState: $processState, positionProcessState: $positionProcessState, chartType: $chartType, resolution: $resolution, accountNumber: $accountNumber, symbol: $symbol, errorCounter: $errorCounter, groupedPositions: $groupedPositions, selectedAccountCurrency: $selectedAccountCurrency, isPositionToggle: $isPositionToggle, positions: $positions)';
}


}

/// @nodoc
abstract mixin class $TradingChartViewStateCopyWith<$Res>  {
  factory $TradingChartViewStateCopyWith(TradingChartViewState value, $Res Function(TradingChartViewState) _then) = _$TradingChartViewStateCopyWithImpl;
@useResult
$Res call({
 ChartViewProcessState processState, ChartPositionProcessState positionProcessState, int chartType, String resolution, String accountNumber, String symbol, int errorCounter, Map<String, GroupedPositions> groupedPositions, String? selectedAccountCurrency, bool isPositionToggle, Map<String, PositionModel> positions
});


$ChartViewProcessStateCopyWith<$Res> get processState;$ChartPositionProcessStateCopyWith<$Res> get positionProcessState;

}
/// @nodoc
class _$TradingChartViewStateCopyWithImpl<$Res>
    implements $TradingChartViewStateCopyWith<$Res> {
  _$TradingChartViewStateCopyWithImpl(this._self, this._then);

  final TradingChartViewState _self;
  final $Res Function(TradingChartViewState) _then;

/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? positionProcessState = null,Object? chartType = null,Object? resolution = null,Object? accountNumber = null,Object? symbol = null,Object? errorCounter = null,Object? groupedPositions = null,Object? selectedAccountCurrency = freezed,Object? isPositionToggle = null,Object? positions = null,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ChartViewProcessState,positionProcessState: null == positionProcessState ? _self.positionProcessState : positionProcessState // ignore: cast_nullable_to_non_nullable
as ChartPositionProcessState,chartType: null == chartType ? _self.chartType : chartType // ignore: cast_nullable_to_non_nullable
as int,resolution: null == resolution ? _self.resolution : resolution // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,symbol: null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,errorCounter: null == errorCounter ? _self.errorCounter : errorCounter // ignore: cast_nullable_to_non_nullable
as int,groupedPositions: null == groupedPositions ? _self.groupedPositions : groupedPositions // ignore: cast_nullable_to_non_nullable
as Map<String, GroupedPositions>,selectedAccountCurrency: freezed == selectedAccountCurrency ? _self.selectedAccountCurrency : selectedAccountCurrency // ignore: cast_nullable_to_non_nullable
as String?,isPositionToggle: null == isPositionToggle ? _self.isPositionToggle : isPositionToggle // ignore: cast_nullable_to_non_nullable
as bool,positions: null == positions ? _self.positions : positions // ignore: cast_nullable_to_non_nullable
as Map<String, PositionModel>,
  ));
}
/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChartViewProcessStateCopyWith<$Res> get processState {
  
  return $ChartViewProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChartPositionProcessStateCopyWith<$Res> get positionProcessState {
  
  return $ChartPositionProcessStateCopyWith<$Res>(_self.positionProcessState, (value) {
    return _then(_self.copyWith(positionProcessState: value));
  });
}
}


/// @nodoc


class _TradingChartViewState implements TradingChartViewState {
   _TradingChartViewState({this.processState = const ChartViewProcessState.loading(), this.positionProcessState = const ChartPositionProcessState.loading(), this.chartType = 1, this.resolution = "1", this.accountNumber = "", this.symbol = "", this.errorCounter = 0, required this.groupedPositions, this.selectedAccountCurrency, this.isPositionToggle = true, required this.positions});
  

@override@JsonKey()  ChartViewProcessState processState;
@override@JsonKey()  ChartPositionProcessState positionProcessState;
@override@JsonKey()  int chartType;
@override@JsonKey()  String resolution;
// Default to 1 Minute
@override@JsonKey()  String accountNumber;
@override@JsonKey()  String symbol;
@override@JsonKey()  int errorCounter;
@override final  Map<String, GroupedPositions> groupedPositions;
@override  String? selectedAccountCurrency;
@override@JsonKey()  bool isPositionToggle;
@override final  Map<String, PositionModel> positions;

/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TradingChartViewStateCopyWith<_TradingChartViewState> get copyWith => __$TradingChartViewStateCopyWithImpl<_TradingChartViewState>(this, _$identity);





@override
String toString() {
  return 'TradingChartViewState(processState: $processState, positionProcessState: $positionProcessState, chartType: $chartType, resolution: $resolution, accountNumber: $accountNumber, symbol: $symbol, errorCounter: $errorCounter, groupedPositions: $groupedPositions, selectedAccountCurrency: $selectedAccountCurrency, isPositionToggle: $isPositionToggle, positions: $positions)';
}


}

/// @nodoc
abstract mixin class _$TradingChartViewStateCopyWith<$Res> implements $TradingChartViewStateCopyWith<$Res> {
  factory _$TradingChartViewStateCopyWith(_TradingChartViewState value, $Res Function(_TradingChartViewState) _then) = __$TradingChartViewStateCopyWithImpl;
@override @useResult
$Res call({
 ChartViewProcessState processState, ChartPositionProcessState positionProcessState, int chartType, String resolution, String accountNumber, String symbol, int errorCounter, Map<String, GroupedPositions> groupedPositions, String? selectedAccountCurrency, bool isPositionToggle, Map<String, PositionModel> positions
});


@override $ChartViewProcessStateCopyWith<$Res> get processState;@override $ChartPositionProcessStateCopyWith<$Res> get positionProcessState;

}
/// @nodoc
class __$TradingChartViewStateCopyWithImpl<$Res>
    implements _$TradingChartViewStateCopyWith<$Res> {
  __$TradingChartViewStateCopyWithImpl(this._self, this._then);

  final _TradingChartViewState _self;
  final $Res Function(_TradingChartViewState) _then;

/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? positionProcessState = null,Object? chartType = null,Object? resolution = null,Object? accountNumber = null,Object? symbol = null,Object? errorCounter = null,Object? groupedPositions = null,Object? selectedAccountCurrency = freezed,Object? isPositionToggle = null,Object? positions = null,}) {
  return _then(_TradingChartViewState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ChartViewProcessState,positionProcessState: null == positionProcessState ? _self.positionProcessState : positionProcessState // ignore: cast_nullable_to_non_nullable
as ChartPositionProcessState,chartType: null == chartType ? _self.chartType : chartType // ignore: cast_nullable_to_non_nullable
as int,resolution: null == resolution ? _self.resolution : resolution // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,symbol: null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,errorCounter: null == errorCounter ? _self.errorCounter : errorCounter // ignore: cast_nullable_to_non_nullable
as int,groupedPositions: null == groupedPositions ? _self.groupedPositions : groupedPositions // ignore: cast_nullable_to_non_nullable
as Map<String, GroupedPositions>,selectedAccountCurrency: freezed == selectedAccountCurrency ? _self.selectedAccountCurrency : selectedAccountCurrency // ignore: cast_nullable_to_non_nullable
as String?,isPositionToggle: null == isPositionToggle ? _self.isPositionToggle : isPositionToggle // ignore: cast_nullable_to_non_nullable
as bool,positions: null == positions ? _self.positions : positions // ignore: cast_nullable_to_non_nullable
as Map<String, PositionModel>,
  ));
}

/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChartViewProcessStateCopyWith<$Res> get processState {
  
  return $ChartViewProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of TradingChartViewState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChartPositionProcessStateCopyWith<$Res> get positionProcessState {
  
  return $ChartPositionProcessStateCopyWith<$Res>(_self.positionProcessState, (value) {
    return _then(_self.copyWith(positionProcessState: value));
  });
}
}

/// @nodoc
mixin _$ChartViewProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartViewProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartViewProcessState()';
}


}

/// @nodoc
class $ChartViewProcessStateCopyWith<$Res>  {
$ChartViewProcessStateCopyWith(ChartViewProcessState _, $Res Function(ChartViewProcessState) __);
}


/// @nodoc


class _Loading implements ChartViewProcessState {
  const _Loading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Loading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartViewProcessState.loading()';
}


}




/// @nodoc


class _Success implements ChartViewProcessState {
  const _Success();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Success);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartViewProcessState.success()';
}


}




/// @nodoc


class _Error implements ChartViewProcessState {
  const _Error();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Error);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartViewProcessState.error()';
}


}




/// @nodoc
mixin _$ChartPositionProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartPositionProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartPositionProcessState()';
}


}

/// @nodoc
class $ChartPositionProcessStateCopyWith<$Res>  {
$ChartPositionProcessStateCopyWith(ChartPositionProcessState _, $Res Function(ChartPositionProcessState) __);
}


/// @nodoc


class ChartPositionConnectedState implements ChartPositionProcessState {
  const ChartPositionConnectedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartPositionConnectedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartPositionProcessState.connected()';
}


}




/// @nodoc


class ChartPositionLoadingState implements ChartPositionProcessState {
  const ChartPositionLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartPositionLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartPositionProcessState.loading()';
}


}




/// @nodoc


class ChartPositionSuccessState implements ChartPositionProcessState {
  const ChartPositionSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartPositionSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartPositionProcessState.success()';
}


}




/// @nodoc


class ChartPositionEmptyState implements ChartPositionProcessState {
  const ChartPositionEmptyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartPositionEmptyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartPositionProcessState.empty()';
}


}




/// @nodoc


class ChartPositionErrorState implements ChartPositionProcessState {
  const ChartPositionErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChartPositionErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChartPositionProcessState.error()';
}


}




// dart format on
