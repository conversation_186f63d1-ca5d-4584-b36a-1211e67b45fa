part of 'trading_chart_view_bloc.dart';

@unfreezed
// ignore: prefer-immutable-bloc-state
sealed class TradingChartViewState with _$TradingChartViewState {
  factory TradingChartViewState({
    @Default(ChartViewProcessState.loading())
    ChartViewProcessState processState,
    @Default(ChartPositionProcessState.loading())
    ChartPositionProcessState positionProcessState,
    @Default(1) int chartType,
    @Default("1") String resolution, // Default to 1 Minute
    @Default("") String accountNumber,
    @Default("") String symbol,
    @Default(0) int errorCounter,
    required final Map<String, GroupedPositions> groupedPositions,
    String? selectedAccountCurrency,
    @Default(true) bool isPositionToggle,
    required final Map<String, PositionModel> positions,
  }) = _TradingChartViewState;
}

@freezed
sealed class ChartViewProcessState with _$ChartViewProcessState {
  const factory ChartViewProcessState.loading() = _Loading;
  const factory ChartViewProcessState.success() = _Success;
  const factory ChartViewProcessState.error() = _Error;
}

@freezed
sealed class ChartPositionProcessState with _$ChartPositionProcessState {
  const factory ChartPositionProcessState.connected() =
      ChartPositionConnectedState;
  const factory ChartPositionProcessState.loading() = ChartPositionLoadingState;
  const factory ChartPositionProcessState.success() = ChartPositionSuccessState;
  const factory ChartPositionProcessState.empty() = ChartPositionEmptyState;
  const factory ChartPositionProcessState.error() = ChartPositionErrorState;
}
