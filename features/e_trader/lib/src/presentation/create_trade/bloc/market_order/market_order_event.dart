part of 'market_order_bloc.dart';

@freezed
sealed class MarketOrderEvent with _$MarketOrderEvent {
  const factory MarketOrderEvent.orderSizeChanged(
    TradeComponentState<double, OrderSizeErrorCode> state,
  ) = _MarketOrderSizeChanged;

  const factory MarketOrderEvent.takeProfitChanged(
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state,
  ) = _TakeProfitChanged;
  const factory MarketOrderEvent.stopLossChanged(
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state,
  ) = _StopLossChanged;
  const factory MarketOrderEvent.valuesChanged(MarketOrderArgs args) =
      _ValuesChanged;
  const factory MarketOrderEvent.goToPortfolio() = _GoToPortfolioEvent;
  const factory MarketOrderEvent.submit(double currentPrice) = _Submit;
  const factory MarketOrderEvent.resetToDefaults(double minLot) =
      _ResetToDefaults;
}
