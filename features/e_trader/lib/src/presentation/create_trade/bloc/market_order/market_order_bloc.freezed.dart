// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'market_order_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MarketOrderEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderEvent()';
}


}

/// @nodoc
class $MarketOrderEventCopyWith<$Res>  {
$MarketOrderEventCopyWith(MarketOrderEvent _, $Res Function(MarketOrderEvent) __);
}


/// @nodoc


class _MarketOrderSizeChanged implements MarketOrderEvent {
  const _MarketOrderSizeChanged(this.state);
  

 final  TradeComponentState<double, OrderSizeErrorCode> state;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MarketOrderSizeChangedCopyWith<_MarketOrderSizeChanged> get copyWith => __$MarketOrderSizeChangedCopyWithImpl<_MarketOrderSizeChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MarketOrderSizeChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'MarketOrderEvent.orderSizeChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$MarketOrderSizeChangedCopyWith<$Res> implements $MarketOrderEventCopyWith<$Res> {
  factory _$MarketOrderSizeChangedCopyWith(_MarketOrderSizeChanged value, $Res Function(_MarketOrderSizeChanged) _then) = __$MarketOrderSizeChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<double, OrderSizeErrorCode> state
});


$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get state;

}
/// @nodoc
class __$MarketOrderSizeChangedCopyWithImpl<$Res>
    implements _$MarketOrderSizeChangedCopyWith<$Res> {
  __$MarketOrderSizeChangedCopyWithImpl(this._self, this._then);

  final _MarketOrderSizeChanged _self;
  final $Res Function(_MarketOrderSizeChanged) _then;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_MarketOrderSizeChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,
  ));
}

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _TakeProfitChanged implements MarketOrderEvent {
  const _TakeProfitChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TakeProfitChangedCopyWith<_TakeProfitChanged> get copyWith => __$TakeProfitChangedCopyWithImpl<_TakeProfitChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TakeProfitChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'MarketOrderEvent.takeProfitChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$TakeProfitChangedCopyWith<$Res> implements $MarketOrderEventCopyWith<$Res> {
  factory _$TakeProfitChangedCopyWith(_TakeProfitChanged value, $Res Function(_TakeProfitChanged) _then) = __$TakeProfitChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$TakeProfitChangedCopyWithImpl<$Res>
    implements _$TakeProfitChangedCopyWith<$Res> {
  __$TakeProfitChangedCopyWithImpl(this._self, this._then);

  final _TakeProfitChanged _self;
  final $Res Function(_TakeProfitChanged) _then;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_TakeProfitChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _StopLossChanged implements MarketOrderEvent {
  const _StopLossChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StopLossChangedCopyWith<_StopLossChanged> get copyWith => __$StopLossChangedCopyWithImpl<_StopLossChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StopLossChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'MarketOrderEvent.stopLossChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$StopLossChangedCopyWith<$Res> implements $MarketOrderEventCopyWith<$Res> {
  factory _$StopLossChangedCopyWith(_StopLossChanged value, $Res Function(_StopLossChanged) _then) = __$StopLossChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$StopLossChangedCopyWithImpl<$Res>
    implements _$StopLossChangedCopyWith<$Res> {
  __$StopLossChangedCopyWithImpl(this._self, this._then);

  final _StopLossChanged _self;
  final $Res Function(_StopLossChanged) _then;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_StopLossChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ValuesChanged implements MarketOrderEvent {
  const _ValuesChanged(this.args);
  

 final  MarketOrderArgs args;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ValuesChangedCopyWith<_ValuesChanged> get copyWith => __$ValuesChangedCopyWithImpl<_ValuesChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ValuesChanged&&(identical(other.args, args) || other.args == args));
}


@override
int get hashCode => Object.hash(runtimeType,args);

@override
String toString() {
  return 'MarketOrderEvent.valuesChanged(args: $args)';
}


}

/// @nodoc
abstract mixin class _$ValuesChangedCopyWith<$Res> implements $MarketOrderEventCopyWith<$Res> {
  factory _$ValuesChangedCopyWith(_ValuesChanged value, $Res Function(_ValuesChanged) _then) = __$ValuesChangedCopyWithImpl;
@useResult
$Res call({
 MarketOrderArgs args
});




}
/// @nodoc
class __$ValuesChangedCopyWithImpl<$Res>
    implements _$ValuesChangedCopyWith<$Res> {
  __$ValuesChangedCopyWithImpl(this._self, this._then);

  final _ValuesChanged _self;
  final $Res Function(_ValuesChanged) _then;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? args = null,}) {
  return _then(_ValuesChanged(
null == args ? _self.args : args // ignore: cast_nullable_to_non_nullable
as MarketOrderArgs,
  ));
}


}

/// @nodoc


class _GoToPortfolioEvent implements MarketOrderEvent {
  const _GoToPortfolioEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToPortfolioEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderEvent.goToPortfolio()';
}


}




/// @nodoc


class _Submit implements MarketOrderEvent {
  const _Submit(this.currentPrice);
  

 final  double currentPrice;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubmitCopyWith<_Submit> get copyWith => __$SubmitCopyWithImpl<_Submit>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Submit&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice));
}


@override
int get hashCode => Object.hash(runtimeType,currentPrice);

@override
String toString() {
  return 'MarketOrderEvent.submit(currentPrice: $currentPrice)';
}


}

/// @nodoc
abstract mixin class _$SubmitCopyWith<$Res> implements $MarketOrderEventCopyWith<$Res> {
  factory _$SubmitCopyWith(_Submit value, $Res Function(_Submit) _then) = __$SubmitCopyWithImpl;
@useResult
$Res call({
 double currentPrice
});




}
/// @nodoc
class __$SubmitCopyWithImpl<$Res>
    implements _$SubmitCopyWith<$Res> {
  __$SubmitCopyWithImpl(this._self, this._then);

  final _Submit _self;
  final $Res Function(_Submit) _then;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? currentPrice = null,}) {
  return _then(_Submit(
null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _ResetToDefaults implements MarketOrderEvent {
  const _ResetToDefaults(this.minLot);
  

 final  double minLot;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ResetToDefaultsCopyWith<_ResetToDefaults> get copyWith => __$ResetToDefaultsCopyWithImpl<_ResetToDefaults>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResetToDefaults&&(identical(other.minLot, minLot) || other.minLot == minLot));
}


@override
int get hashCode => Object.hash(runtimeType,minLot);

@override
String toString() {
  return 'MarketOrderEvent.resetToDefaults(minLot: $minLot)';
}


}

/// @nodoc
abstract mixin class _$ResetToDefaultsCopyWith<$Res> implements $MarketOrderEventCopyWith<$Res> {
  factory _$ResetToDefaultsCopyWith(_ResetToDefaults value, $Res Function(_ResetToDefaults) _then) = __$ResetToDefaultsCopyWithImpl;
@useResult
$Res call({
 double minLot
});




}
/// @nodoc
class __$ResetToDefaultsCopyWithImpl<$Res>
    implements _$ResetToDefaultsCopyWith<$Res> {
  __$ResetToDefaultsCopyWithImpl(this._self, this._then);

  final _ResetToDefaults _self;
  final $Res Function(_ResetToDefaults) _then;

/// Create a copy of MarketOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? minLot = null,}) {
  return _then(_ResetToDefaults(
null == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc
mixin _$MarketOrderState {

 String get accountNumber; set accountNumber(String value); MarginInformationModel? get marginInformation; set marginInformation(MarginInformationModel? value); SymbolQuoteModel get symbolQuoteModel; set symbolQuoteModel(SymbolQuoteModel value); TradeComponentState<double, OrderSizeErrorCode> get orderSizeState; set orderSizeState(TradeComponentState<double, OrderSizeErrorCode> value); TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get takeProfitState; set takeProfitState(TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> value); TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get stopLossState; set stopLossState(TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> value); TradeComponentState<void, TradeError> get marginAllocationState; set marginAllocationState(TradeComponentState<void, TradeError> value); TradeType? get tradeType; set tradeType(TradeType? value); double get openPrice; set openPrice(double value); MarketOrderProcessState get currentState; set currentState(MarketOrderProcessState value);
/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketOrderStateCopyWith<MarketOrderState> get copyWith => _$MarketOrderStateCopyWithImpl<MarketOrderState>(this as MarketOrderState, _$identity);





@override
String toString() {
  return 'MarketOrderState(accountNumber: $accountNumber, marginInformation: $marginInformation, symbolQuoteModel: $symbolQuoteModel, orderSizeState: $orderSizeState, takeProfitState: $takeProfitState, stopLossState: $stopLossState, marginAllocationState: $marginAllocationState, tradeType: $tradeType, openPrice: $openPrice, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $MarketOrderStateCopyWith<$Res>  {
  factory $MarketOrderStateCopyWith(MarketOrderState value, $Res Function(MarketOrderState) _then) = _$MarketOrderStateCopyWithImpl;
@useResult
$Res call({
 String accountNumber, MarginInformationModel? marginInformation, SymbolQuoteModel symbolQuoteModel, TradeComponentState<double, OrderSizeErrorCode> orderSizeState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, TradeComponentState<void, TradeError> marginAllocationState, TradeType? tradeType, double openPrice, MarketOrderProcessState currentState
});


$MarginInformationModelCopyWith<$Res>? get marginInformation;$SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel;$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;$TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState;$MarketOrderProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$MarketOrderStateCopyWithImpl<$Res>
    implements $MarketOrderStateCopyWith<$Res> {
  _$MarketOrderStateCopyWithImpl(this._self, this._then);

  final MarketOrderState _self;
  final $Res Function(MarketOrderState) _then;

/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountNumber = null,Object? marginInformation = freezed,Object? symbolQuoteModel = null,Object? orderSizeState = null,Object? takeProfitState = null,Object? stopLossState = null,Object? marginAllocationState = null,Object? tradeType = freezed,Object? openPrice = null,Object? currentState = null,}) {
  return _then(_self.copyWith(
accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,symbolQuoteModel: null == symbolQuoteModel ? _self.symbolQuoteModel : symbolQuoteModel // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,orderSizeState: null == orderSizeState ? _self.orderSizeState : orderSizeState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,marginAllocationState: null == marginAllocationState ? _self.marginAllocationState : marginAllocationState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<void, TradeError>,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,openPrice: null == openPrice ? _self.openPrice : openPrice // ignore: cast_nullable_to_non_nullable
as double,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as MarketOrderProcessState,
  ));
}
/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.symbolQuoteModel, (value) {
    return _then(_self.copyWith(symbolQuoteModel: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.orderSizeState, (value) {
    return _then(_self.copyWith(orderSizeState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState {
  
  return $TradeComponentStateCopyWith<void, TradeError, $Res>(_self.marginAllocationState, (value) {
    return _then(_self.copyWith(marginAllocationState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarketOrderProcessStateCopyWith<$Res> get currentState {
  
  return $MarketOrderProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _MarketOrderState extends MarketOrderState {
   _MarketOrderState({required this.accountNumber, required this.marginInformation, required this.symbolQuoteModel, required this.orderSizeState, required this.takeProfitState, required this.stopLossState, required this.marginAllocationState, required this.tradeType, required this.openPrice, this.currentState = const MarketOrderProcessState.loading()}): super._();
  

@override  String accountNumber;
@override  MarginInformationModel? marginInformation;
@override  SymbolQuoteModel symbolQuoteModel;
@override  TradeComponentState<double, OrderSizeErrorCode> orderSizeState;
@override  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState;
@override  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState;
@override  TradeComponentState<void, TradeError> marginAllocationState;
@override  TradeType? tradeType;
@override  double openPrice;
@override@JsonKey()  MarketOrderProcessState currentState;

/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MarketOrderStateCopyWith<_MarketOrderState> get copyWith => __$MarketOrderStateCopyWithImpl<_MarketOrderState>(this, _$identity);





@override
String toString() {
  return 'MarketOrderState(accountNumber: $accountNumber, marginInformation: $marginInformation, symbolQuoteModel: $symbolQuoteModel, orderSizeState: $orderSizeState, takeProfitState: $takeProfitState, stopLossState: $stopLossState, marginAllocationState: $marginAllocationState, tradeType: $tradeType, openPrice: $openPrice, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$MarketOrderStateCopyWith<$Res> implements $MarketOrderStateCopyWith<$Res> {
  factory _$MarketOrderStateCopyWith(_MarketOrderState value, $Res Function(_MarketOrderState) _then) = __$MarketOrderStateCopyWithImpl;
@override @useResult
$Res call({
 String accountNumber, MarginInformationModel? marginInformation, SymbolQuoteModel symbolQuoteModel, TradeComponentState<double, OrderSizeErrorCode> orderSizeState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, TradeComponentState<void, TradeError> marginAllocationState, TradeType? tradeType, double openPrice, MarketOrderProcessState currentState
});


@override $MarginInformationModelCopyWith<$Res>? get marginInformation;@override $SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel;@override $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;@override $TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState;@override $MarketOrderProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$MarketOrderStateCopyWithImpl<$Res>
    implements _$MarketOrderStateCopyWith<$Res> {
  __$MarketOrderStateCopyWithImpl(this._self, this._then);

  final _MarketOrderState _self;
  final $Res Function(_MarketOrderState) _then;

/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountNumber = null,Object? marginInformation = freezed,Object? symbolQuoteModel = null,Object? orderSizeState = null,Object? takeProfitState = null,Object? stopLossState = null,Object? marginAllocationState = null,Object? tradeType = freezed,Object? openPrice = null,Object? currentState = null,}) {
  return _then(_MarketOrderState(
accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,symbolQuoteModel: null == symbolQuoteModel ? _self.symbolQuoteModel : symbolQuoteModel // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,orderSizeState: null == orderSizeState ? _self.orderSizeState : orderSizeState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,marginAllocationState: null == marginAllocationState ? _self.marginAllocationState : marginAllocationState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<void, TradeError>,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,openPrice: null == openPrice ? _self.openPrice : openPrice // ignore: cast_nullable_to_non_nullable
as double,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as MarketOrderProcessState,
  ));
}

/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.symbolQuoteModel, (value) {
    return _then(_self.copyWith(symbolQuoteModel: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.orderSizeState, (value) {
    return _then(_self.copyWith(orderSizeState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState {
  
  return $TradeComponentStateCopyWith<void, TradeError, $Res>(_self.marginAllocationState, (value) {
    return _then(_self.copyWith(marginAllocationState: value));
  });
}/// Create a copy of MarketOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarketOrderProcessStateCopyWith<$Res> get currentState {
  
  return $MarketOrderProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$MarketOrderProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState()';
}


}

/// @nodoc
class $MarketOrderProcessStateCopyWith<$Res>  {
$MarketOrderProcessStateCopyWith(MarketOrderProcessState _, $Res Function(MarketOrderProcessState) __);
}


/// @nodoc


class MarketOrderLoadingProcessState implements MarketOrderProcessState {
  const MarketOrderLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState.loading()';
}


}




/// @nodoc


class MarketOrderConnectedProcessState implements MarketOrderProcessState {
  const MarketOrderConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState.connected()';
}


}




/// @nodoc


class MarketOrderDisconnectedProcessState implements MarketOrderProcessState {
  const MarketOrderDisconnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderDisconnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState.disconnected()';
}


}




/// @nodoc


class PlacingMarketOrderProcessState implements MarketOrderProcessState {
  const PlacingMarketOrderProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PlacingMarketOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState.placingOrder()';
}


}




/// @nodoc


class MarketOrderSuccessProcessState implements MarketOrderProcessState {
  const MarketOrderSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState.orderSuccess()';
}


}




/// @nodoc


class MarketOrderErrorProcessState implements MarketOrderProcessState {
  const MarketOrderErrorProcessState({this.errorCode});
  

 final  String? errorCode;

/// Create a copy of MarketOrderProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketOrderErrorProcessStateCopyWith<MarketOrderErrorProcessState> get copyWith => _$MarketOrderErrorProcessStateCopyWithImpl<MarketOrderErrorProcessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderErrorProcessState&&(identical(other.errorCode, errorCode) || other.errorCode == errorCode));
}


@override
int get hashCode => Object.hash(runtimeType,errorCode);

@override
String toString() {
  return 'MarketOrderProcessState.orderError(errorCode: $errorCode)';
}


}

/// @nodoc
abstract mixin class $MarketOrderErrorProcessStateCopyWith<$Res> implements $MarketOrderProcessStateCopyWith<$Res> {
  factory $MarketOrderErrorProcessStateCopyWith(MarketOrderErrorProcessState value, $Res Function(MarketOrderErrorProcessState) _then) = _$MarketOrderErrorProcessStateCopyWithImpl;
@useResult
$Res call({
 String? errorCode
});




}
/// @nodoc
class _$MarketOrderErrorProcessStateCopyWithImpl<$Res>
    implements $MarketOrderErrorProcessStateCopyWith<$Res> {
  _$MarketOrderErrorProcessStateCopyWithImpl(this._self, this._then);

  final MarketOrderErrorProcessState _self;
  final $Res Function(MarketOrderErrorProcessState) _then;

/// Create a copy of MarketOrderProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorCode = freezed,}) {
  return _then(MarketOrderErrorProcessState(
errorCode: freezed == errorCode ? _self.errorCode : errorCode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class MarketOrderMarketClosedProcessState implements MarketOrderProcessState {
  const MarketOrderMarketClosedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketOrderMarketClosedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketOrderProcessState.marketClosed()';
}


}




// dart format on
