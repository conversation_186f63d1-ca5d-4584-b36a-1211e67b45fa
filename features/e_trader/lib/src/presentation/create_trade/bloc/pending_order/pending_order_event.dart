part of 'pending_order_bloc.dart';

@freezed
sealed class PendingOrderEvent with _$PendingOrderEvent {
  const factory PendingOrderEvent.orderSizeChanged(
    TradeComponentState<double, OrderSizeErrorCode> state,
  ) = _PendingOrderSizeChanged;
  const factory PendingOrderEvent.priceChanged(
    TradeComponentState<double, OrderPriceErrorCode> state,
  ) = _PendingOrderPriceChanged;
  const factory PendingOrderEvent.takeProfitChanged(
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state,
  ) = _TakeProfitChanged;
  const factory PendingOrderEvent.stopLossChanged(
    TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state,
  ) = _StopLossChanged;
  const factory PendingOrderEvent.valuesChanged(PendingOrderArgs args) =
      _ValuesChanged;
  const factory PendingOrderEvent.goToPortfolio() = _GoToPortfolioEvent;
  const factory PendingOrderEvent.submit(double currentPrice) = _Submit;
  const factory PendingOrderEvent.resetToDefaults(double minLot) =
      _ResetToDefaults;
}
