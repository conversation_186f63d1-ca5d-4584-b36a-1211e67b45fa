// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pending_order_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PendingOrderEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderEvent()';
}


}

/// @nodoc
class $PendingOrderEventCopyWith<$Res>  {
$PendingOrderEventCopyWith(PendingOrderEvent _, $Res Function(PendingOrderEvent) __);
}


/// @nodoc


class _PendingOrderSizeChanged implements PendingOrderEvent {
  const _PendingOrderSizeChanged(this.state);
  

 final  TradeComponentState<double, OrderSizeErrorCode> state;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PendingOrderSizeChangedCopyWith<_PendingOrderSizeChanged> get copyWith => __$PendingOrderSizeChangedCopyWithImpl<_PendingOrderSizeChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PendingOrderSizeChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'PendingOrderEvent.orderSizeChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$PendingOrderSizeChangedCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$PendingOrderSizeChangedCopyWith(_PendingOrderSizeChanged value, $Res Function(_PendingOrderSizeChanged) _then) = __$PendingOrderSizeChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<double, OrderSizeErrorCode> state
});


$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get state;

}
/// @nodoc
class __$PendingOrderSizeChangedCopyWithImpl<$Res>
    implements _$PendingOrderSizeChangedCopyWith<$Res> {
  __$PendingOrderSizeChangedCopyWithImpl(this._self, this._then);

  final _PendingOrderSizeChanged _self;
  final $Res Function(_PendingOrderSizeChanged) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_PendingOrderSizeChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,
  ));
}

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _PendingOrderPriceChanged implements PendingOrderEvent {
  const _PendingOrderPriceChanged(this.state);
  

 final  TradeComponentState<double, OrderPriceErrorCode> state;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PendingOrderPriceChangedCopyWith<_PendingOrderPriceChanged> get copyWith => __$PendingOrderPriceChangedCopyWithImpl<_PendingOrderPriceChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PendingOrderPriceChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'PendingOrderEvent.priceChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$PendingOrderPriceChangedCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$PendingOrderPriceChangedCopyWith(_PendingOrderPriceChanged value, $Res Function(_PendingOrderPriceChanged) _then) = __$PendingOrderPriceChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<double, OrderPriceErrorCode> state
});


$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get state;

}
/// @nodoc
class __$PendingOrderPriceChangedCopyWithImpl<$Res>
    implements _$PendingOrderPriceChangedCopyWith<$Res> {
  __$PendingOrderPriceChangedCopyWithImpl(this._self, this._then);

  final _PendingOrderPriceChanged _self;
  final $Res Function(_PendingOrderPriceChanged) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_PendingOrderPriceChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderPriceErrorCode>,
  ));
}

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _TakeProfitChanged implements PendingOrderEvent {
  const _TakeProfitChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TakeProfitChangedCopyWith<_TakeProfitChanged> get copyWith => __$TakeProfitChangedCopyWithImpl<_TakeProfitChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TakeProfitChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'PendingOrderEvent.takeProfitChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$TakeProfitChangedCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$TakeProfitChangedCopyWith(_TakeProfitChanged value, $Res Function(_TakeProfitChanged) _then) = __$TakeProfitChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$TakeProfitChangedCopyWithImpl<$Res>
    implements _$TakeProfitChangedCopyWith<$Res> {
  __$TakeProfitChangedCopyWithImpl(this._self, this._then);

  final _TakeProfitChanged _self;
  final $Res Function(_TakeProfitChanged) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_TakeProfitChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _StopLossChanged implements PendingOrderEvent {
  const _StopLossChanged(this.state);
  

 final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$StopLossChangedCopyWith<_StopLossChanged> get copyWith => __$StopLossChangedCopyWithImpl<_StopLossChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StopLossChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'PendingOrderEvent.stopLossChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$StopLossChangedCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$StopLossChangedCopyWith(_StopLossChanged value, $Res Function(_StopLossChanged) _then) = __$StopLossChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> state
});


$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state;

}
/// @nodoc
class __$StopLossChangedCopyWithImpl<$Res>
    implements _$StopLossChangedCopyWith<$Res> {
  __$StopLossChangedCopyWithImpl(this._self, this._then);

  final _StopLossChanged _self;
  final $Res Function(_StopLossChanged) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_StopLossChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,
  ));
}

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ValuesChanged implements PendingOrderEvent {
  const _ValuesChanged(this.args);
  

 final  PendingOrderArgs args;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ValuesChangedCopyWith<_ValuesChanged> get copyWith => __$ValuesChangedCopyWithImpl<_ValuesChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ValuesChanged&&(identical(other.args, args) || other.args == args));
}


@override
int get hashCode => Object.hash(runtimeType,args);

@override
String toString() {
  return 'PendingOrderEvent.valuesChanged(args: $args)';
}


}

/// @nodoc
abstract mixin class _$ValuesChangedCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$ValuesChangedCopyWith(_ValuesChanged value, $Res Function(_ValuesChanged) _then) = __$ValuesChangedCopyWithImpl;
@useResult
$Res call({
 PendingOrderArgs args
});




}
/// @nodoc
class __$ValuesChangedCopyWithImpl<$Res>
    implements _$ValuesChangedCopyWith<$Res> {
  __$ValuesChangedCopyWithImpl(this._self, this._then);

  final _ValuesChanged _self;
  final $Res Function(_ValuesChanged) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? args = null,}) {
  return _then(_ValuesChanged(
null == args ? _self.args : args // ignore: cast_nullable_to_non_nullable
as PendingOrderArgs,
  ));
}


}

/// @nodoc


class _GoToPortfolioEvent implements PendingOrderEvent {
  const _GoToPortfolioEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToPortfolioEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderEvent.goToPortfolio()';
}


}




/// @nodoc


class _Submit implements PendingOrderEvent {
  const _Submit(this.currentPrice);
  

 final  double currentPrice;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubmitCopyWith<_Submit> get copyWith => __$SubmitCopyWithImpl<_Submit>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Submit&&(identical(other.currentPrice, currentPrice) || other.currentPrice == currentPrice));
}


@override
int get hashCode => Object.hash(runtimeType,currentPrice);

@override
String toString() {
  return 'PendingOrderEvent.submit(currentPrice: $currentPrice)';
}


}

/// @nodoc
abstract mixin class _$SubmitCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$SubmitCopyWith(_Submit value, $Res Function(_Submit) _then) = __$SubmitCopyWithImpl;
@useResult
$Res call({
 double currentPrice
});




}
/// @nodoc
class __$SubmitCopyWithImpl<$Res>
    implements _$SubmitCopyWith<$Res> {
  __$SubmitCopyWithImpl(this._self, this._then);

  final _Submit _self;
  final $Res Function(_Submit) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? currentPrice = null,}) {
  return _then(_Submit(
null == currentPrice ? _self.currentPrice : currentPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc


class _ResetToDefaults implements PendingOrderEvent {
  const _ResetToDefaults(this.minLot);
  

 final  double minLot;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ResetToDefaultsCopyWith<_ResetToDefaults> get copyWith => __$ResetToDefaultsCopyWithImpl<_ResetToDefaults>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ResetToDefaults&&(identical(other.minLot, minLot) || other.minLot == minLot));
}


@override
int get hashCode => Object.hash(runtimeType,minLot);

@override
String toString() {
  return 'PendingOrderEvent.resetToDefaults(minLot: $minLot)';
}


}

/// @nodoc
abstract mixin class _$ResetToDefaultsCopyWith<$Res> implements $PendingOrderEventCopyWith<$Res> {
  factory _$ResetToDefaultsCopyWith(_ResetToDefaults value, $Res Function(_ResetToDefaults) _then) = __$ResetToDefaultsCopyWithImpl;
@useResult
$Res call({
 double minLot
});




}
/// @nodoc
class __$ResetToDefaultsCopyWithImpl<$Res>
    implements _$ResetToDefaultsCopyWith<$Res> {
  __$ResetToDefaultsCopyWithImpl(this._self, this._then);

  final _ResetToDefaults _self;
  final $Res Function(_ResetToDefaults) _then;

/// Create a copy of PendingOrderEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? minLot = null,}) {
  return _then(_ResetToDefaults(
null == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc
mixin _$PendingOrderState {

 String get accountNumber; MarginInformationModel? get marginInformation; SymbolQuoteModel get symbolQuoteModel; TradeComponentState<double, OrderSizeErrorCode> get orderSizeState; TradeComponentState<double, OrderPriceErrorCode> get orderPriceState; TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get takeProfitState; TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> get stopLossState; TradeComponentState<void, TradeError> get marginAllocationState; TradeType? get tradeType; PendingOrderProcessState get currentState;
/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PendingOrderStateCopyWith<PendingOrderState> get copyWith => _$PendingOrderStateCopyWithImpl<PendingOrderState>(this as PendingOrderState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderState&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.marginInformation, marginInformation) || other.marginInformation == marginInformation)&&(identical(other.symbolQuoteModel, symbolQuoteModel) || other.symbolQuoteModel == symbolQuoteModel)&&(identical(other.orderSizeState, orderSizeState) || other.orderSizeState == orderSizeState)&&(identical(other.orderPriceState, orderPriceState) || other.orderPriceState == orderPriceState)&&(identical(other.takeProfitState, takeProfitState) || other.takeProfitState == takeProfitState)&&(identical(other.stopLossState, stopLossState) || other.stopLossState == stopLossState)&&(identical(other.marginAllocationState, marginAllocationState) || other.marginAllocationState == marginAllocationState)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType)&&(identical(other.currentState, currentState) || other.currentState == currentState));
}


@override
int get hashCode => Object.hash(runtimeType,accountNumber,marginInformation,symbolQuoteModel,orderSizeState,orderPriceState,takeProfitState,stopLossState,marginAllocationState,tradeType,currentState);

@override
String toString() {
  return 'PendingOrderState(accountNumber: $accountNumber, marginInformation: $marginInformation, symbolQuoteModel: $symbolQuoteModel, orderSizeState: $orderSizeState, orderPriceState: $orderPriceState, takeProfitState: $takeProfitState, stopLossState: $stopLossState, marginAllocationState: $marginAllocationState, tradeType: $tradeType, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $PendingOrderStateCopyWith<$Res>  {
  factory $PendingOrderStateCopyWith(PendingOrderState value, $Res Function(PendingOrderState) _then) = _$PendingOrderStateCopyWithImpl;
@useResult
$Res call({
 String accountNumber, MarginInformationModel? marginInformation, SymbolQuoteModel symbolQuoteModel, TradeComponentState<double, OrderSizeErrorCode> orderSizeState, TradeComponentState<double, OrderPriceErrorCode> orderPriceState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, TradeComponentState<void, TradeError> marginAllocationState, TradeType? tradeType, PendingOrderProcessState currentState
});


$MarginInformationModelCopyWith<$Res>? get marginInformation;$SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel;$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState;$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;$TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState;$PendingOrderProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class _$PendingOrderStateCopyWithImpl<$Res>
    implements $PendingOrderStateCopyWith<$Res> {
  _$PendingOrderStateCopyWithImpl(this._self, this._then);

  final PendingOrderState _self;
  final $Res Function(PendingOrderState) _then;

/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountNumber = null,Object? marginInformation = freezed,Object? symbolQuoteModel = null,Object? orderSizeState = null,Object? orderPriceState = null,Object? takeProfitState = null,Object? stopLossState = null,Object? marginAllocationState = null,Object? tradeType = freezed,Object? currentState = null,}) {
  return _then(_self.copyWith(
accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,symbolQuoteModel: null == symbolQuoteModel ? _self.symbolQuoteModel : symbolQuoteModel // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,orderSizeState: null == orderSizeState ? _self.orderSizeState : orderSizeState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,orderPriceState: null == orderPriceState ? _self.orderPriceState : orderPriceState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderPriceErrorCode>,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,marginAllocationState: null == marginAllocationState ? _self.marginAllocationState : marginAllocationState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<void, TradeError>,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as PendingOrderProcessState,
  ));
}
/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.symbolQuoteModel, (value) {
    return _then(_self.copyWith(symbolQuoteModel: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.orderSizeState, (value) {
    return _then(_self.copyWith(orderSizeState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState {
  
  return $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res>(_self.orderPriceState, (value) {
    return _then(_self.copyWith(orderPriceState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState {
  
  return $TradeComponentStateCopyWith<void, TradeError, $Res>(_self.marginAllocationState, (value) {
    return _then(_self.copyWith(marginAllocationState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PendingOrderProcessStateCopyWith<$Res> get currentState {
  
  return $PendingOrderProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc


class _PendingOrderState extends PendingOrderState {
  const _PendingOrderState({required this.accountNumber, this.marginInformation, required this.symbolQuoteModel, required this.orderSizeState, required this.orderPriceState, required this.takeProfitState, required this.stopLossState, required this.marginAllocationState, this.tradeType, this.currentState = const PendingOrderProcessState.loading()}): super._();
  

@override final  String accountNumber;
@override final  MarginInformationModel? marginInformation;
@override final  SymbolQuoteModel symbolQuoteModel;
@override final  TradeComponentState<double, OrderSizeErrorCode> orderSizeState;
@override final  TradeComponentState<double, OrderPriceErrorCode> orderPriceState;
@override final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState;
@override final  TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState;
@override final  TradeComponentState<void, TradeError> marginAllocationState;
@override final  TradeType? tradeType;
@override@JsonKey() final  PendingOrderProcessState currentState;

/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PendingOrderStateCopyWith<_PendingOrderState> get copyWith => __$PendingOrderStateCopyWithImpl<_PendingOrderState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PendingOrderState&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.marginInformation, marginInformation) || other.marginInformation == marginInformation)&&(identical(other.symbolQuoteModel, symbolQuoteModel) || other.symbolQuoteModel == symbolQuoteModel)&&(identical(other.orderSizeState, orderSizeState) || other.orderSizeState == orderSizeState)&&(identical(other.orderPriceState, orderPriceState) || other.orderPriceState == orderPriceState)&&(identical(other.takeProfitState, takeProfitState) || other.takeProfitState == takeProfitState)&&(identical(other.stopLossState, stopLossState) || other.stopLossState == stopLossState)&&(identical(other.marginAllocationState, marginAllocationState) || other.marginAllocationState == marginAllocationState)&&(identical(other.tradeType, tradeType) || other.tradeType == tradeType)&&(identical(other.currentState, currentState) || other.currentState == currentState));
}


@override
int get hashCode => Object.hash(runtimeType,accountNumber,marginInformation,symbolQuoteModel,orderSizeState,orderPriceState,takeProfitState,stopLossState,marginAllocationState,tradeType,currentState);

@override
String toString() {
  return 'PendingOrderState(accountNumber: $accountNumber, marginInformation: $marginInformation, symbolQuoteModel: $symbolQuoteModel, orderSizeState: $orderSizeState, orderPriceState: $orderPriceState, takeProfitState: $takeProfitState, stopLossState: $stopLossState, marginAllocationState: $marginAllocationState, tradeType: $tradeType, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$PendingOrderStateCopyWith<$Res> implements $PendingOrderStateCopyWith<$Res> {
  factory _$PendingOrderStateCopyWith(_PendingOrderState value, $Res Function(_PendingOrderState) _then) = __$PendingOrderStateCopyWithImpl;
@override @useResult
$Res call({
 String accountNumber, MarginInformationModel? marginInformation, SymbolQuoteModel symbolQuoteModel, TradeComponentState<double, OrderSizeErrorCode> orderSizeState, TradeComponentState<double, OrderPriceErrorCode> orderPriceState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> takeProfitState, TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode> stopLossState, TradeComponentState<void, TradeError> marginAllocationState, TradeType? tradeType, PendingOrderProcessState currentState
});


@override $MarginInformationModelCopyWith<$Res>? get marginInformation;@override $SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel;@override $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState;@override $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState;@override $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState;@override $TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState;@override $PendingOrderProcessStateCopyWith<$Res> get currentState;

}
/// @nodoc
class __$PendingOrderStateCopyWithImpl<$Res>
    implements _$PendingOrderStateCopyWith<$Res> {
  __$PendingOrderStateCopyWithImpl(this._self, this._then);

  final _PendingOrderState _self;
  final $Res Function(_PendingOrderState) _then;

/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountNumber = null,Object? marginInformation = freezed,Object? symbolQuoteModel = null,Object? orderSizeState = null,Object? orderPriceState = null,Object? takeProfitState = null,Object? stopLossState = null,Object? marginAllocationState = null,Object? tradeType = freezed,Object? currentState = null,}) {
  return _then(_PendingOrderState(
accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,marginInformation: freezed == marginInformation ? _self.marginInformation : marginInformation // ignore: cast_nullable_to_non_nullable
as MarginInformationModel?,symbolQuoteModel: null == symbolQuoteModel ? _self.symbolQuoteModel : symbolQuoteModel // ignore: cast_nullable_to_non_nullable
as SymbolQuoteModel,orderSizeState: null == orderSizeState ? _self.orderSizeState : orderSizeState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,orderPriceState: null == orderPriceState ? _self.orderPriceState : orderPriceState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderPriceErrorCode>,takeProfitState: null == takeProfitState ? _self.takeProfitState : takeProfitState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,stopLossState: null == stopLossState ? _self.stopLossState : stopLossState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<OrderLimitCalculation, OrderLimitErrorCode>,marginAllocationState: null == marginAllocationState ? _self.marginAllocationState : marginAllocationState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<void, TradeError>,tradeType: freezed == tradeType ? _self.tradeType : tradeType // ignore: cast_nullable_to_non_nullable
as TradeType?,currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as PendingOrderProcessState,
  ));
}

/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarginInformationModelCopyWith<$Res>? get marginInformation {
    if (_self.marginInformation == null) {
    return null;
  }

  return $MarginInformationModelCopyWith<$Res>(_self.marginInformation!, (value) {
    return _then(_self.copyWith(marginInformation: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SymbolQuoteModelCopyWith<$Res> get symbolQuoteModel {
  
  return $SymbolQuoteModelCopyWith<$Res>(_self.symbolQuoteModel, (value) {
    return _then(_self.copyWith(symbolQuoteModel: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.orderSizeState, (value) {
    return _then(_self.copyWith(orderSizeState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res> get orderPriceState {
  
  return $TradeComponentStateCopyWith<double, OrderPriceErrorCode, $Res>(_self.orderPriceState, (value) {
    return _then(_self.copyWith(orderPriceState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get takeProfitState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.takeProfitState, (value) {
    return _then(_self.copyWith(takeProfitState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res> get stopLossState {
  
  return $TradeComponentStateCopyWith<OrderLimitCalculation, OrderLimitErrorCode, $Res>(_self.stopLossState, (value) {
    return _then(_self.copyWith(stopLossState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<void, TradeError, $Res> get marginAllocationState {
  
  return $TradeComponentStateCopyWith<void, TradeError, $Res>(_self.marginAllocationState, (value) {
    return _then(_self.copyWith(marginAllocationState: value));
  });
}/// Create a copy of PendingOrderState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PendingOrderProcessStateCopyWith<$Res> get currentState {
  
  return $PendingOrderProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}

/// @nodoc
mixin _$PendingOrderProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState()';
}


}

/// @nodoc
class $PendingOrderProcessStateCopyWith<$Res>  {
$PendingOrderProcessStateCopyWith(PendingOrderProcessState _, $Res Function(PendingOrderProcessState) __);
}


/// @nodoc


class PendingOrderLoadingProcessState implements PendingOrderProcessState {
  const PendingOrderLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState.loading()';
}


}




/// @nodoc


class PendingOrderConnectedProcessState implements PendingOrderProcessState {
  const PendingOrderConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState.connected()';
}


}




/// @nodoc


class PendingOrderDisconnectedProcessState implements PendingOrderProcessState {
  const PendingOrderDisconnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderDisconnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState.disconnected()';
}


}




/// @nodoc


class PendingOrderPlacingProcessState implements PendingOrderProcessState {
  const PendingOrderPlacingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderPlacingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState.placingOrder()';
}


}




/// @nodoc


class PendingOrderSuccessProcessState implements PendingOrderProcessState {
  const PendingOrderSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState.orderSuccess()';
}


}




/// @nodoc


class PendingOrderErrorProcessState implements PendingOrderProcessState {
  const PendingOrderErrorProcessState({this.errorMessage});
  

 final  String? errorMessage;

/// Create a copy of PendingOrderProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PendingOrderErrorProcessStateCopyWith<PendingOrderErrorProcessState> get copyWith => _$PendingOrderErrorProcessStateCopyWithImpl<PendingOrderErrorProcessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderErrorProcessState&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'PendingOrderProcessState.orderError(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $PendingOrderErrorProcessStateCopyWith<$Res> implements $PendingOrderProcessStateCopyWith<$Res> {
  factory $PendingOrderErrorProcessStateCopyWith(PendingOrderErrorProcessState value, $Res Function(PendingOrderErrorProcessState) _then) = _$PendingOrderErrorProcessStateCopyWithImpl;
@useResult
$Res call({
 String? errorMessage
});




}
/// @nodoc
class _$PendingOrderErrorProcessStateCopyWithImpl<$Res>
    implements $PendingOrderErrorProcessStateCopyWith<$Res> {
  _$PendingOrderErrorProcessStateCopyWithImpl(this._self, this._then);

  final PendingOrderErrorProcessState _self;
  final $Res Function(PendingOrderErrorProcessState) _then;

/// Create a copy of PendingOrderProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = freezed,}) {
  return _then(PendingOrderErrorProcessState(
errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class PendingOrderMarketClosedProcessState implements PendingOrderProcessState {
  const PendingOrderMarketClosedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PendingOrderMarketClosedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PendingOrderProcessState.marketClosed()';
}


}




// dart format on
