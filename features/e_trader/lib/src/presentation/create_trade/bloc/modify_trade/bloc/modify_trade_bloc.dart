import 'dart:async';

import 'package:e_trader/src/data/api/modify_trade_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/margin_requirment_hub_request_model.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/data/socket/position_response.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/model/margin_requirment.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/order_limit_error_code.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_margin_requirment_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_margin_requirment_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:e_trader/src/presentation/model/pip_information_model.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';
import 'package:rxdart/rxdart.dart';

part 'modify_trade_bloc.freezed.dart';
part 'modify_trade_event.dart';
part 'modify_trade_state.dart';

class ModifyTradeBloc extends Bloc<ModifyTradeEvent, ModifyTradeState> {
  ModifyTradeBloc(
    SubscribeToPositionsUseCase subscribeToPositionUseCase,
    ModifyTradeUseCase modifyTradeUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    SubscribeToMarginRequirmentUseCase subscribeToMarginRequirmentUseCase,
    UpdatePositionsUseCase updatePositionsUseCase,
    UpdateMarginRequirmentUseCase updateMarginRequirmentUseCase,
    PositionModel positionModel,
    TradingAnalytics tradingAnalyticsEvent,
  ) : _subscribeToPositionUseCase = subscribeToPositionUseCase,
      _modifyTradeUseCase = modifyTradeUseCase,
      _getAccountNumberUseCase = getAccountNumberUseCase,
      _subscribeToMarginRequirmentUseCase = subscribeToMarginRequirmentUseCase,
      _updatePositionsUseCase = updatePositionsUseCase,
      _updateMarginRequirmentUseCase = updateMarginRequirmentUseCase,
      _tradingAnalyticsEvent = tradingAnalyticsEvent,
      super(_ModifyTradeState(positionModel: positionModel)) {
    on<ModifyTradeEvent>((event, emit) async {
      await switch (event) {
        _Initialize() => _initialize(emit),
        _Resubscribe() => _resubscribe(emit),
        _ModifyTradeTakeProfitChanged value => _takeProfitChanged(value, emit),
        _ModifyTradeStopLossChanged value => _stopLossChanged(value, emit),
        _ModifyTradeSubmit() => _submit(emit),
      };
    });
    add(ModifyTradeEvent.initialize());
  }

  final SubscribeToPositionsUseCase _subscribeToPositionUseCase;
  final ModifyTradeUseCase _modifyTradeUseCase;
  final SubscribeToMarginRequirmentUseCase _subscribeToMarginRequirmentUseCase;
  final UpdatePositionsUseCase _updatePositionsUseCase;
  final UpdateMarginRequirmentUseCase _updateMarginRequirmentUseCase;

  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final TradingAnalytics _tradingAnalyticsEvent;

  FutureOr<void> _initialize(Emitter<ModifyTradeState> emit) async {
    final result =
        await TaskEither<Exception, (List<Stream<Object?>>, String)>.Do((
          $,
        ) async {
          final accountNumber = await $(
            _getAccountNumberUseCase().toTaskEither(),
          );
          final streams = await $(
            TaskEither.sequenceList([
              _subscribeToPositionUseCase(
                subscriberId: '${ModifyTradeBloc}_$hashCode',
                eventType: TradingSocketEvent.positions.subscribe,
                symbolName: state.positionModel.platformName,
                positionId: int.tryParse(state.positionModel.positionId)!,
              ),
              _getMarginRequirementHubStream(
                symbolCode: state.positionModel.platformName,
                tradeType: state.positionModel.positionType,
                volume: state.positionModel.volume,
                accountNumber: accountNumber,
              ),
            ]),
          );

          return (streams, accountNumber);
        }).run();

    return result.fold(
      (error) {
        addError(error);
        emit(
          state.copyWith(currentState: ModifyTradeProcessState.disconnected()),
        );
      },
      (value) => emit.forEach(
        Rx.combineLatest(value.$1, (List<Object?> data) {
          final positionModel =
              (data.firstOrNull as PositionResponse?)?.position;
          final marginRequirement = data.elementAtOrNull(1) as MarginRequirment;
          return (positionModel, marginRequirement);
        }),
        onData: (streams) {
          final positionModel = streams.$1;
          final marginRequirement = streams.$2;
          final accountNumber = value.$2;
          PositionModel selectedPositionModel = state.positionModel;
          if (positionModel != null &&
              positionModel.positionId == selectedPositionModel.positionId) {
            selectedPositionModel = positionModel;
          }
          return state.copyWith(
            positionModel: selectedPositionModel,
            accountNumber: accountNumber,
            marginInformation: _getMarginInformation(marginRequirement),
            currentState: ModifyTradeProcessState.connected(),
          );
        },
        onError: (error, stackTrace) {
          addError(error, stackTrace);
          return state.copyWith(
            currentState: ModifyTradeProcessState.disconnected(),
          );
        },
      ),
    );
  }

  FutureOr<void> _resubscribe(Emitter<ModifyTradeState> emit) async {
    try {
      // Resubscribe to positions
      await _updatePositionsUseCase(
        eventType: TradingSocketEvent.positions.subscribe,
        symbolName: state.positionModel.platformName,
      );

      // Resubscribe to margin requirements if account number is available
      if (state.accountNumber != null) {
        await _updateMarginRequirmentUseCase(
          MarginRequirmentHubRequestModel(
            accountNumber: state.accountNumber!,
            symbol: state.positionModel.platformName,
            volume: state.positionModel.volume,
            tradeType: state.positionModel.positionType,
          ),
          TradingSocketEvent.marginRequirements.subscribe,
        );
      }
    } catch (e) {
      addError(e);
      emit(
        state.copyWith(currentState: ModifyTradeProcessState.disconnected()),
      );
    }
  }

  MarginInformationModel _getMarginInformation(
    MarginRequirment marginRequirmentModel,
  ) {
    var marginInformation = state.marginInformation;
    if (marginInformation == null) {
      marginInformation = MarginInformationModel(
        marginFree: marginRequirmentModel.marginFree,
        requiredMargin: marginRequirmentModel.requiredMargin,
        requiredMarginPercentage:
            marginRequirmentModel.requiredMarginPercentage,
        maxLot: marginRequirmentModel.maxLot,
        minLot: marginRequirmentModel.minLot,
        notionalValue: marginRequirmentModel.notionalValue,
        pipInformation: PipInformationModel(
          pipValue: marginRequirmentModel.pipValue,
          onePip: marginRequirmentModel.onePip,
          pipMultipler: marginRequirmentModel.multiply,
        ),
      );
    } else {
      marginInformation.marginFree = marginRequirmentModel.marginFree;
      marginInformation.requiredMargin = marginRequirmentModel.requiredMargin;
      marginInformation.requiredMarginPercentage =
          marginRequirmentModel.requiredMarginPercentage;
      marginInformation.maxLot = marginRequirmentModel.maxLot;
      marginInformation.minLot = marginRequirmentModel.minLot;
      marginInformation.notionalValue = marginRequirmentModel.notionalValue;

      marginInformation.pipInformation.pipValue =
          marginRequirmentModel.pipValue;
      marginInformation.pipInformation.onePip = marginRequirmentModel.onePip;
      marginInformation.pipInformation.pipMultipler =
          marginRequirmentModel.multiply;
    }
    return marginInformation;
  }

  TaskEither<Exception, Stream<MarginRequirment>>
  _getMarginRequirementHubStream({
    required int volume,
    required TradeType tradeType,
    required String symbolCode,
    required String accountNumber,
  }) {
    final model = MarginRequirmentHubRequestModel(
      accountNumber: accountNumber,
      symbol: symbolCode,
      volume: volume,
      tradeType: tradeType,
    );

    return _subscribeToMarginRequirmentUseCase(
      model,
      subscriberId: '${ModifyTradeBloc}_$hashCode',
      eventType: TradingSocketEvent.marginRequirements.subscribe,
    );
  }

  FutureOr<void> _takeProfitChanged(
    _ModifyTradeTakeProfitChanged value,
    Emitter<ModifyTradeState> emit,
  ) {
    emit(state.copyWith(takeProfitState: value.state));
  }

  FutureOr<void> _stopLossChanged(
    _ModifyTradeStopLossChanged value,
    Emitter<ModifyTradeState> emit,
  ) {
    emit(state.copyWith(stopLossState: value.state));
  }

  Future<void> _submit(Emitter<ModifyTradeState> emit) async {
    emit(state.copyWith(currentState: ModifyTradeProcessState.placingOrder()));
    final interactionId = await _tradingAnalyticsEvent.startInteraction(
      TradeAnalyticsEvent.startModifyTrade.eventName,
    );
    _tradingAnalyticsEvent.startModifyTrade(
      state.positionModel.platformName,
      state.positionModel.positionId,
      state.takeProfit.toString(),
      state.stopLoss.toString(),
      interactionId,
    );

    final result =
        await _modifyTradeUseCase(
          ModifyTradeModel(
            accountNumber: state.accountNumber!,
            positionId: state.positionModel.positionId.toString(),
            takeProfit: state.takeProfit,
            stopLoss: state.stopLoss,
          ),
        ).run();
    result.fold(
      (exception) {
        if (exception is PositionsAndOrdersException &&
            exception.errors.containsKey("IsMarketOpen")) {
          final isMarketOpenError =
              exception.errors["IsMarketOpen"]?.firstOrNull;
          if (isMarketOpenError?.toLowerCase() == "false") {
            emit(
              state.copyWith(
                currentState: ModifyTradeProcessState.marketClosed(),
              ),
            );
            return;
          }
        } else
          emit(
            state.copyWith(currentState: ModifyTradeProcessState.orderError()),
          );

        addError(exception);
        _tradingAnalyticsEvent.modifyTradeResult(
          false,
          state.positionModel.platformName,
          state.positionModel.positionId,
          state.takeProfit.toString(),
          state.stopLoss.toString(),
          interactionId,
        );
      },
      (response) {
        emit(
          state.copyWith(currentState: ModifyTradeProcessState.orderSuccess()),
        );
        _tradingAnalyticsEvent.modifyTradeResult(
          true,
          state.positionModel.platformName,
          state.positionModel.positionId,
          state.takeProfit.toString(),
          state.stopLoss.toString(),
          interactionId,
        );
      },
    );
    _tradingAnalyticsEvent.endInteraction(interactionId);
  }
}
