import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/flags/trader_flags.dart';
import 'package:e_trader/src/domain/formatter/decimal_text_input_formatter.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/validators/trade_error.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/input_order_size_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/order_size_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

typedef OrderSizeArgs =
    ({
      double minLot,
      double maxLot,
      double initialOrderSize,
      bool isDisabled,
      double lotsSteps,
    });

class InputOrderSizeWidget extends StatelessWidget {
  const InputOrderSizeWidget({
    super.key,
    required this.args,
    required this.onOrderSizeChanged,
    this.tradeType,
    this.showBorder = true,
    this.options = const [],
    this.footer,
    this.marginAllocationState,
    this.onResetCallbackReady,
    this.semanticsIdentifier,
    this.orderType,
    this.isMarketClosed,
  });
  final List<String> options;
  final OrderSizeArgs args;
  final void Function(TradeComponentState<double, OrderSizeErrorCode> state)
  onOrderSizeChanged;
  final bool showBorder;
  final Widget? footer;
  final TradeComponentState<void, TradeError>? marginAllocationState;
  final TradeType? tradeType;
  final void Function(VoidCallback resetCallback)? onResetCallbackReady;
  final String? semanticsIdentifier;
  final OrderType? orderType;
  final bool? isMarketClosed;

  @override
  Widget build(BuildContext buildContext) {
    return BlocProvider(
      create:
          (context) =>
              args.isDisabled
                  ? InputOrderSizeBloc(args: args)
                  : diContainer<InputOrderSizeBloc>(param1: args),
      child: _InputOrderSizeContent(
        args: args,
        options: options,
        showBorder: showBorder,
        onOrderSizeChanged: onOrderSizeChanged,
        footer: footer,
        marginAllocationState: marginAllocationState,
        initLocale: Localizations.localeOf(buildContext).toString(),
        tradeType: tradeType,
        onResetCallbackReady: onResetCallbackReady,
        semanticsIdentifier: semanticsIdentifier,
        orderType: orderType,
        isMarketClosed: isMarketClosed,
      ),
    );
  }
}

class _InputOrderSizeContent extends StatefulWidget {
  const _InputOrderSizeContent({
    required this.args,
    required this.onOrderSizeChanged,
    required this.showBorder,
    this.options = const [],
    this.footer,
    required this.initLocale,
    this.marginAllocationState,
    this.tradeType,
    this.onResetCallbackReady,
    this.semanticsIdentifier,
    this.orderType,
    this.isMarketClosed,
  });

  final OrderSizeArgs args;
  final List<String> options;
  final bool showBorder;
  final void Function(TradeComponentState<double, OrderSizeErrorCode> state)
  onOrderSizeChanged;
  final Widget? footer;
  final String initLocale;
  final TradeComponentState<void, TradeError>? marginAllocationState;
  final TradeType? tradeType;
  final void Function(VoidCallback resetCallback)? onResetCallbackReady;
  final String? semanticsIdentifier;
  final OrderType? orderType;
  final bool? isMarketClosed;

  @override
  State<_InputOrderSizeContent> createState() => _InputOrderSizeContentState();
}

class _InputOrderSizeContentState extends State<_InputOrderSizeContent> {
  late final TextEditingController _inputController;
  DuploToast toast = DuploToast();

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
    _inputController.text = EquitiFormatter.formatNumber(
      value: widget.args.initialOrderSize,
      locale: widget.initLocale,
    );

    // Expose the reset function to the parent
    widget.onResetCallbackReady?.call(resetToMinLot);
  }

  void resetToMinLot() {
    final minLotStr = EquitiFormatter.formatNumber(
      value: widget.args.minLot,
      locale: Localizations.localeOf(context).toString(),
    );
    _inputController.text = minLotStr;

    context.read<InputOrderSizeBloc>().add(
      InputOrderSizeEvent.updateOrderSize(
        args: widget.args,
        orderSize: minLotStr,
      ),
    );
  }

  @override
  void didUpdateWidget(covariant _InputOrderSizeContent oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.args != widget.args) {
      final minLotStr = EquitiFormatter.formatNumber(
        value: widget.args.initialOrderSize,
        locale: Localizations.localeOf(context).toString(),
      );

      context.read<InputOrderSizeBloc>().add(
        InputOrderSizeEvent.updateOrderSize(
          args: widget.args,
          orderSize: minLotStr,
        ),
      );
    }
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  void _updateInputValue(
    TradeComponentState<double, OrderSizeErrorCode> state,
  ) {
    final value = EquitiFormatter.formatNumber(
      value: state.value,
      locale: Localizations.localeOf(context).toString(),
    );
    _inputController.text = value;
    _inputController.selection = TextSelection.collapsed(offset: value.length);
  }

  int _getLotDecimals(double number) {
    final str = EquitiFormatter.formatToString(value: number);
    final index = str.indexOf('.');
    if (index == -1) return 0;
    var decimals = str.substring(index + 1).replaceFirst(RegExp(r'0+$'), '');
    return decimals.length;
  }

  @override
  Widget build(BuildContext buildContext) => MultiBlocListener(
    listeners: [
      BlocListener<
        InputOrderSizeBloc,
        TradeComponentState<double, OrderSizeErrorCode>
      >(listener: (context, state) => widget.onOrderSizeChanged(state)),
      BlocListener<
        InputOrderSizeBloc,
        TradeComponentState<double, OrderSizeErrorCode>
      >(
        listenWhen:
            (previous, current) => previous != current && current.isValid(),
        listener: (context, state) => _updateInputValue(state),
      ),
    ],
    child: BlocBuilder<
      InputOrderSizeBloc,
      TradeComponentState<double, OrderSizeErrorCode>
    >(
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        final localization = EquitiLocalization.of(buildContext);

        String? errorText;
        if (!state.isValid() && !(state is TradeComponentLoadingState)) {
          errorText = localization.trader_pleaseSpecifyTheLotSize;
        } else if (!(widget.marginAllocationState?.isValid() ?? false)) {
          errorText =
              widget.marginAllocationState == null
                  ? null
                  : switch (widget.marginAllocationState!) {
                    TradeComponentErrorState() =>
                      localization.trader_marginAllocationNotValid,
                    _ => null,
                  };
        }

        return StepperControlWidget(
          title: localization.trader_lotSize,
          segmentControWidget:
              widget.options.isNotEmpty
                  ? HighlightOptionBoxWidget(
                    selectedIndex: 0,
                    options: widget.options,
                    onSelectionChange:
                        (value, index) =>
                            context.read<InputOrderSizeBloc>().add(
                              InputOrderSizeEvent.updateOrderSize(
                                args: widget.args,
                                orderSize: value,
                              ),
                            ),
                  )
                  : null,
          trailingWidget: DuploTap(
            onTap: () {
              final String description =
                  EquitiLocalization.of(context).trader_lotsizeInfo;
              DuploDialog.showInfoDialog(
                context: context,
                title: localization.trader_lotSize,
                description: description,
              );
            },

            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 0, 8),
              child: Assets.images.help.svg(width: 12, height: 12),
            ),
          ),
          inputWidget: DuploTap(
            onTap: () {
              final traderFlags = diContainer<TraderFlags>();
              final traderflagMarketIsClosed =
                  traderFlags.showClosedMarketBanner();
              final marketClosed = widget.isMarketClosed ?? false;

              if (marketClosed &&
                  traderflagMarketIsClosed &&
                  widget.orderType == OrderType.marketOrder) {
                toast.hidesToastMessage();
                toast.showToastMessage(
                  context: context,
                  widget: DuploToastMessage(
                    titleMessage: localization.trader_marketIsClosed,
                    descriptionMessage:
                        localization.trader_openTrade_marketIsClosedDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                  ),
                );
              } else if (widget.tradeType == null) {
                toast.hidesToastMessage();
                toast.showToastMessage(
                  context: context,
                  widget: DuploToastMessage(
                    titleMessage: localization.trader_selectBuySell,
                    descriptionMessage: localization.trader_selectDirection,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () {
                      toast.hidesToastMessage();
                    },
                  ),
                );
              }
            },
            child: StepperNumberInputWidget(
              controller: _inputController,
              prescisionFactor: _getLotDecimals(widget.args.minLot),
              minimumValue: widget.args.minLot,
              hintText: localization.trader_enterAnumber,
              errorText: errorText,
              enabled: !widget.args.isDisabled,
              textInputFormatter: DecimalTextInputFormatter(
                decimalRange: _getLotDecimals(widget.args.minLot),
              ),
              onValueChange:
                  (value) => context.read<InputOrderSizeBloc>().add(
                    InputOrderSizeEvent.updateOrderSize(
                      args: widget.args,
                      orderSize: value,
                    ),
                  ),
              changeFactor: widget.args.lotsSteps,
              semanticsIdentifier: widget.semanticsIdentifier,
            ),
          ),
          footerWidget:
              widget.footer ??
              OrderLimitFooterWidget(
                firstPair: KeyValuePair(
                  label: localization.trader_minSize,
                  value: EquitiFormatter.formatNumber(
                    value: widget.args.minLot,
                    locale: Localizations.localeOf(context).toString(),
                  ),
                ),
                secondPair: KeyValuePair(
                  label: localization.trader_maxSize,
                  value: EquitiFormatter.formatNumber(
                    value: widget.args.maxLot,
                    locale: Localizations.localeOf(context).toString(),
                  ),
                ),
                semanticsIdentifier: widget.semanticsIdentifier,
              ),
          bordered: widget.showBorder,
        );
      },
    ),
  );
}
