import 'dart:async';

import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/exceptions/positions_and_orders_exception.dart';
import 'package:e_trader/src/domain/usecase/close_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trade_size_from_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'trade_options_bloc.freezed.dart';
part 'trade_options_event.dart';
part 'trade_options_state.dart';

class TradeOptionsBloc extends Bloc<TradeOptionsEvent, TradeOptionsState>
    with DisposableMixin {
  final PositionModel _position;
  final SubscribeToPositionsUseCase _subscribeToPositionsUseCase;
  final UpdatePositionsUseCase _updatePositionsUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final GetTradeSizeFromVolumeUseCase _getTradeSizeFromVolumeUseCase;
  final CloseTradeUseCase _closeTradeUseCase;
  final TradingAnalytics _tradingAnalyticsEvent;
  TradeOptionsBloc(
    this._position,
    this._subscribeToPositionsUseCase,
    this._updatePositionsUseCase,
    this._getAccountNumberUseCase,
    this._getTradeSizeFromVolumeUseCase,
    this._closeTradeUseCase,
    this._tradingAnalyticsEvent,
  ) : super(const TradeOptionsState()) {
    on<_ConnectToPositionSocket>(
      (event, emit) => _connectToPositionSocket(emit, event),
    );
    on<_QuickCloseTrade>((event, emit) => _quickCloseTrade(emit));
    on<_UpdatePositions>((event, emit) => _onUpdatePositions(event, emit));
  }

  Future<void> _connectToPositionSocket(
    Emitter<TradeOptionsState> emit,
    _ConnectToPositionSocket event,
  ) async {
    emit(
      state.copyWith(processState: const TradeOptionsProcessState.loading()),
    );
    final interactionId = await _tradingAnalyticsEvent.startInteraction(
      TradeAnalyticsEvent.loadPortfolioTradeDetails.eventName,
    );
    await _tradingAnalyticsEvent.loadPortfolioTradeDetails(interactionId);

    final result =
        await _subscribeToPositionsUseCase(
          positionId: int.parse(_position.positionId),
          subscriberId: '${TradeOptionsBloc}_$hashCode',
          eventType: TradingSocketEvent.positions.register,
          symbolName: event.symbolName,
        ).run();

    await result.fold(
      (error) async {
        addError(error);
        _tradingAnalyticsEvent.failedLoadingTradeDetails(
          error.toString(),
          interactionId,
        );
        emit(
          state.copyWith(processState: const TradeOptionsProcessState.error()),
        );
      },
      (positionResponseStream) async {
        _tradingAnalyticsEvent.doneLoadingTradeDetails(
          event.symbolName,
          _position.positionId,
          _position.openPrice.toString(),
          _position.takeProfit.toString(),
          _position.stopLoss.toString(),
          interactionId,
        );

        // Emit connected state after successful register
        emit(
          state.copyWith(
            processState: const TradeOptionsProcessState.connected(),
          ),
        );

        await emit.forEach(
          positionResponseStream,
          onData: (positionResponse) {
            if (positionResponse == null) {
              return state;
            }
            return state.copyWith(
              processState: const TradeOptionsProcessState.success(),
              position: positionResponse.position,
              lotSize: _getTradeSizeFromVolumeUseCase(
                positionResponse.position.volume,
              ),
              lastUpdateTimestamp: DateTime.now().microsecondsSinceEpoch,
            );
          },
          onError: (error, stackTrace) {
            addError(error, stackTrace);
            _tradingAnalyticsEvent.failedLoadingTradeDetails(
              error.toString(),
              interactionId,
            );
            return state.copyWith(
              processState: const TradeOptionsProcessState.error(),
            );
          },
        );
      },
    );
    _tradingAnalyticsEvent.endInteraction(interactionId);
  }

  FutureOr<void> _onUpdatePositions(
    _UpdatePositions event,
    Emitter<TradeOptionsState> _,
  ) {
    _updatePositionsUseCase(
      positionId: int.parse(_position.positionId),
      eventType: event.eventType,
      symbolName: event.symbol,
    );
  }

  Future<void> _quickCloseTrade(Emitter<TradeOptionsState> emit) async {
    emit(
      state.copyWith(
        quickCloseProcessState: const QuickCloseProcessState.loading(),
      ),
    );
    final interactionId = await _tradingAnalyticsEvent.startInteraction(
      TradeAnalyticsEvent.startCloseTrade.eventName,
    );

    final accountNumberEither = _getAccountNumberUseCase();
    final accountNumber = accountNumberEither.fold(
      (exception) => throw exception,
      (account) => account,
    );

    final request = CloseTradeRequestModel(
      accountNumber: accountNumber,
      positions: [
        ClosePositionItemModel(
          id: state.position!.positionId,
          volume: state.position!.volume,
        ),
      ],
    );

    _tradingAnalyticsEvent.startCloseTrade(
      request,
      CloseTradeType.quickClose,
      state.position?.platformName ?? "",
      interactionId,
    );

    final result = await _closeTradeUseCase(request).run();
    result.fold(
      (exception) async {
        final errorMessage = exception.toString();
        if (!emit.isDone) {
          if (exception is PositionsAndOrdersException &&
              exception.errors.containsKey("IsMarketOpen")) {
            final isMarketOpenError =
                exception.errors["IsMarketOpen"]?.firstOrNull;
            if (isMarketOpenError?.toLowerCase() == "false") {
              emit(
                state.copyWith(
                  quickCloseProcessState: QuickCloseProcessState.marketClosed(),
                ),
              );
              return;
            }
          } else
            emit(
              state.copyWith(
                quickCloseProcessState: QuickCloseProcessState.error(
                  state.errorCounter + 1,
                ),
                errorCounter: state.errorCounter + 1,
              ),
            );
        }

        _tradingAnalyticsEvent.closeTradeResult(
          null,
          CloseTradeType.quickClose.name,
          state.position?.platformName ?? "",
          interactionId,
          errorMessage,
        );
        addError(exception);
      },
      (success) async {
        if (!emit.isDone) {
          if (success?.success ?? false) {
            _tradingAnalyticsEvent.closeTradeResult(
              success,
              CloseTradeType.quickClose.name,
              state.position?.platformName ?? "",
              interactionId,
              null,
            );
            emit(
              state.copyWith(
                quickCloseProcessState: QuickCloseProcessState.success(),
                errorCounter: 0,
              ),
            );
          } else {
            _tradingAnalyticsEvent.closeTradeResult(
              success,
              CloseTradeType.quickClose.name,
              state.position?.platformName ?? "",
              interactionId,
              null,
            );
            emit(
              state.copyWith(
                quickCloseProcessState: QuickCloseProcessState.error(
                  state.errorCounter + 1,
                ),
                errorCounter: state.errorCounter + 1,
              ),
            );
          }
        }
      },
    );
    _tradingAnalyticsEvent.endInteraction(interactionId);
  }
}
