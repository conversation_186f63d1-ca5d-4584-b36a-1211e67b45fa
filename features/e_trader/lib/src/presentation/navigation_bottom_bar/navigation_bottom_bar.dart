import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:domain/domain.dart';

import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/di/trading_environment_dependencies.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/model/trade_confirmation_result.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/account_balance/bloc/account_balance_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/bloc/navigation_bottom_bar_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/enums/navigation_tab.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/reset_balance_button.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_registry.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/account_header_widget.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/widgets/sheet_content_widget.dart';
part 'widgets/account_summary_content_widget.dart';

class NavigationBottomBar extends StatefulWidget {
  const NavigationBottomBar({super.key});

  @override
  State<NavigationBottomBar> createState() => _NavigationBottomBarState();
}

class _NavigationBottomBarState extends State<NavigationBottomBar> {
  late Future<void> _dependencyRegistrationFuture;

  @override
  void initState() {
    super.initState();

    // Determine if current account is demo and register dependencies
    final selectedAccount = diContainer<GetSelectedAccountUseCase>().call();
    final isDemo = selectedAccount?.isDemo ?? false;

    _dependencyRegistrationFuture = TradingEnvironmentDependencies.register(
      isDemo: isDemo,
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _dependencyRegistrationFuture,
      builder: (builderContext, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text(
                'Error initializing: ${snapshot.error ?? 'Unknown error'}',
              ),
            ),
          );
        }

        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) {
                return diContainer<NavigationBottomBarBloc>()
                  ..add(NavigationBottomBarEvent.fetchTradingAccounts());
              },
            ),
            BlocProvider(create: (_) => diContainer<AccountBalanceBloc>()),
          ],
          child: BlocBuilder<NavigationBottomBarBloc, NavigationBottomBarState>(
            buildWhen: (previous, current) => previous != current,
            builder: (_, state) {
              if (state.hasOpenPositions == HasOpenPositions.checking) {
                return LoadingView();
              }
              return _NavigationBarContent(
                initialTab:
                    state.hasOpenPositions == HasOpenPositions.yes
                        ? NavigationTab.portfolio
                        : NavigationTab.markets,
              );
            },
          ),
        );
      },
    );
  }
}

class _NavigationBarContent extends StatefulWidget {
  const _NavigationBarContent({required this.initialTab});

  final NavigationTab initialTab;

  @override
  State<_NavigationBarContent> createState() => _NavigationBarContentState();
}

class _NavigationBarContentState extends State<_NavigationBarContent>
    with RouteAwareAppLifecycleMixin {
  late final ValueNotifier<int> _navController;
  late final ValueNotifier<int> _portfolioTabIndex;
  int accountSettingsTab = 0;

  late final SheetController _draggableController;
  NavigationTab _prevTab = NavigationTab.markets;

  // Cache for layout calculations
  _LayoutDimensions? _cachedDimensions;
  Size? _lastConstraintsSize;

  /// Helper to get current tab as enum
  NavigationTab get _currentTab =>
      NavigationTab.values.elementAtOrNull(_navController.value) ??
      NavigationTab.markets;

  /// Helper to set current tab from enum
  set _currentTab(NavigationTab tab) => _navController.value = tab.index;

  @override
  void initState() {
    super.initState();
    _draggableController = SheetController();
    _navController = ValueNotifier<int>(widget.initialTab.index);
    _portfolioTabIndex = ValueNotifier<int>(0);
    _prevTab = _currentTab;
    _navController.addListener(_onTabIndexChanged);
  }

  void _onTabIndexChanged() {
    final nextTab = _currentTab;
    final prevTab = _prevTab;
    if (nextTab == prevTab) return;

    context.read<NavigationBottomBarBloc>().add(
      NavigationBottomBarEvent.changeTab(tab: nextTab),
    );

    // Pause leaving tab
    if (prevTab == NavigationTab.markets) {
      TabSubscriptionRegistry.pauseSymbolsTab();
    }
    if (prevTab == NavigationTab.portfolio) {
      TabSubscriptionRegistry.pausePortfolioTab();
    }

    // Resume entering tab
    if (nextTab == NavigationTab.markets) {
      TabSubscriptionRegistry.resumeSymbolsTab();
    }
    if (nextTab == NavigationTab.portfolio) {
      TabSubscriptionRegistry.resumePortfolioTab();
    }

    _prevTab = nextTab;
  }

  @override
  void dispose() {
    _navController.removeListener(_onTabIndexChanged);
    _navController.dispose();
    _portfolioTabIndex.dispose();
    _draggableController.dispose();
    super.dispose();
  }

  /// Handles bottom navigation tab changes
  void _handleBottomNavTabChange(int index) {
    _navController.value = index;
  }

  /// Gets or calculates layout dimensions with caching
  _LayoutDimensions _getLayoutDimensions({
    required BuildContext context,
    required BoxConstraints constraints,
    required NavigationBottomBarState state,
  }) {
    final currentSize = Size(constraints.maxWidth, constraints.maxHeight);

    // Return cached dimensions if constraints haven't changed
    if (_cachedDimensions != null && _lastConstraintsSize == currentSize) {
      return _cachedDimensions!;
    }

    // Calculate new dimensions
    final dimensions = _LayoutDimensions.calculate(
      context: context,
      constraints: constraints,
      isDemo: state.tradingAccountModel.isDemo,
      tabIndex: state.tab.index,
      marginLevel: state.tradingAccountModel.marginLevel ?? 0,
    );

    // Cache the result
    _cachedDimensions = dimensions;
    _lastConstraintsSize = currentSize;

    return dimensions;
  }

  Widget buildIcon(trader.SvgGenImage iconAsset, Color color) {
    return iconAsset.svg(colorFilter: ColorFilter.mode(color, BlendMode.srcIn));
  }

  /// Builds the list of bottom navigation items
  List<DubloBottomNavBarItems> _buildNavItems(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final selectedColor = theme.foreground.fgWhite;
    final unselectedColor = theme.foreground.fgDisabled;

    return NavigationTab.values.map((tab) {
      final String title;
      final trader.SvgGenImage icon;

      switch (tab) {
        case NavigationTab.discover:
          title = localization.trader_discover;
          icon = trader.Assets.images.discoverIcon;
        case NavigationTab.markets:
          title = localization.trader_markets;
          icon = trader.Assets.images.marketsIcon;
        case NavigationTab.portfolio:
          title = localization.trader_portfolio;
          icon = trader.Assets.images.portfolioIcon;
        case NavigationTab.performance:
          title = localization.trader_performance;
          icon = trader.Assets.images.performanceIcon;
        case NavigationTab.account:
          title = localization.trader_account;
          icon = trader.Assets.images.account;
      }

      return DubloBottomNavBarItems(
        title: title,
        selectedIcon: buildIcon(icon, selectedColor),
        unselectedIcon: buildIcon(icon, unselectedColor),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return BlocBuilder<NavigationBottomBarBloc, NavigationBottomBarState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return Scaffold(
          backgroundColor: theme.background.bgPrimary,
          bottomNavigationBar: DuploBottomNavbar(
            onNavBarCallBack: _handleBottomNavTabChange,
            controller: _navController,
            navItems: _buildNavItems(context),
          ),
          body: SafeArea(
            bottom: false,
            child: LayoutBuilder(
              builder: (layoutBuilderContext, constraints) {
                final dimensions = _getLayoutDimensions(
                  context: layoutBuilderContext,
                  constraints: constraints,
                  state: state,
                );
                return ColoredBox(
                  color: theme.background.bgPrimary,
                  child: RouteVisibilityInheritedWidget(
                    isRouteVisible: isRouteVisible,
                    currentAppState: currentAppState,
                    child: Stack(
                      children: [
                        Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          child: AccountHeaderWidget(
                            sheetController: _draggableController,
                          ),
                        ),
                        _MarginProgressBarWidget(
                          topPosition: 8 + dimensions.accountHeaderHeight,
                        ),
                        _AccountSummaryWidget(
                          topPosition:
                              8 +
                              dimensions.accountHeaderHeight +
                              dimensions.marginProgressBarHeight,
                          height: dimensions.accountSummaryContentHeight,
                        ),
                        if (!state.tradingAccountModel.isDemo)
                          _FundingButtonsWidget(
                            topPosition:
                                dimensions.accountSummaryContentHeight + 16,
                            height: dimensions.actionsContentHeight,
                            blocContext: builderContext,
                          ),
                        if (state.tab == NavigationTab.account &&
                            state.tradingAccountModel.isDemo)
                          _ResetBalanceButtonWidget(
                            topPosition:
                                dimensions.accountSummaryContentHeight + 16,
                            height: dimensions.actionsContentHeight,
                            accountNumber:
                                state.tradingAccountModel.accountNumber,
                            homeCurrency:
                                state.tradingAccountModel.homeCurrency,
                          ),
                        SheetContentWidget(
                          topPosition: dimensions.accountHeaderHeight,
                          sheetController: _draggableController,
                          initialChildSize: dimensions.initialChildSize,
                          maxChildSize: dimensions.maxChildSize,
                          navController: _navController,
                          portfolioTabIndex: _portfolioTabIndex,
                          accountSettingsTab: accountSettingsTab,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    _handleTradeConfirmationNavigation(route);
  }

  void _handleTradeConfirmationNavigation(Route<Object?> route) {
    if (route.settings.name == 'confirmation_sheet') {
      switch (diContainer<EquitiNavigatorBase>()
          .globalData[EquitiTraderRouteSchema.navBarRoute.url]) {
        case TradeConfirmationResult.viewTrades:
          _portfolioTabIndex.value = 1;
          _currentTab = NavigationTab.portfolio;
          break;

        case TradeConfirmationResult.viewOrders:
          _portfolioTabIndex.value = 2;
          _currentTab = NavigationTab.portfolio;
          break;

        default:
          _currentTab = NavigationTab.markets;
      }
      diContainer<EquitiNavigatorBase>().globalData.remove(
        EquitiTraderRouteSchema.navBarRoute.url,
      );
    }
  }
}

/// Widget for the margin progress bar positioned element
class _MarginProgressBarWidget extends StatelessWidget {
  final double topPosition;

  const _MarginProgressBarWidget({required this.topPosition});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: topPosition,
      left: 0,
      right: 0,
      child: BlocSelector<
        AccountBalanceBloc,
        AccountBalanceState,
        TradingAccountModel
      >(
        selector: (accountState) => accountState.tradingAccountModel,
        builder: (_, tradingAccountModel) {
          return DuploMarginProgressBar(
            height: 4,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            marginLevel: tradingAccountModel.marginLevel ?? 0,
            balance: tradingAccountModel.equity,
          );
        },
      ),
    );
  }
}

/// Widget for the account summary content positioned element
class _AccountSummaryWidget extends StatelessWidget {
  final double topPosition;
  final double height;

  const _AccountSummaryWidget({
    required this.topPosition,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: topPosition,
      right: 0,
      left: 0,
      child: SizedBox(
        height: height,
        child: const Padding(
          padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 40),
          child: _AccountSummaryContentWidget(),
        ),
      ),
    );
  }
}

/// Widget for the funding buttons positioned element
class _FundingButtonsWidget extends StatelessWidget {
  final double topPosition;
  final double height;
  final BuildContext blocContext;

  const _FundingButtonsWidget({
    required this.topPosition,
    required this.height,
    required this.blocContext,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: topPosition,
      left: 0,
      right: 0,
      child: SizedBox(
        height: height,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: DuploFundingButtons(
            onDepositPressed: () {
              blocContext.read<NavigationBottomBarBloc>().add(
                const NavigationBottomBarEvent.navigateToDepositPaymentOptions(),
              );
            },
            onTransferPressed: () {
              blocContext.read<NavigationBottomBarBloc>().add(
                const NavigationBottomBarEvent.navigateToTransferFunds(),
              );
            },
            onWithdrawPressed: () {
              blocContext.read<NavigationBottomBarBloc>().add(
                const NavigationBottomBarEvent.navigateToWithdrawPaymentOptions(),
              );
            },
          ),
        ),
      ),
    );
  }
}

/// Widget for the reset balance button positioned element
class _ResetBalanceButtonWidget extends StatelessWidget {
  final double topPosition;
  final double height;
  final String accountNumber;
  final String homeCurrency;

  const _ResetBalanceButtonWidget({
    required this.topPosition,
    required this.height,
    required this.accountNumber,
    required this.homeCurrency,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: topPosition,
      left: 0,
      right: 0,
      child: SizedBox(
        height: height,
        child: ResetBalanceButton(
          accountNumber: accountNumber,
          homeCurrency: homeCurrency,
        ),
      ),
    );
  }
}

/// Holds pre-calculated layout dimensions
class _LayoutDimensions {
  final double accountHeaderHeight;
  final double marginProgressBarHeight;
  final double accountSummaryContentHeight;
  final double actionsContentHeight;
  final double maxChildSize;
  final double initialChildSize;

  const _LayoutDimensions({
    required this.accountHeaderHeight,
    required this.marginProgressBarHeight,
    required this.accountSummaryContentHeight,
    required this.actionsContentHeight,
    required this.maxChildSize,
    required this.initialChildSize,
  });

  /// Calculates layout dimensions based on context and state
  factory _LayoutDimensions.calculate({
    required BuildContext context,
    required BoxConstraints constraints,
    required bool isDemo,
    required int tabIndex,
    required double marginLevel,
  }) {
    final viewPadding = MediaQuery.viewPaddingOf(context);
    final textStyles = context.duploTextStyles;

    // Calculate account header height
    final accountHeaderHeight = kToolbarHeight + viewPadding.top;

    // Calculate margin progress bar height
    final marginProgressBar = DuploMarginProgressBar(
      height: 4,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      marginLevel: marginLevel,
    );
    final marginProgressBarHeight = marginProgressBar.getHeight();

    // Measure text heights for account summary
    final textXsHeight =
        measureText(
          context: context,
          text: '',
          textStyle: textStyles.textXs.toTextStyle(),
        ).height;

    final displaySmHeight =
        measureText(
          context: context,
          text: '',
          textStyle: textStyles.displaySm.toTextStyle(),
        ).height;

    final textSmHeight =
        measureText(
          context: context,
          text: '',
          textStyle: textStyles.textSm.toTextStyle(),
        ).height;

    // Calculate account summary content height
    final accountSummaryContentHeight =
        accountHeaderHeight +
        marginProgressBarHeight +
        8 +
        textXsHeight +
        displaySmHeight +
        textSmHeight;

    // Calculate actions content height
    final actionsContentHeight = 50.0 + textXsHeight;

    // Calculate max child size for sheet
    final maxChildSize = ((constraints.maxHeight -
                accountHeaderHeight -
                8 -
                marginProgressBarHeight) /
            (constraints.maxHeight - accountHeaderHeight))
        .clamp(0.0, 1.0);

    // Calculate total content above sheet
    final totalContentAboveSheet =
        isDemo && tabIndex != 4
            ? accountSummaryContentHeight
            : accountSummaryContentHeight + actionsContentHeight - 16;

    // Calculate initial child size for sheet
    final initialChildSize = ((constraints.maxHeight - totalContentAboveSheet) /
            constraints.maxHeight)
        .clamp(0.2, 0.9);

    return _LayoutDimensions(
      accountHeaderHeight: accountHeaderHeight,
      marginProgressBarHeight: marginProgressBarHeight,
      accountSummaryContentHeight: accountSummaryContentHeight,
      actionsContentHeight: actionsContentHeight,
      maxChildSize: maxChildSize,
      initialChildSize: initialChildSize,
    );
  }
}
