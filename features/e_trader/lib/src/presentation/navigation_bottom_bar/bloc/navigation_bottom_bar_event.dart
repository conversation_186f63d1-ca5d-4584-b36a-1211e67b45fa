part of 'navigation_bottom_bar_bloc.dart';

@freezed
sealed class NavigationBottomBarEvent with _$NavigationBottomBarEvent {
  const factory NavigationBottomBarEvent.navigateToDepositPaymentOptions() =
      _NavigateToDepositPaymentOptions;
  const factory NavigationBottomBarEvent.navigateToWithdrawPaymentOptions() =
      _NavigateToWithdrawPaymentOptions;
  const factory NavigationBottomBarEvent.navigateToTransferFunds() =
      _NavigateToTransferFunds;
  const factory NavigationBottomBarEvent.changeTab({
    required NavigationTab tab,
  }) = _ChangeTab;
  const factory NavigationBottomBarEvent.fetchTradingAccounts() =
      _FetchTradingAccounts;
}
