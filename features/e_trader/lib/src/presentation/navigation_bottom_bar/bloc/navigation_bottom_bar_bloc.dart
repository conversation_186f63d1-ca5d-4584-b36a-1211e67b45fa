import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_accounts_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/enums/navigation_tab.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';

part 'navigation_bottom_bar_bloc.freezed.dart';
part 'navigation_bottom_bar_event.dart';
part 'navigation_bottom_bar_state.dart';

class NavigationBottomBarBloc
    extends Bloc<NavigationBottomBarEvent, NavigationBottomBarState>
    with DisposableMixin {
  final EquitiTraderNavigation _equitiTraderNavigation;
  final LoggerBase _logger;
  final GetTradingAccountsUseCase _getTradingAccountsUseCase;

  NavigationBottomBarBloc(
    GetSelectedAccountUseCase _getSelectedAccountUseCase,
    this._logger,
    this._equitiTraderNavigation,
    this._getTradingAccountsUseCase,
  ) : super(
        _NavigationBottomBarState(
          tradingAccountModel: _getSelectedAccountUseCase()!,
          tab: NavigationTab.markets,
        ),
      ) {
    on<_NavigateToDepositPaymentOptions>(_navigateToPaymentOptions);
    on<_NavigateToWithdrawPaymentOptions>(_navigateToWithdrawPaymentOptions);
    on<_NavigateToTransferFunds>(_navigateToTransferFunds);
    on<_ChangeTab>(_changeTab);
    on<_FetchTradingAccounts>(_fetchTradingAccounts);
  }

  @override
  addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }

  FutureOr<void> _fetchTradingAccounts(
    _FetchTradingAccounts event,
    Emitter<NavigationBottomBarState> emit,
  ) async {
    emit(state.copyWith(hasOpenPositions: HasOpenPositions.checking));
    final tradingAccounts = await _getTradingAccountsUseCase().run();
    tradingAccounts.fold(
      (error) {
        emit(state.copyWith(hasOpenPositions: HasOpenPositions.no));
      },
      (accounts) {
        final selectedAccount = accounts.firstOrNullWhere(
          (TradingAccountModel acc) =>
              acc.recordId == state.tradingAccountModel.recordId,
        );
        final hasOpenPositions = selectedAccount?.hasOpenPositions ?? false;
        emit(
          state.copyWith(
            hasOpenPositions:
                hasOpenPositions ? HasOpenPositions.yes : HasOpenPositions.no,
          ),
        );
      },
    );
  }

  FutureOr<void> _changeTab(
    _ChangeTab event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    emit(state.copyWith(tab: event.tab));
  }

  FutureOr<void> _navigateToPaymentOptions(
    _NavigateToDepositPaymentOptions event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    log("Navigate to deposit payment options", name: "NavigationBottomBarBloc");
    _equitiTraderNavigation.navigateToDepositOptions(
      depositFlowConfig: DepositFlowConfig(
        origin: EquitiTraderRouteSchema.navBarRoute.url,
        depositType: DepositType.additional,
      ),
    );
  }

  FutureOr<void> _navigateToWithdrawPaymentOptions(
    _NavigateToWithdrawPaymentOptions event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    _equitiTraderNavigation.navigateToWithdrawOptions(
      EquitiTraderRouteSchema.navBarRoute.url,
    );
  }

  FutureOr<void> _navigateToTransferFunds(
    _NavigateToTransferFunds event,
    Emitter<NavigationBottomBarState> emit,
  ) {
    _equitiTraderNavigation.goToTransferFundsScreen(
      EquitiTraderRouteSchema.navBarRoute.url,
    );
  }
}
