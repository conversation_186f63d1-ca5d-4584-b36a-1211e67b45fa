// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'navigation_bottom_bar_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$NavigationBottomBarEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigationBottomBarEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'NavigationBottomBarEvent()';
}


}

/// @nodoc
class $NavigationBottomBarEventCopyWith<$Res>  {
$NavigationBottomBarEventCopyWith(NavigationBottomBarEvent _, $Res Function(NavigationBottomBarEvent) __);
}


/// @nodoc


class _NavigateToDepositPaymentOptions implements NavigationBottomBarEvent {
  const _NavigateToDepositPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToDepositPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'NavigationBottomBarEvent.navigateToDepositPaymentOptions()';
}


}




/// @nodoc


class _NavigateToWithdrawPaymentOptions implements NavigationBottomBarEvent {
  const _NavigateToWithdrawPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToWithdrawPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'NavigationBottomBarEvent.navigateToWithdrawPaymentOptions()';
}


}




/// @nodoc


class _NavigateToTransferFunds implements NavigationBottomBarEvent {
  const _NavigateToTransferFunds();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToTransferFunds);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'NavigationBottomBarEvent.navigateToTransferFunds()';
}


}




/// @nodoc


class _ChangeTab implements NavigationBottomBarEvent {
  const _ChangeTab({required this.tab});
  

 final  NavigationTab tab;

/// Create a copy of NavigationBottomBarEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeTabCopyWith<_ChangeTab> get copyWith => __$ChangeTabCopyWithImpl<_ChangeTab>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeTab&&(identical(other.tab, tab) || other.tab == tab));
}


@override
int get hashCode => Object.hash(runtimeType,tab);

@override
String toString() {
  return 'NavigationBottomBarEvent.changeTab(tab: $tab)';
}


}

/// @nodoc
abstract mixin class _$ChangeTabCopyWith<$Res> implements $NavigationBottomBarEventCopyWith<$Res> {
  factory _$ChangeTabCopyWith(_ChangeTab value, $Res Function(_ChangeTab) _then) = __$ChangeTabCopyWithImpl;
@useResult
$Res call({
 NavigationTab tab
});




}
/// @nodoc
class __$ChangeTabCopyWithImpl<$Res>
    implements _$ChangeTabCopyWith<$Res> {
  __$ChangeTabCopyWithImpl(this._self, this._then);

  final _ChangeTab _self;
  final $Res Function(_ChangeTab) _then;

/// Create a copy of NavigationBottomBarEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tab = null,}) {
  return _then(_ChangeTab(
tab: null == tab ? _self.tab : tab // ignore: cast_nullable_to_non_nullable
as NavigationTab,
  ));
}


}

/// @nodoc


class _FetchTradingAccounts implements NavigationBottomBarEvent {
  const _FetchTradingAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FetchTradingAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'NavigationBottomBarEvent.fetchTradingAccounts()';
}


}




/// @nodoc
mixin _$NavigationBottomBarState {

 TradingAccountModel get tradingAccountModel; NavigationTab get tab; HasOpenPositions get hasOpenPositions;
/// Create a copy of NavigationBottomBarState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NavigationBottomBarStateCopyWith<NavigationBottomBarState> get copyWith => _$NavigationBottomBarStateCopyWithImpl<NavigationBottomBarState>(this as NavigationBottomBarState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigationBottomBarState&&(identical(other.tradingAccountModel, tradingAccountModel) || other.tradingAccountModel == tradingAccountModel)&&(identical(other.tab, tab) || other.tab == tab)&&(identical(other.hasOpenPositions, hasOpenPositions) || other.hasOpenPositions == hasOpenPositions));
}


@override
int get hashCode => Object.hash(runtimeType,tradingAccountModel,tab,hasOpenPositions);

@override
String toString() {
  return 'NavigationBottomBarState(tradingAccountModel: $tradingAccountModel, tab: $tab, hasOpenPositions: $hasOpenPositions)';
}


}

/// @nodoc
abstract mixin class $NavigationBottomBarStateCopyWith<$Res>  {
  factory $NavigationBottomBarStateCopyWith(NavigationBottomBarState value, $Res Function(NavigationBottomBarState) _then) = _$NavigationBottomBarStateCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel tradingAccountModel, NavigationTab tab, HasOpenPositions hasOpenPositions
});


$TradingAccountModelCopyWith<$Res> get tradingAccountModel;

}
/// @nodoc
class _$NavigationBottomBarStateCopyWithImpl<$Res>
    implements $NavigationBottomBarStateCopyWith<$Res> {
  _$NavigationBottomBarStateCopyWithImpl(this._self, this._then);

  final NavigationBottomBarState _self;
  final $Res Function(NavigationBottomBarState) _then;

/// Create a copy of NavigationBottomBarState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tradingAccountModel = null,Object? tab = null,Object? hasOpenPositions = null,}) {
  return _then(_self.copyWith(
tradingAccountModel: null == tradingAccountModel ? _self.tradingAccountModel : tradingAccountModel // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,tab: null == tab ? _self.tab : tab // ignore: cast_nullable_to_non_nullable
as NavigationTab,hasOpenPositions: null == hasOpenPositions ? _self.hasOpenPositions : hasOpenPositions // ignore: cast_nullable_to_non_nullable
as HasOpenPositions,
  ));
}
/// Create a copy of NavigationBottomBarState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get tradingAccountModel {
  
  return $TradingAccountModelCopyWith<$Res>(_self.tradingAccountModel, (value) {
    return _then(_self.copyWith(tradingAccountModel: value));
  });
}
}


/// @nodoc


class _NavigationBottomBarState implements NavigationBottomBarState {
  const _NavigationBottomBarState({required this.tradingAccountModel, required this.tab, this.hasOpenPositions = HasOpenPositions.checking});
  

@override final  TradingAccountModel tradingAccountModel;
@override final  NavigationTab tab;
@override@JsonKey() final  HasOpenPositions hasOpenPositions;

/// Create a copy of NavigationBottomBarState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NavigationBottomBarStateCopyWith<_NavigationBottomBarState> get copyWith => __$NavigationBottomBarStateCopyWithImpl<_NavigationBottomBarState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigationBottomBarState&&(identical(other.tradingAccountModel, tradingAccountModel) || other.tradingAccountModel == tradingAccountModel)&&(identical(other.tab, tab) || other.tab == tab)&&(identical(other.hasOpenPositions, hasOpenPositions) || other.hasOpenPositions == hasOpenPositions));
}


@override
int get hashCode => Object.hash(runtimeType,tradingAccountModel,tab,hasOpenPositions);

@override
String toString() {
  return 'NavigationBottomBarState(tradingAccountModel: $tradingAccountModel, tab: $tab, hasOpenPositions: $hasOpenPositions)';
}


}

/// @nodoc
abstract mixin class _$NavigationBottomBarStateCopyWith<$Res> implements $NavigationBottomBarStateCopyWith<$Res> {
  factory _$NavigationBottomBarStateCopyWith(_NavigationBottomBarState value, $Res Function(_NavigationBottomBarState) _then) = __$NavigationBottomBarStateCopyWithImpl;
@override @useResult
$Res call({
 TradingAccountModel tradingAccountModel, NavigationTab tab, HasOpenPositions hasOpenPositions
});


@override $TradingAccountModelCopyWith<$Res> get tradingAccountModel;

}
/// @nodoc
class __$NavigationBottomBarStateCopyWithImpl<$Res>
    implements _$NavigationBottomBarStateCopyWith<$Res> {
  __$NavigationBottomBarStateCopyWithImpl(this._self, this._then);

  final _NavigationBottomBarState _self;
  final $Res Function(_NavigationBottomBarState) _then;

/// Create a copy of NavigationBottomBarState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tradingAccountModel = null,Object? tab = null,Object? hasOpenPositions = null,}) {
  return _then(_NavigationBottomBarState(
tradingAccountModel: null == tradingAccountModel ? _self.tradingAccountModel : tradingAccountModel // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,tab: null == tab ? _self.tab : tab // ignore: cast_nullable_to_non_nullable
as NavigationTab,hasOpenPositions: null == hasOpenPositions ? _self.hasOpenPositions : hasOpenPositions // ignore: cast_nullable_to_non_nullable
as HasOpenPositions,
  ));
}

/// Create a copy of NavigationBottomBarState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get tradingAccountModel {
  
  return $TradingAccountModelCopyWith<$Res>(_self.tradingAccountModel, (value) {
    return _then(_self.copyWith(tradingAccountModel: value));
  });
}
}

// dart format on
