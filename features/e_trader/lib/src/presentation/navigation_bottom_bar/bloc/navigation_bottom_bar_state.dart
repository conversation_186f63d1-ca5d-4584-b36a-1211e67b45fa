part of 'navigation_bottom_bar_bloc.dart';

@freezed
sealed class NavigationBottomBarState with _$NavigationBottomBarState {
  const factory NavigationBottomBarState({
    required TradingAccountModel tradingAccountModel,
    required NavigationTab tab,
    @Default(HasOpenPositions.checking) HasOpenPositions hasOpenPositions,
  }) = _NavigationBottomBarState;
}

enum HasOpenPositions { yes, no, checking }
