import 'dart:async';

import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/model/price_alert.dart';
import 'package:e_trader/src/domain/model/set_price_alert_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/delete_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'set_price_alert_event.dart';
part 'set_price_alert_state.dart';
part 'set_price_alert_bloc.freezed.dart';

class SetPriceAlertBloc extends Bloc<SetPriceAlertEvent, SetPriceAlertState> {
  final SavePriceAlertUseCase _savePriceAlertUseCase;
  final ModifyPriceAlertUseCase _modifyPriceAlertUseCase;

  final DeletePriceAlertUseCase _deletePriceAlertUseCase;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;
  final TradingAnalytics _tradingAnalyticsEvent;

  SetPriceAlertBloc(
    this._savePriceAlertUseCase,
    this._subscribeToSymbolQuotesUseCase,
    this._deletePriceAlertUseCase,
    this._modifyPriceAlertUseCase,
    this._tradingAnalyticsEvent,
  ) : super(SetPriceAlertState.loading()) {
    on<_OnSaveAlert>(_onSaveAlert);
    on<_OnEditPriceChanged>(_onEditPriceStateChanged);
    on<_OnFetchSymbolDetails>(_onFetchSymbolDetails);
    on<_TradeTypeChanged>(_onTradeTypeChanged);
    on<_OnDeleteAlert>(_onDeleteAlert);
    on<_OnModifyAlert>(_onModifyAlert);
  }

  void _onEditPriceStateChanged(
    _OnEditPriceChanged event,
    Emitter<SetPriceAlertState> emit,
  ) {
    switch (event.editState) {
      case EditPriceAlertSuccess(info: final model):
        switch (state) {
          case SetPriceAlertSuccess(:final info):
            emit(
              SetPriceAlertState.success(
                info.copyWith(
                  enteredPrice: model.enteredPrice,
                  vaidationError: model.validationErrorMessage != null,
                ),
              ),
            );
          case _:
            break;
        }
      case _:
        break;
    }
  }

  void _onTradeTypeChanged(
    _TradeTypeChanged event,
    Emitter<SetPriceAlertState> emit,
  ) {
    if (state case SetPriceAlertSuccess(:final info)) {
      emit(
        SetPriceAlertState.success(info.copyWith(selctedTradeType: event.type)),
      );
    }
  }

  FutureOr<void> _onSaveAlert(
    _OnSaveAlert event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    if (state case SetPriceAlertSuccess(:final info)) {
      final interactionId = await _tradingAnalyticsEvent.startInteraction(
        TradeAnalyticsEvent.placingAlert.eventName,
      );
      _tradingAnalyticsEvent.placingAlert(
        info.prices.platformName,
        info.enteredPrice.toString(),
        interactionId,
      );
      emit(
        SetPriceAlertState.success(
          info.copyWith(viewState: SetPriceAlertViewState.creating),
        ),
      );
      var saveResponse =
          await _savePriceAlertUseCase(
            symbolCode: info.prices.platformName,
            triggerPrice: info.enteredPrice.toStringAsFixed(info.prices.digits),
            type: info.selctedTradeType,
          ).run();
      await saveResponse.fold(
        (exception) async {
          addError(exception);
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.creationFailed),
            ),
          );
          _tradingAnalyticsEvent.alertPlacedResult(
            false,
            info.prices.platformName,
            info.enteredPrice.toString(),
            interactionId,
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
        (response) async {
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.creationSuccess),
            ),
          );
          await _tradingAnalyticsEvent.alertPlacedResult(
            response,
            info.prices.platformName,
            info.enteredPrice.toString(),
            interactionId,
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
      );
      _tradingAnalyticsEvent.endInteraction(interactionId);
    }
  }

  void _onDeleteAlert(
    _OnDeleteAlert event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    if (state case SetPriceAlertSuccess(:final info)) {
      final interactionId = await _tradingAnalyticsEvent.startInteraction(
        TradeAnalyticsEvent.startDeleteAlert.eventName,
      );
      _tradingAnalyticsEvent.startDeleteAlert(
        info.prices.platformName,
        event.alert.priceAlertId,
        interactionId,
      );
      emit(
        SetPriceAlertState.success(
          info.copyWith(viewState: SetPriceAlertViewState.deleting),
        ),
      );

      var saveResponse =
          await _deletePriceAlertUseCase(
            alertIds: [event.alert.priceAlertId],
          ).run();
      await saveResponse.fold(
        (exception) async {
          addError(exception);
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.deleteFailed),
            ),
          );

          _tradingAnalyticsEvent.deleteAlertResult(
            false,
            info.prices.platformName,
            event.alert.priceAlertId,
            interactionId,
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
        (response) async {
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.deleteSuccess),
            ),
          );

          _tradingAnalyticsEvent.deleteAlertResult(
            response,
            info.prices.platformName,
            event.alert.priceAlertId,
            interactionId,
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
      );
      _tradingAnalyticsEvent.endInteraction(interactionId);
    }
  }

  void _onModifyAlert(
    _OnModifyAlert event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    if (state case SetPriceAlertSuccess(:final info)) {
      emit(
        SetPriceAlertState.success(
          info.copyWith(viewState: SetPriceAlertViewState.modifying),
        ),
      );
      final interactionId = await _tradingAnalyticsEvent.startInteraction(
        TradeAnalyticsEvent.startModifyAlert.eventName,
      );
      await _tradingAnalyticsEvent.startModifyAlert(
        info.prices.platformName,
        event.alert.priceAlertId,
        info.enteredPrice.toString(),
        interactionId,
      );
      var saveResponse =
          await _modifyPriceAlertUseCase(
            alertId: event.alert.priceAlertId,
            triggerPrice: info.enteredPrice.toStringAsFixed(info.prices.digits),
            type: info.selctedTradeType,
          ).run();
      await saveResponse.fold(
        (exception) async {
          addError(exception);
          emit(
            SetPriceAlertState.success(
              info.copyWith(
                viewState: SetPriceAlertViewState.modificationFailed,
              ),
            ),
          );
          await _tradingAnalyticsEvent.modifyAlertResult(
            false,
            info.prices.platformName,
            event.alert.priceAlertId,
            info.enteredPrice.toString(),
            interactionId,
          );

          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
        (response) async {
          emit(
            SetPriceAlertState.success(
              info.copyWith(
                viewState: SetPriceAlertViewState.modificationSuccess,
              ),
            ),
          );
          _tradingAnalyticsEvent.modifyAlertResult(
            true,
            info.prices.platformName,
            event.alert.priceAlertId,
            info.enteredPrice.toString(),
            interactionId,
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
      );
      _tradingAnalyticsEvent.endInteraction(interactionId);
    }
  }

  FutureOr<void> _onFetchSymbolDetails(
    _OnFetchSymbolDetails event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    emit(SetPriceAlertState.loading());
    final interactionId = await _tradingAnalyticsEvent.startInteraction(
      TradeAnalyticsEvent.loadPortfolioAlertDetails.eventName,
    );
    await _tradingAnalyticsEvent.loadPortfolioAlertDetails(interactionId);
    final result =
        await _subscribeToSymbolQuotesUseCase(
          symbol: event.symbol,
          subscriberId: '${SetPriceAlertBloc}_$hashCode',
        ).run();
    await result.fold(
      (left) {
        addError(left);
        emit(SetPriceAlertState.error());
      },
      (subscribeResultStream) {
        _tradingAnalyticsEvent.doneLoadingAlertDetails(
          event.symbol,
          event.alert?.priceAlertId ?? "",
          event.alert?.priceAlertPrice.toString() ?? "",
          interactionId,
        );
        return emit.forEach(
          subscribeResultStream,
          onData: (symbolQuoteModel) {
            final newModel = switch (state) {
              SetPriceAlertSuccess(:final info) => () {
                final retModel = SetPriceAlertModel(
                  selctedTradeType: info.selctedTradeType,
                  prices: symbolQuoteModel,
                  enteredPrice: info.enteredPrice,
                  viewState: SetPriceAlertViewState.idle,
                  vaidationError: info.vaidationError,
                  // Add a timestamp to force uniqueness
                  lastUpdated: DateTime.now().millisecondsSinceEpoch,
                );
                return retModel;
              }(),
              _ => () {
                final retModel = SetPriceAlertModel(
                  selctedTradeType:
                      event.alert?.alertTradeType ?? TradeType.buy,
                  prices: symbolQuoteModel,
                  enteredPrice:
                      event.alert?.priceAlertPrice ?? symbolQuoteModel.ask,
                  viewState: SetPriceAlertViewState.idle,
                  vaidationError: false,
                  lastUpdated: DateTime.now().millisecondsSinceEpoch,
                );
                return retModel;
              }(),
            };
            return SetPriceAlertState.success(newModel);
          },
          onError: (error, stackTrace) {
            addError(error, stackTrace);
            _tradingAnalyticsEvent.failedLoadingAlertDetails(
              error.toString() + " stackTrace: " + stackTrace.toString(),
              interactionId,
            );
            return SetPriceAlertState.error();
          },
        );
      },
    );
    _tradingAnalyticsEvent.endInteraction(interactionId);
  }
}
