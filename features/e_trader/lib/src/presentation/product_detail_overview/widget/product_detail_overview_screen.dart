import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/product_detail_info.dart';
import 'package:e_trader/src/presentation/market_hours/widgets/market_hour_details_widget_v2.dart';
import 'package:e_trader/src/presentation/product_detail_overview/bloc/product_detail_overview_bloc.dart';
import 'package:e_trader/src/presentation/product_details/bloc/product_details_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:prelude/prelude.dart';

class ProductDetailOverviewScreen extends StatefulWidget {
  final String platformName;
  final int digit;
  final String accountNumber;

  const ProductDetailOverviewScreen({
    super.key,
    required this.platformName,
    required this.digit,
    required this.accountNumber,
  });

  @override
  State<ProductDetailOverviewScreen> createState() =>
      _ProductDetailOverviewScreenState();
}

extension LocalizationExtension on BuildContext {
  EquitiLocalization get loc => EquitiLocalization.of(this);
}

String formatNumberOrPlaceholder({
  num? value,
  required String locale,
  String placeholder = '--',
}) {
  return (value != null)
      ? EquitiFormatter.formatNumber(value: value, locale: locale)
      : placeholder;
}

String formatDateOrPlaceholder({String? value, String placeholder = '--'}) {
  DateTime parsedDate = DateTime.parse(value ?? '');
  DateFormat formatter = DateFormat("dd/MM/yyyy");
  final retValue = formatter.format(parsedDate);
  if (retValue.isEmpty) return placeholder;
  return retValue;
}

extension NullableStringExtension on String? {
  String orPlaceholder([String placeholder = "--"]) {
    return (this?.isNotEmpty ?? false) ? this! : placeholder;
  }
}

class SectionData {
  final String title;
  final List<KeyValuePair> data;

  const SectionData({required this.title, required this.data});
}

class _ProductDetailOverviewScreenState
    extends State<ProductDetailOverviewScreen>
    with AutomaticKeepAliveClientMixin {
  List<SectionData> generalDataGroup(ProductDetailInfo info, double spread) {
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context).toString();
    return [
      SectionData(
        title: l10n.trader_general,
        data: [
          KeyValuePair(
            label: l10n.trader_baseCurrency,
            value: info.baseCurrency.orPlaceholder(),
          ),
          KeyValuePair(
            label: l10n.trader_contractSize,
            value: formatNumberOrPlaceholder(
              value: info.contractSize,
              locale: locale,
            ),
          ),
          KeyValuePair(
            label: l10n.trader_contractSizeUnit,
            value: info.contractSizeUnit.orPlaceholder(),
          ),
          KeyValuePair(
            label: l10n.trader_spreadValue,
            value: formatNumberOrPlaceholder(value: spread, locale: locale),
          ),
          KeyValuePair(
            label: l10n.trader_marginRate,
            value:
                info.marginRate != null
                    ? EquitiFormatter.decimalPatternDigits(
                          value: info.marginRate!,
                          locale: locale,
                          digits: 2,
                        ) +
                        "%"
                    : "--",
          ),
        ],
      ),
    ];
  }

  List<SectionData> classificationDataGroup(
    ProductDetailInfo info,
    BuildContext context,
  ) {
    final l10n = EquitiLocalization.of(context);
    return [
      SectionData(
        title: l10n.trader_classifications,
        data: [
          KeyValuePair(
            label: l10n.trader_productName,
            value: info.productName.orPlaceholder(),
          ),
          KeyValuePair(
            label: l10n.trader_assetClass,
            value: info.assetClass.orPlaceholder(),
          ),
          KeyValuePair(
            label: l10n.trader_assetType,
            value: info.assetType.orPlaceholder(),
          ),
          KeyValuePair(
            label: l10n.trader_tickerName,
            value: info.tickerName.orPlaceholder(),
          ),
        ],
      ),
    ];
  }

  List<SectionData> sectionDataGroup(
    ProductDetailInfo info,
    bool swapEnabled,
    BuildContext context,
  ) {
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context).toString();
    return [
      SectionData(
        title: l10n.trader_positionLimits,
        data: [
          KeyValuePair(
            label: l10n.trader_minimumLot,
            value: formatNumberOrPlaceholder(
              value: info.minLot,
              locale: locale,
            ),
          ),
          KeyValuePair(
            label: l10n.trader_maximumLot,
            value: formatNumberOrPlaceholder(
              value: info.maxLot,
              locale: locale,
            ),
          ),
          KeyValuePair(
            label: l10n.trader_lotSteps,
            value: formatNumberOrPlaceholder(
              value: info.lotsSteps,
              locale: locale,
            ),
          ),
        ],
      ),
      if (info.commissionDescription case final commission?
          when commission.isNotEmpty)
        SectionData(
          title: l10n.trader_productFinancing,
          data: [
            KeyValuePair(
              label: l10n.trader_commisionChanges,
              value: commission,
            ),
          ],
        ),
      if (info.expiryDate != null && info.expiryDate!.isNotEmpty)
        SectionData(
          title: l10n.trader_productExpiry,
          data: [
            KeyValuePair(
              label: l10n.trader_expiryDate,
              value: formatDateOrPlaceholder(value: info.expiryDate),
            ),
          ],
        ),
      if (swapEnabled)
        SectionData(
          title: l10n.trader_swaps,
          data: [
            KeyValuePair(
              label: l10n.trader_swapsLong,
              value: formatNumberOrPlaceholder(
                value: info.swapLong,
                locale: locale,
              ),
            ),
            KeyValuePair(
              label: l10n.trader_swapsShort,
              value: formatNumberOrPlaceholder(
                value: info.swapShort,
                locale: locale,
              ),
            ),
          ],
        ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final l10n = EquitiLocalization.of(context);

    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return BlocProvider(
      create:
          (_) =>
              diContainer<ProductDetailOverviewBloc>()..add(
                ProductDetailOverviewEvent.fetchSymbolInfo(widget.platformName),
              ),
      child: Container(
        child: BlocBuilder<
          ProductDetailOverviewBloc,
          ProductDetailOverviewState
        >(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            return switch (state) {
              ProductDetailOverviewLoading() => Center(
                child: const CircularProgressIndicator.adaptive(),
              ),
              ProductDetailOverviewError() => Container(
                child: Center(
                  child: EmptyOrErrorStateComponent.defaultError(
                    blocBuilderContext,
                    () {
                      blocBuilderContext.read<ProductDetailOverviewBloc>().add(
                        ProductDetailOverviewEvent.fetchSymbolInfo(
                          widget.platformName,
                        ),
                      );
                    },
                  ),
                ),
              ),
              ProductDetailOverviewSuccess(
                :final info,
                :final showSwapDetails,
              ) =>
                () {
                  final classificationSectionData = classificationDataGroup(
                    info,
                    blocBuilderContext,
                  );

                  final sectionData = sectionDataGroup(
                    info,
                    showSwapDetails,
                    blocBuilderContext,
                  );
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: Container(
                          color: theme.background.bgSecondary,
                          child: SingleChildScrollView(
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  SizedBox(height: 20),
                                  MarketHourDetailsWidgetV2(
                                    platformName: widget.platformName,
                                  ),
                                  SizedBox(height: 24),
                                  DuploText(
                                    text: l10n.trader_productDetails,
                                    style: duploTextStyles.textMd,
                                    textAlign: TextAlign.start,
                                    fontWeight: DuploFontWeight.semiBold,
                                    color: theme.text.textPrimary,
                                  ),
                                  SizedBox(height: 8),
                                  DuploText(
                                    text: info.description ?? "--",
                                    color: theme.text.textSecondary,
                                    style: duploTextStyles.textSm,
                                    textAlign: TextAlign.start,
                                    fontWeight: DuploFontWeight.medium,
                                  ),
                                  SizedBox(height: 8),
                                  Column(
                                    children:
                                        classificationSectionData.map((item) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 24.0,
                                            ),
                                            child: DuploKeyValueDisplay(
                                              title: item.title,
                                              keyValuePairs: item.data,
                                            ),
                                          );
                                        }).toList(),
                                  ),
                                  BlocSelector<
                                    ProductDetailsBloc,
                                    ProductDetailsState,
                                    double?
                                  >(
                                    selector: (selectorState) {
                                      final currSpread =
                                          switch (selectorState) {
                                            ProductDetailsSuccess(
                                              :final infoModel,
                                            ) =>
                                              infoModel.spread,
                                            _ => null,
                                          };
                                      return currSpread;
                                    },

                                    builder: (_, detailsState) {
                                      return Column(
                                        children:
                                            generalDataGroup(
                                              info,
                                              detailsState ?? 0,
                                            ).map((item) {
                                              return Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      vertical: 24.0,
                                                    ),
                                                child: DuploKeyValueDisplay(
                                                  title: item.title,
                                                  keyValuePairs: item.data,
                                                ),
                                              );
                                            }).toList(),
                                      );
                                    },
                                  ),
                                  Column(
                                    children:
                                        sectionData.map((item) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 24.0,
                                            ),
                                            child: DuploKeyValueDisplay(
                                              title: item.title,
                                              keyValuePairs: item.data,
                                            ),
                                          );
                                        }).toList(),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                }(),
            };
          },
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
