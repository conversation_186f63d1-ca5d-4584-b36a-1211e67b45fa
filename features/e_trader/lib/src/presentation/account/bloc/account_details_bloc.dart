import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:domain/domain.dart';
import 'package:e_trader/src/data/api/trade_account_model.dart';

import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/account_balance_hub_response.dart';
import 'package:e_trader/src/domain/exceptions/account_not_found_exception.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_trading_account_balance_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'account_details_bloc.freezed.dart';
part 'account_details_event.dart';
part 'account_details_state.dart';

class AccountDetailsBloc extends Bloc<AccountDetailsEvent, AccountDetailsState>
    with DisposableMixin {
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final UpdateTradingAccountBalanceHubUseCase
  _updateTradingAccountBalanceHubUseCase;
  final SubscribeToTradingAccountBalanceUseCase _getTradingAccountUseCase;
  final GetTradingPreferencesUseCase _getTradingPreferencesUseCase;
  final TradingAccountModel? _tradingAccountModel;

  final LoggerBase _logger;

  AccountDetailsBloc(
    this._getSelectedAccountUseCase,
    this._getTradingAccountUseCase,
    this._updateTradingAccountBalanceHubUseCase,
    this._getTradingPreferencesUseCase,
    this._tradingAccountModel,
    this._logger,
  ) : super(AccountDetailsState()) {
    on<AccountDetailsEvent>((event, emit) async {
      if (event is _SubscribeToTradingAccountBalance) {
        await _subscribeToTradingAccountBalance(emit);
      } else if (event is _UpdateTradingAccountBalance) {
        _updateTradingAccountBalance(event);
      } else if (event is _UpdateTradingAccountLeverage) {
        _updateTradingAccountLeverage(emit);
      }
    });
    on<_ProcessTradingAccountBalance>(
      _processTradingAccountBalance,
      transformer: droppable(),
    );
    on<_ProcessAccountBalanceError>(
      _processAccountBalanceError,
      transformer: droppable(),
    );
  }

  Future<void> _subscribeToTradingAccountBalance(
    Emitter<AccountDetailsState> emit,
  ) async {
    final result =
        await TaskEither<
          Exception,
          (TradingAccountModel, Stream<AccountBalanceHubResponse?>)
        >.Do(($) async {
          final client = _tradingAccountModel ?? _getSelectedAccountUseCase();
          if (client == null) {
            throw AccountNotFoundException('Account Not Found');
          }
          final getTradingAccountResult = await $(
            _getTradingAccountUseCase(
              [client.accountNumber],
              subscriberId: '${AccountDetailsBloc}_$hashCode',
              eventType: TradingSocketEvent.accountBalance.register,
            ),
          );

          return (client, getTradingAccountResult);
        }).run();
    return result.fold(
      (error) {
        addError(error);
        emit(
          state.copyWith(
            accountProcessState: AccountDetailsProcessState.error(),
          ),
        );
      },
      (value) {
        final client = value.$1;
        final stream = value.$2;
        emit(
          state.copyWith(
            accountdetails: client,
            accountProcessState: AccountDetailsProcessState.connected(),
          ),
        );
        addSubscription(
          stream.listen(
            (accountBalanceResponse) => add(
              AccountDetailsEvent.processTradingAccountBalance(
                accountBalanceResponse,
              ),
            ),
            onError: (Object error, stackTrace) {
              addError(error);
              add(AccountDetailsEvent.processError(error));
            },
          ),
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error, stackTrace: stackTrace);
    super.addError(error, stackTrace);
  }

  void _updateTradingAccountLeverage(Emitter<AccountDetailsState> emit) {
    final savedLeverageString = _getTradingPreferencesUseCase
        .getLeverageForSpesificAccountNumber(
          state.accountdetails?.accountNumber ?? "",
        );

    emit(
      state.copyWith(
        accountdetails: state.accountdetails?.copyWith(
          leverage: int.tryParse(savedLeverageString) ?? 100,
        ),
      ),
    );
  }

  void _updateTradingAccountBalance(_UpdateTradingAccountBalance event) {
    try {
      _updateTradingAccountBalanceHubUseCase(
        eventType: event.eventType,
        accountNumbers: [
          if (state.accountdetails?.accountNumber != null)
            state.accountdetails!.accountNumber,
        ],
      );
    } catch (e) {
      addError(e);
    }
  }

  void _processTradingAccountBalance(
    _ProcessTradingAccountBalance event,
    Emitter<AccountDetailsState> emit,
  ) {
    final accountBalanceResponse = event.accountBalanceHubResponse;
    if (accountBalanceResponse == null) {
      emit(
        state.copyWith(accountProcessState: AccountDetailsProcessState.empty()),
      );
      return;
    }
    emit(
      state.copyWith(
        accountProcessState: AccountDetailsProcessState.success(),
        tradingAccountUpdates: accountBalanceResponse.account,
      ),
    );
  }

  FutureOr<void> _processAccountBalanceError(
    _ProcessAccountBalanceError event,
    Emitter<AccountDetailsState> emit,
  ) {
    emit(
      state.copyWith(accountProcessState: AccountDetailsProcessState.error()),
    );
  }
}
