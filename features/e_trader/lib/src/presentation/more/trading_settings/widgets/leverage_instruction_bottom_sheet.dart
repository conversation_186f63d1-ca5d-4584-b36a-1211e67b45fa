import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';

class LeverageInstructionBottomSheet extends StatelessWidget {
  const LeverageInstructionBottomSheet();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          DuploText(
            text: EquitiLocalization.of(context).trader_adjustYourLeverage,
            style: context.duploTextStyles.textSm,
            textAlign: TextAlign.start,
            color: context.duploTheme.text.textSecondary,
          ),
          const SizedBox(height: 16),
          DuploWarningContainer(
            margin: EdgeInsets.zero,
            description:
                EquitiLocalization.of(context).trader_highLeverageWarning,
          ),
          const SizedBox(height: 12),
          DuploButton.secondary(
            useFullWidth: true,
            title: EquitiLocalization.of(context).trader_close,
            onTap: () => Navigator.pop(context),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
