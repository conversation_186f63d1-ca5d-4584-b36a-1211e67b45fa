import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/domain/usecase/save_selected_account_id_for_payments_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/model/account_view_model.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_settings_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/empty_demo_accounts.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/server_not_supported_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_account_card_shimmer.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_account_card_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_accounts_header_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class TradingAccountsWidget extends StatelessWidget {
  const TradingAccountsWidget(
    this.tradingEnvironment,
    this.footer, {
    super.key,
  });

  final TradingEnvironment tradingEnvironment;
  final Widget footer;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return BlocBuilder<AccountsBloc, AccountsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, accountState) {
        return switch (accountState.processState) {
          AccountsLoadingProcessState() => ListView(
            children: const [
              TradingAccountCardShimmer(),
              TradingAccountCardShimmer(),
              TradingAccountCardShimmer(),
            ],
          ),
          AccountsSuccessProcessState() => () {
            final tradingAccountsViewModel = accountState.tradingAccounts;
            final tradingAccounts = accountState.accounts;

            if (tradingAccountsViewModel.isEmpty) {
              return tradingEnvironment == TradingEnvironment.demo
                  ? EmptyDemoAccounts(
                    onTap: () {
                      diContainer<EquitiTraderNavigation>()
                          .navigateToCreateAccountMain(
                            createAccountFlow: CreateAccountFlow.demoAccount,
                            thenCallback: () {
                              builderContext.read<AccountsBloc>().add(
                                AccountsEvent.refreshAccounts(),
                              );
                            },
                          );
                    },
                  )
                  : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      trader.Assets.images.noWallet.svg(),
                      const SizedBox(height: 16),
                      DuploText(
                        text:
                            EquitiLocalization.of(
                              context,
                            ).trader_noAccountsAvailable,
                        style: textStyles.textLg,
                        color: theme.text.textPrimary,
                        fontWeight: DuploFontWeight.semiBold,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      DuploText(
                        textAlign: TextAlign.center,
                        color: theme.text.textPrimary,
                        text:
                            EquitiLocalization.of(
                              context,
                            ).trader_noLiveAccountsDescription,
                        style: textStyles.textSm,
                      ),
                    ],
                  );
            }

            return CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsetsDirectional.only(start: 16.0),
                    child: TradingAccountsHeaderWidget(tradingEnvironment),
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 16)),
                SliverList.separated(
                  itemCount: tradingAccountsViewModel.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (listViewContext, index) {
                    final tradingAccountViewModel =
                        tradingAccountsViewModel[index];

                    return TradingAccountCardWidget(
                      isSelected: tradingAccountViewModel.isSelected,
                      platformType: tradingAccountViewModel.platformType,
                      accountName: tradingAccountViewModel.nickName ?? '',
                      accountNumber:
                          tradingAccountViewModel.accountNumber ?? '',
                      tags: [
                        tradingAccountViewModel.platformType.displayName,
                        tradingAccountViewModel.platformTypeName,
                        if (tradingAccountViewModel.isSwapFree)
                          EquitiLocalization.of(context).trader_swapFree,
                      ],
                      equity: tradingAccountViewModel.equity ?? 0,
                      profit: tradingAccountViewModel.profit ?? 0,
                      marginLevel: tradingAccountViewModel.marginLevel ?? 0,
                      margin: tradingAccountViewModel.margin ?? 0,
                      balance: tradingAccountViewModel.balance ?? 0,
                      currency: tradingAccountViewModel.homeCurrency,
                      accountCategory: AccountCategory.accounts,
                      onActionPressed: () {
                        onActionPressed(
                          listViewContext,
                          tradingAccounts,
                          tradingAccountViewModel,
                        );
                      },
                      onTap: () {
                        if (tradingAccountViewModel.platformType ==
                            PlatformType.mt4) {
                          serverNotSupportedBottomSheet(
                            listViewContext,
                            tradingAccounts.firstOrNullWhere(
                              (tradingAccount) =>
                                  tradingAccountViewModel.accountNumber ==
                                  tradingAccount.accountNumber,
                            )!,
                          );
                          return;
                        }
                        listViewContext.read<AccountsBloc>().add(
                          AccountsEvent.onAccountSelected(
                            accountId: tradingAccountViewModel.accountId,
                          ),
                        );
                      },
                    );
                  },
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: footer,
                    ),
                  ),
                ),
              ],
            );
          }(),
          AccountsErrorProcessState() => Center(
            child: DuploText(
              text: EquitiLocalization.of(context).trader_somethingWentWrong,
              style: textStyles.textSm,
            ),
          ),
        };
      },
    );
  }

  void onActionPressed(
    BuildContext listViewContext,
    List<TradingAccountModel> tradingAccounts,
    AccountViewModel tradingAccountViewModel,
  ) {
    final selectedAccount =
        tradingAccounts.firstOrNullWhere(
          (tradingAccount) =>
              tradingAccountViewModel.accountNumber ==
              tradingAccount.accountNumber,
        )!;

    showAccountSettingsBottomSheet<bool?>(
      listViewContext,
      selectedAccount,
      tradingEnvironment,
      onDepositPressed: () {
        listViewContext.read<AccountsBloc>().add(
          AccountsEvent.goToDepositPaymentOptions(),
        );
        _saveSelectedAccountIdForPaymentsUseCase(
          tradingAccountViewModel.accountId,
        );
      },
      onWithdrawPressed: () {
        listViewContext.read<AccountsBloc>().add(
          AccountsEvent.goToWithdrawPaymentOptions(),
        );
        _saveSelectedAccountIdForPaymentsUseCase(
          tradingAccountViewModel.accountId,
        );
      },
      onTransferPressed: () {
        listViewContext.read<AccountsBloc>().add(
          AccountsEvent.goToTransferOptions(),
        );
        _saveSelectedAccountIdForPaymentsUseCase(
          tradingAccountViewModel.accountId,
        );
      },
    ).then((value) {
      if (value == true) {
        listViewContext.read<AccountsBloc>().add(
          const AccountsEvent.refreshAccounts(),
        );
      }
    });
  }

  void _saveSelectedAccountIdForPaymentsUseCase(String accountId) async {
    await diContainer<SaveSelectedAccountIdForPaymentsUseCase>().call(
      accountId,
    );
  }
}
