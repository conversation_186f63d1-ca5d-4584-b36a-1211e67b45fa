// ignore_for_file: avoid-passing-async-when-sync-expected

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/di/trading_environment_dependencies.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_empty_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_list_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/empty_demo_accounts.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/switch_account_portfolio_section.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

class SwitchAccountContent extends StatefulWidget {
  const SwitchAccountContent({
    required this.selectedTradingEnvironment,
    required this.selectedCategoryIndex,
    required this.onAccountCategoryChanged,
    super.key,
  });
  final TradingEnvironment selectedTradingEnvironment;
  final int selectedCategoryIndex;
  final void Function(AccountCategory) onAccountCategoryChanged;

  @override
  State<SwitchAccountContent> createState() => _SwitchAccountContentState();
}

class _SwitchAccountContentState extends State<SwitchAccountContent>
    with PerformanceObserverMixin {
  late final SheetController _sheetController;

  @override
  void initState() {
    super.initState();
    _sheetController = SheetController();
  }

  @override
  void dispose() {
    _sheetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }
        try {
          await diContainer<UpdateTradingAccountBalanceHubUseCase>().call(
            eventType: TradingSocketEvent.accountBalance.unsubscribe,
            accountNumbers:
                context
                    .read<AccountsBloc>()
                    .state
                    .tradingAccounts
                    .map((account) => account.accountNumber!)
                    .toList(),
          );
          final selectedAccount =
              diContainer<GetSelectedAccountUseCase>().call();
          final isDemo = selectedAccount?.isDemo ?? false;
          TradingEnvironmentDependencies.register(isDemo: isDemo);
        } catch (error, stackTrace) {
          diContainer<LoggerBase>().logError(error, stackTrace: stackTrace);
        } finally {
          Navigator.pop(context);
        }
      },
      child: BlocBuilder<AccountsBloc, AccountsState>(
        buildWhen: (previous, current) => previous != current,
        builder: (blocBuilderContext, state) {
          return Padding(
            padding: EdgeInsets.only(top: 4.0),
            child: NestedScrollView(
              headerSliverBuilder: (_, innerBoxIsScrolled) {
                return [
                  if (widget.selectedTradingEnvironment ==
                          TradingEnvironment.live &&
                      ((state.processState is AccountsSuccessProcessState &&
                              state.accounts.isNotEmpty) ||
                          state.processState is AccountsLoadingProcessState))
                    SliverToBoxAdapter(child: SwitchAccountPortfolioSection()),
                ];
              },
              body: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: theme.border.borderSecondary),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(24),
                  ),
                ),
                padding: const EdgeInsetsDirectional.symmetric(
                  vertical: 24,
                  horizontal: 16,
                ),
                child: _buildAccountContent(blocBuilderContext, state),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds the appropriate content widget based on the current state
  Widget _buildAccountContent(BuildContext context, AccountsState state) {
    final isSuccess = state.processState is AccountsSuccessProcessState;
    final isEmpty = state.accounts.isEmpty;
    final isDemo = widget.selectedTradingEnvironment == TradingEnvironment.demo;

    // Empty demo accounts
    if (isSuccess && isEmpty && isDemo) {
      return EmptyDemoAccounts(
        onTap: () {
          diContainer<EquitiTraderNavigation>().navigateToCreateAccountMain(
            createAccountFlow: CreateAccountFlow.demoAccount,
            thenCallback: () {
              context.read<AccountsBloc>().add(AccountsEvent.refreshAccounts());
            },
          );
        },
      );
    }

    // Empty live accounts
    if (isSuccess && isEmpty) {
      return AccountEmptyWidget(
        selectedTradingEnvironment: widget.selectedTradingEnvironment,
        selectedCategoryIndex: widget.selectedCategoryIndex,
        onAddAccountTap:
            () => _navigateToCreateAccount(
              context,
              thenCallback: () {
                context.read<AccountsBloc>().add(
                  AccountsEvent.refreshAccounts(),
                );
              },
            ),
      );
    }

    return AccountListWidget(
      selectedTradingEnvironment: widget.selectedTradingEnvironment,
      selectedCategoryIndex: widget.selectedCategoryIndex,
      onAccountCategoryChanged: widget.onAccountCategoryChanged,
      tradingAccountsCount: state.tradingAccounts.length,
      walletsCount: state.wallets.length,
      onAddAccountTap:
          (ctx) => _navigateToCreateAccount(
            ctx,
            thenCallback: () {
              ctx.read<AccountsBloc>().add(AccountsEvent.refreshAccounts());
            },
          ),
    );
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    final rawResult =
        diContainer<EquitiNavigatorBase>().globalData[EquitiTraderRouteSchema
            .switchAccountRoute
            .url];
    final result = rawResult is bool ? rawResult : false;
    if (result) {
      context.read<AccountsBloc>().add(AccountsEvent.refreshAccounts());
      diContainer<EquitiNavigatorBase>().globalData.remove(
        EquitiTraderRouteSchema.switchAccountRoute.url,
      );
    }

    // Subscribe to balance updates when route is popped (returning to this screen)
    context.read<AccountsBloc>().add(
      AccountsEvent.updateBalanceSubscription(subscribe: true),
    );
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    super.onRoutePushed(route);
    // Unsubscribe from balance updates when route is pushed (leaving this screen)
    if (route.settings.name != EquitiTraderRouteSchema.navBarRoute.url) {
      context.read<AccountsBloc>().add(
        AccountsEvent.updateBalanceSubscription(subscribe: false),
      );
    }
  }

  /// Determines the appropriate create account flow and navigates to the create account screen
  void _navigateToCreateAccount(
    BuildContext context, {
    required void Function() thenCallback,
  }) {
    // TODO:(AAKASH) check for max account length
    final createAccountFlow = _determineCreateAccountFlow(context);
    diContainer<EquitiTraderNavigation>().navigateToCreateAccountMain(
      createAccountFlow: createAccountFlow,
      thenCallback: thenCallback,
    );
  }

  /// Determines which create account flow to use based on environment and existing accounts
  CreateAccountFlow _determineCreateAccountFlow(BuildContext context) {
    if (widget.selectedTradingEnvironment == TradingEnvironment.demo) {
      return CreateAccountFlow.demoAccount;
    }

    final hasExistingAccounts =
        context.read<AccountsBloc>().state.accounts.isNotEmpty;

    return hasExistingAccounts
        ? CreateAccountFlow.additionalLiveAccount
        : CreateAccountFlow.firstLiveAccount;
  }
}
