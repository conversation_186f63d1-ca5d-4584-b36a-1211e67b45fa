import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/account_toggle_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/add_new_account_button.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_accounts_widget.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/wallets_widget.dart';
import 'package:flutter/material.dart';

class AccountListWidget extends StatelessWidget {
  const AccountListWidget({
    required this.selectedTradingEnvironment,
    required this.selectedCategoryIndex,
    required this.onAccountCategoryChanged,
    required this.tradingAccountsCount,
    required this.walletsCount,
    required this.onAddAccountTap,
    super.key,
  });

  final TradingEnvironment selectedTradingEnvironment;
  final int selectedCategoryIndex;
  final void Function(AccountCategory) onAccountCategoryChanged;
  final int tradingAccountsCount;
  final int walletsCount;
  final void Function(BuildContext context) onAddAccountTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (selectedTradingEnvironment == TradingEnvironment.live)
          AccountToggleWidget(
            selectedValue: switch (selectedCategoryIndex) {
              0 => AccountCategory.accounts,
              1 => AccountCategory.wallets,
              _ => throw UnimplementedError(),
            },
            onChanged: onAccountCategoryChanged,
            accountsCount: tradingAccountsCount,
            walletsCount: walletsCount,
          ),
        const SizedBox(height: 16),
        Expanded(
          child: switch (selectedCategoryIndex) {
            0 => TradingAccountsWidget(
              selectedTradingEnvironment,
              AddNewAccountButton(
                selectedCategoryIndex: selectedCategoryIndex,
                onTap: () => onAddAccountTap(context),
              ),
            ),
            1 => WalletsWidget(
              footer: AddNewAccountButton(
                selectedCategoryIndex: selectedCategoryIndex,
              ),
            ),
            _ => throw UnimplementedError(),
          },
        ),
      ],
    );
  }
}
