import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/create_new_wallet/create_new_wallet_content.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddNewAccountButton extends StatelessWidget {
  final int selectedCategoryIndex;
  final void Function()? onTap;

  const AddNewAccountButton({required this.selectedCategoryIndex, this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    return BlocBuilder<AccountsBloc, AccountsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (blocBuilderContext, state) {
        final isLoading = state.processState is AccountsLoadingProcessState;

        return SafeArea(
          top: false,
          child: switch (selectedCategoryIndex) {
            0 => DuploButton.defaultPrimary(
              title: EquitiLocalization.of(context).trader_addNewAccount,
              leadingIcon: Assets.images.plus.keyName,
              useFullWidth: true,
              isDisabled: isLoading,
              onTap: () => onTap?.call(),
            ),
            1 => DuploButton.defaultPrimary(
              title: EquitiLocalization.of(context).trader_addNewWallet,
              leadingIcon: Assets.images.plus.keyName,
              useFullWidth: true,
              isDisabled: isLoading,
              onTap: () {
                DuploSheet.showNonScrollableModalSheet<bool?>(
                  backgroundColor: theme.background.bgPrimary,
                  hasTrailingIc: false,
                  leadingNavBarWidget: Align(
                    alignment:
                        Directionality.of(context) == TextDirection.rtl
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Assets.images
                          .arrowLeftDirectional(context)
                          .svg(
                            colorFilter: ColorFilter.mode(
                              theme.foreground.fgSecondary,
                              BlendMode.srcIn,
                            ),
                          ),
                    ),
                  ),
                  alignment: AlignmentDirectional.centerStart,
                  context: context,
                  title: EquitiLocalization.of(context).trader_addNewWallet,
                  content: (_) => const CreateNewWalletContent(),
                ).then((result) {
                  if (result == true) {
                    blocBuilderContext.read<AccountsBloc>().add(
                      AccountsEvent.refreshAccounts(),
                    );
                  }
                });
              },
            ),
            _ => throw UnimplementedError(),
          },
        );
      },
    );
  }
}
