import 'package:duplo/duplo.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class SwitchAccountPortfolioSection extends StatefulWidget {
  const SwitchAccountPortfolioSection();

  @override
  State<SwitchAccountPortfolioSection> createState() =>
      SwitchAccountPortfolioSectionState();
}

class SwitchAccountPortfolioSectionState
    extends State<SwitchAccountPortfolioSection> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    final state = context.read<AccountsBloc>().state;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 8),
          child: Column(
            children: [
              AnimatedCrossFade(
                duration: Duration(milliseconds: 300),
                crossFadeState:
                    _isExpanded
                        ? CrossFadeState.showSecond
                        : CrossFadeState.showFirst,
                firstCurve: Curves.easeOut,
                secondCurve: Curves.easeOut,
                firstChild: _PortfolioListItem(
                  label: localization.trader_totalEquity,
                  value: state.totalEquity ?? 0,
                  currency: state.selectedCurrency,
                  onInfoTap: () {
                    _showInfoModal(
                      context,
                      localization.trader_totalEquity,
                      localization.trader_totalEquityDescription,
                    );
                  },
                ),
                secondChild: Column(
                  children: [
                    Row(
                      children: [
                        DuploText(
                          text: localization.trader_displayCurrency,
                          style: duploTextStyles.textXs,
                          fontWeight: DuploFontWeight.medium,
                          color: theme.text.textSecondary,
                        ),
                        Spacer(),
                        GestureDetector(
                          onTap: () {
                            DuploDropDown.customBottomSheetSelector(
                              context: context,
                              bottomSheetTitle:
                                  EquitiLocalization.of(
                                    context,
                                  ).trader_selectCurrency,
                              items:
                                  state.availableCurrencies
                                      .map(
                                        (currency) => DropDownItemModel(
                                          title: currency,
                                          image:
                                              FlagProvider.getFlagFromCurrencyCode(
                                                currency,
                                              ),
                                        ),
                                      )
                                      .toList(),
                              selectedIndex: state.availableCurrencies.indexOf(
                                state.selectedCurrency,
                              ),
                              onChanged: (index) {
                                final selectedCurrency = state
                                    .availableCurrencies
                                    .elementAtOrNull(index);
                                if (selectedCurrency != null) {
                                  context.read<AccountsBloc>().add(
                                    AccountsEvent.onCurrencySelected(
                                      currency: selectedCurrency,
                                    ),
                                  );
                                }
                              },
                            );
                          },
                          child: DuploTagContainer.sm(
                            leading: FlagProvider.getFlagFromCurrencyCode(
                              state.selectedCurrency,
                            ),
                            text: state.selectedCurrency,
                            trailing: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Assets.images.chevronSelectorVertical.svg(
                                width: 10,
                                height: 10,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Divider(color: theme.border.borderSecondary),
                  ],
                ),
              ),

              ClipRect(
                child: AnimatedAlign(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                  heightFactor: _isExpanded ? 1.0 : 0.0,
                  alignment: Alignment.topCenter,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 8),
                      _PortfolioListItem(
                        label: localization.trader_totalEquity,
                        value: state.totalEquity ?? 0,
                        currency: state.selectedCurrency,
                        onInfoTap: () {
                          _showInfoModal(
                            context,
                            localization.trader_totalEquity,
                            localization.trader_totalEquityDescription,
                          );
                        },
                      ),
                      SizedBox(height: 16),
                      _PortfolioListItem(
                        label: localization.trader_totalProfit,
                        value: state.totalProfit ?? 0,
                        currency: state.selectedCurrency,
                        onInfoTap: () {
                          _showInfoModal(
                            context,
                            localization.trader_totalProfit,
                            localization.trader_totalProfitDescription,
                          );
                        },
                      ),
                      SizedBox(height: 16),
                      _PortfolioListItem(
                        label: localization.trader_totalBalance,
                        value: state.totalBalance ?? 0,
                        currency: state.selectedCurrency,
                        onInfoTap: () {
                          _showInfoModal(
                            context,
                            localization.trader_totalBalance,
                            localization.trader_totalBalanceDescription,
                          );
                        },
                      ),
                      SizedBox(height: 16),
                      _PortfolioListItem(
                        label: localization.trader_totalCredit,
                        value: state.totalCredit ?? 0,
                        currency: state.selectedCurrency,
                        onInfoTap: () {
                          _showInfoModal(
                            context,
                            localization.trader_totalCredit,
                            localization.trader_totalCreditDescription,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            padding: EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: theme.background.bgTertiary,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedRotation(
                  turns: _isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                  child: Assets.images.chevronDown.svg(),
                ),
                SizedBox(width: 4),
                DuploText(
                  text:
                      _isExpanded
                          ? localization.trader_hide
                          : localization.trader_showAll,
                  style: duploTextStyles.textXs,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textQuaternary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showInfoModal(BuildContext context, String title, String description) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);

    DuploSheet.showModalSheetV2<void>(
      context,
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            DuploText(
              text: title,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
              maxLines: 1,
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: description,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
              textAlign: TextAlign.start,
            ),
          ],
        ),
      ),
      bottomBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DuploButton.secondary(
          title: localization.login_dismiss,
          onTap: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }
}

class _PortfolioListItem extends StatelessWidget {
  const _PortfolioListItem({
    required this.value,
    required this.label,
    required this.currency,
    this.onInfoTap,
  });

  final double value;
  final String label;
  final String currency;
  final void Function()? onInfoTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final formattedValue = EquitiFormatter.decimalPatternDigits(
      value: value,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );
    final whole = formattedValue.split('.').firstOrNull!;
    final fraction = formattedValue.split('.').elementAtOrNull(1);
    return Container(
      child: Row(
        children: [
          DuploText(
            text: label,
            style: context.duploTextStyles.textXs,
            color: theme.text.textSecondary,
          ),
          SizedBox(width: 4),
          GestureDetector(
            onTap: onInfoTap,
            child: Assets.images.help.svg(width: 12, height: 12),
          ),
          Spacer(),
          Directionality(
            textDirection: TextDirection.ltr,
            child: DuploText.rich(
              spans: [
                DuploTextSpan(
                  text: whole,
                  style: context.duploTextStyles.textSm,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                DuploTextSpan(
                  text: ".",
                  style: context.duploTextStyles.textXs,
                  color: theme.text.textSecondary,
                ),
                DuploTextSpan(
                  text: fraction!,
                  style: context.duploTextStyles.textXs,
                  color: theme.text.textSecondary,
                ),

                DuploTextSpan(
                  text: " $currency",
                  style: context.duploTextStyles.textXs,
                  color: theme.text.textSecondary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
