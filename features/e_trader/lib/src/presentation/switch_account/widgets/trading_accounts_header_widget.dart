import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class TradingAccountsHeaderWidget extends StatelessWidget {
  const TradingAccountsHeaderWidget(this.tradingEnvironment, {super.key});

  final TradingEnvironment tradingEnvironment;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text:
              tradingEnvironment == TradingEnvironment.live
                  ? EquitiLocalization.of(context).trader_cfdTradingAccounts
                  : EquitiLocalization.of(context).trader_demoTradingAccounts,
          style: textStyles.textMd,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
        ),
        Flexible(
          child: DuploText(
            text:
                EquitiLocalization.of(context).trader_switchAccountDescription,
            style: textStyles.textXs,
            color: theme.text.textTertiary,
          ),
        ),
      ],
    );
  }
}
