import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as assetsTrader;

class TradingAccountCardWidget extends StatelessWidget {
  const TradingAccountCardWidget({
    super.key,
    required this.accountName,
    this.accountNumber,
    required this.accountCategory,
    required this.equity,
    required this.profit,
    this.marginLevel,
    this.margin,
    required this.balance,
    required this.currency,
    this.onActionPressed,
    required this.onTap,
    this.tags = const [],
    this.isSelected = false,
    this.platformType,
  }) : assert(
         accountCategory != AccountCategory.accounts || platformType != null,
         'platformType is required for accounts',
       );

  final bool isSelected;
  final AccountCategory accountCategory;
  final String accountName;
  final String? accountNumber;
  final List<String> tags;
  final String currency;
  final double equity;
  final double profit;
  final double? marginLevel;
  final double balance;
  final VoidCallback? onActionPressed;
  final VoidCallback onTap;
  final PlatformType? platformType;
  final double? margin;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final formattedEquity = EquitiFormatter.decimalPatternDigits(
      value: equity,
      locale: Localizations.localeOf(context).toString(),
      digits: 2,
    );
    final formattedProfit = EquitiFormatter.decimalPatternDigits(
      value: profit,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );
    final formattedBalance = EquitiFormatter.decimalPatternDigits(
      value: balance,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );

    final String equityWhole = formattedEquity.split('.').firstOrNull!;
    final String equityFraction =
        formattedEquity.split('.').elementAtOrNull(1)!;
    final String profitWhole = formattedProfit.split('.').firstOrNull!;
    final String profitFraction =
        formattedProfit.split('.').elementAtOrNull(1)!;
    final String balanceWhole = formattedBalance.split('.').firstOrNull!;
    final String balanceFraction =
        formattedBalance.split('.').elementAtOrNull(1)!;

    return Card(
      elevation: 0,
      color: theme.background.bgSecondaryHover,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: theme.border.borderSecondary, width: 1.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: theme.background.bgPrimary,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Color(0xff0A0D121A),
              blurRadius: 8,
              spreadRadius: -12,
              offset: const Offset(10, 0),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsetsDirectional.all(16),
          child: Column(
            children: [
              _Header(
                accountName: accountName,
                accountNumber: accountNumber,
                accountCurrency: currency,
                accountCategory: accountCategory,
                tags: tags,
                onActionPressed: onActionPressed,
              ),
              const SizedBox(height: 16),
              _Content(
                currency: currency,
                accountCategory: accountCategory,
                balanceWhole: balanceWhole,
                balanceFraction: balanceFraction,
                equityWhole: equityWhole,
                equityFraction: equityFraction,
                profitWhole: profitWhole,
                profitFraction: profitFraction,
              ),
              if (accountCategory == AccountCategory.accounts) ...[
                const SizedBox(height: 8),
                _Footer(
                  marginLevel: marginLevel!,
                  platformType: platformType!,
                  balance: balance,
                  margin: margin!,
                  onTap: onTap,
                  accountName: accountName,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.accountName,
    this.accountNumber,
    required this.accountCategory,
    required this.accountCurrency,
    this.tags = const [],
    this.onActionPressed,
  });

  final String accountName;
  final String accountCurrency;
  final String? accountNumber;
  final AccountCategory accountCategory;
  final List<String> tags;
  final VoidCallback? onActionPressed;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                children: [
                  DuploText(
                    text: accountName,
                    style: textStyles.textXs,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textSecondary,
                  ),
                  if (accountNumber != null) ...[
                    const SizedBox(width: 4),
                    DuploText(
                      text: '($accountNumber)',
                      style: textStyles.textXs,
                      color: theme.text.textSecondary,
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  if (accountCategory == AccountCategory.accounts)
                    DuploTagContainer.xs(
                      leading: FlagProvider.getFlagFromCurrencyCode(
                        accountCurrency,
                      ),
                      text: accountCurrency,
                      type: DuploTagType.neutral,
                    ),
                  if (accountCategory == AccountCategory.accounts)
                    ...tags
                        .map<Widget>(
                          (tag) => DuploTagContainer.xs(
                            text: tag,
                            type: DuploTagType.neutral,
                          ),
                        )
                        .toList(),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        if (accountCategory == AccountCategory.accounts)
          GestureDetector(
            onTap: onActionPressed,
            child: Container(
              height: 40,
              width: 40,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.background.bgSecondary,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: theme.border.borderTertiary),
              ),
              child: assetsTrader.Assets.images.settings.svg(
                colorFilter: ColorFilter.mode(
                  theme.foreground.fgSecondary,
                  BlendMode.srcIn,
                ),
              ),
            ),
          )
        else
          DuploTagContainer.xs(
            leading: FlagProvider.getFlagFromCurrencyCode(accountCurrency),
            text: accountCurrency,
            type: DuploTagType.neutral,
          ),
      ],
    );
  }
}

class _Content extends StatelessWidget {
  const _Content({
    required this.equityWhole,
    required this.equityFraction,
    required this.profitWhole,
    required this.profitFraction,
    required this.accountCategory,
    required this.balanceWhole,
    required this.balanceFraction,
    required this.currency,
  });

  final String equityWhole;
  final String equityFraction;
  final String profitWhole;
  final String profitFraction;
  final AccountCategory accountCategory;
  final String balanceWhole;
  final String balanceFraction;
  final String currency;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return Row(
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text:
                  accountCategory == AccountCategory.accounts
                      ? EquitiLocalization.of(context).trader_equity
                      : EquitiLocalization.of(context).trader_balance,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textTertiary,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            DuploText.rich(
              spans: [
                DuploTextSpan(
                  text:
                      accountCategory == AccountCategory.accounts
                          ? equityWhole
                          : balanceWhole,
                  style: textStyles.textLg,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                DuploTextSpan(
                  text: '.',
                  style: textStyles.textSm,
                  color: theme.text.textSecondary,
                ),
                DuploTextSpan(
                  text:
                      accountCategory == AccountCategory.accounts
                          ? equityFraction
                          : balanceFraction,
                  style: textStyles.textSm,
                  color: theme.text.textSecondary,
                ),
              ],
            ),
          ],
        ),
        const Spacer(),
        if (accountCategory == AccountCategory.accounts)
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              DuploText(
                text: EquitiLocalization.of(context).trader_profit,
                style: textStyles.textXs,
                fontWeight: DuploFontWeight.medium,
                color: theme.text.textTertiary,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Directionality(
                textDirection: TextDirection.ltr,
                child: DuploText.rich(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  spans: [
                    DuploTextSpan(
                      text: profitWhole,
                      style: textStyles.textLg,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                    ),
                    DuploTextSpan(
                      text: '.',
                      style: textStyles.textSm,
                      color: theme.text.textSecondary,
                    ),
                    DuploTextSpan(
                      text: profitFraction,
                      style: textStyles.textSm,
                      color: theme.text.textSecondary,
                    ),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }
}

class _Footer extends StatelessWidget {
  const _Footer({
    required this.marginLevel,
    required this.platformType,
    required this.balance,
    required this.margin,
    required this.onTap,
    required this.accountName,
  });

  final double marginLevel;
  final double balance;
  final PlatformType platformType;
  final double margin;
  final VoidCallback onTap;
  final String accountName;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        Row(
          children: [
            DuploText(
              text: localization.trader_marginLevel,
              style: style.textXs,
              color: theme.text.textSecondary,
            ),
            SizedBox(width: 8),
            Expanded(
              child: DuploMarginProgressBar(
                showMarginTextBesideBar: true,
                balance: balance,
                margin: margin,
                marginLevel: marginLevel,
                height: 4,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        DuploTap(
          onTap: onTap,
          semanticsIdentifier: '${accountName}_account_card',
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: theme.border.borderTertiary),
              ),
              color: theme.background.bgSecondary,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (platformType == PlatformType.mt4)
                  assetsTrader.Assets.images.server.svg(
                    height: 18,
                    width: 18,
                    colorFilter: ColorFilter.mode(
                      theme.foreground.fgSecondary,
                      BlendMode.srcIn,
                    ),
                  )
                else
                  assetsTrader.Assets.images.forex.svg(
                    height: 18,
                    width: 18,
                    colorFilter: ColorFilter.mode(
                      theme.foreground.fgSecondary,
                      BlendMode.srcIn,
                    ),
                  ),
                const SizedBox(width: 4),
                DuploText(
                  text:
                      platformType == PlatformType.mt4
                          ? localization.trader_serverDetailsText
                          : localization.trader_markets,
                  style: style.textXs,
                  color: theme.text.textPrimary,
                  fontWeight: DuploFontWeight.medium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
