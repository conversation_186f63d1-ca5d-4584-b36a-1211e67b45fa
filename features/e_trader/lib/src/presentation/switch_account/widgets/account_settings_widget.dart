// ignore_for_file: prefer-match-file-name
import 'package:duplo/duplo.dart';
import 'package:domain/domain.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/presentation/account/account_details/account_details_tab.dart';
import 'package:e_trader/src/presentation/account/bloc/account_details_bloc.dart';
import 'package:e_trader/src/presentation/change_account_password/change_account_password_screen.dart';
import 'package:e_trader/src/presentation/change_leverage/change_leverage_screen.dart';
import 'package:e_trader/src/presentation/reset_balance/reset_balance_screen.dart';
import 'package:e_trader/src/presentation/switch_account/rename_account/show_rename_account_bottom_sheet.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/show_server_details_bottom_sheet.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

Future<T?> showAccountSettingsBottomSheet<T>(
  BuildContext context,
  TradingAccountModel account,
  TradingEnvironment tradingEnvironment, {
  VoidCallback? onDepositPressed,
  VoidCallback? onWithdrawPressed,
  VoidCallback? onTransferPressed,
}) {
  return DuploSheet.showModalSheetV2<T?>(
    settings: RouteSettings(name: 'account_settings_bottom_sheet'),
    context,
    content: Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 16),
          if (tradingEnvironment == TradingEnvironment.live) ...[
            _AccountSummaryWidget(account),
            SizedBox(height: 16),
            DuploFundingButtons(
              onDepositPressed: () {
                Navigator.of(context).pop();
                onDepositPressed?.call();
              },
              onWithdrawPressed: () {
                Navigator.of(context).pop();
                onWithdrawPressed?.call();
              },
              onTransferPressed: () {
                Navigator.of(context).pop();
                onTransferPressed?.call();
              },
            ),
            SizedBox(height: 12),
          ],
          _ChangeMaxLeverageWidget(account),
          if (account.platformType != PlatformType.equitiTrader)
            _ServerDetailsWidget(account),
          _AccountInformationWidget(account),
          if (tradingEnvironment == TradingEnvironment.demo)
            _ResetBalanceWidget(account),
          _RenameAccountWidget(account),
          if (account.platformType != PlatformType.equitiTrader)
            _ChangeAccountPasswordWidget(account),
        ],
      ),
    ),
    bottomBar: SizedBox(),
  );
}

class _ChangeMaxLeverageWidget extends StatefulWidget {
  const _ChangeMaxLeverageWidget(this.account);

  final TradingAccountModel account;

  @override
  State<_ChangeMaxLeverageWidget> createState() =>
      _ChangeMaxLeverageWidgetState();
}

class _ChangeMaxLeverageWidgetState extends State<_ChangeMaxLeverageWidget> {
  int? selectedLeverage;

  @override
  initState() {
    super.initState();
    selectedLeverage = widget.account.leverage;
  }

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_changeAccountLeverage,
    trailingText:
        // ignore: prefer-number-format
        selectedLeverage != null ? "1:${selectedLeverage!.toString()}" : null,
    onPressed: () {
      final l10n = EquitiLocalization.of(context);
      DuploSheet.showModalSheetV2<bool?>(
        context,
        appBar: DuploAppBar(title: l10n.trader_changeAccountLeverage),
        content: ChangeLeverageScreen(
          accountNumber: widget.account.accountNumber,
          leverage: widget.account.leverage,
        ),
        bottomBar: const SizedBox(),
      ).then((value) {
        Navigator.of(context).pop(value);
      });
    },
  );
}

class _AccountInformationWidget extends StatelessWidget {
  const _AccountInformationWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_accountInformation,

    onPressed: () {
      Navigator.of(context).pop();
      DuploSheet.showModalSheetV2<bool?>(
        context,
        appBar: DuploAppBar(
          title: EquitiLocalization.of(context).trader_accountDetails,
          titleWidget: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DuploText(
                text: EquitiLocalization.of(context).trader_accountDetails,
                style: context.duploTextStyles.textSm,
                color: context.duploTheme.text.textPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
              SizedBox(height: 4),
              DuploText(
                text: account.nickName ?? '',
                style: context.duploTextStyles.textXs,
                color: context.duploTheme.text.textSecondary,
                fontWeight: DuploFontWeight.medium,
              ),
            ],
          ),
        ),
        content: BlocProvider(
          create: (ctx) => diContainer<AccountDetailsBloc>(param1: account),
          child: AccountDetailsTab(),
        ),
      );
    },
  );
}

class _RenameAccountWidget extends StatelessWidget {
  const _RenameAccountWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_renameAccount,
    onPressed: () {
      showRenameAccountBottomSheet<bool?>(context, account).then((value) {
        Navigator.of(context).pop(value);
      });
    },
  );
}

class _ServerDetailsWidget extends StatelessWidget {
  const _ServerDetailsWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: EquitiLocalization.of(context).trader_metaTraderServerDetails,
    trailingText: account.platformType.displayName,
    onPressed: () {
      Navigator.of(context).pop();
      showServerDetailsBottomSheet(context, account);
    },
  );
}

class _AccountSummaryWidget extends StatelessWidget {
  const _AccountSummaryWidget(this.accountModel);

  final TradingAccountModel accountModel;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: accountModel.nickName,
          style: duploTextStyles.textMd,
          color: theme.text.textPrimary,
          fontWeight: DuploFontWeight.semiBold,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        //TODO(sagar): update the value once you get from the designer
        DuploText(
          text: "${accountModel.platformTypeName}",
          style: duploTextStyles.textXs,
          color: theme.text.textTertiary,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}

class _ChangeAccountPasswordWidget extends StatelessWidget {
  const _ChangeAccountPasswordWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return TextChevronWidget(
      title: localization.trader_changeAccountPasswordTitle,
      onPressed: () {
        Navigator.of(context).pop();
        DuploSheet.showModalSheetV2<void>(
          context,
          appBar: DuploAppBar(
            title: localization.trader_changeAccountPasswordTitle,
          ),
          content: ChangeAccountPasswordScreen(
            accountNumber: account.accountNumber,
          ),
        );
      },
    );
  }
}

class _ResetBalanceWidget extends StatelessWidget {
  const _ResetBalanceWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) {
    return TextChevronWidget(
      title: EquitiLocalization.of(context).trader_resetBalance,
      onPressed: () {
        Navigator.of(context).pop();
        DuploSheet.showModalSheet<void>(
          context: context,
          title: "",
          useSafeArea: false,
          hasTopBarLayer: false,
          hideCloseButton: true,
          navBarHeight: 0,
          content:
              (duploSheetContext) => ResetBalanceScreen(
                accountNumber: account.accountNumber,
                accountCurrency: account.homeCurrency,
              ),
        );
      },
    );
  }
}
