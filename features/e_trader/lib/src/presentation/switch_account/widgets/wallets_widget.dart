import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/wallet_details_bottom_sheet.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_account_card_shimmer.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/trading_account_card_widget.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class WalletsWidget extends StatelessWidget {
  const WalletsWidget({required this.footer, super.key});
  final Widget footer;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return BlocBuilder<AccountsBloc, AccountsState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, accountState) {
        return switch (accountState.processState) {
          AccountsLoadingProcessState() => ListView(
            children: const [
              TradingAccountCardShimmer(
                accountCategory: AccountCategory.wallets,
              ),
              TradingAccountCardShimmer(
                accountCategory: AccountCategory.wallets,
              ),
              TradingAccountCardShimmer(
                accountCategory: AccountCategory.wallets,
              ),
            ],
          ),
          AccountsSuccessProcessState() => () {
            final walletsViewModel = accountState.wallets;
            final tradingAccounts = accountState.accounts;

            if (walletsViewModel.isEmpty) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  trader.Assets.images.noWallet.svg(),
                  const SizedBox(height: 16),
                  DuploText(
                    text: EquitiLocalization.of(context).trader_noWalletsFound,
                    style: textStyles.textLg,
                    fontWeight: DuploFontWeight.semiBold,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  DuploText(
                    text:
                        EquitiLocalization.of(
                          context,
                        ).trader_createWalletDescription,
                    style: textStyles.textSm,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              );
            }

            return CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsetsDirectional.only(start: 16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DuploText(
                          text:
                              EquitiLocalization.of(
                                context,
                              ).trader_cfdTradingWallets,
                          style: textStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        Flexible(
                          child: DuploText(
                            text:
                                EquitiLocalization.of(
                                  context,
                                ).trader_switchWalletDescription,
                            style: textStyles.textXs,
                            color: theme.text.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 16)),
                SliverList.separated(
                  itemCount: walletsViewModel.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (listViewContext, index) {
                    final wallet = walletsViewModel[index];

                    return TradingAccountCardWidget(
                      isSelected: wallet.isSelected,
                      accountName: wallet.accountNumber ?? '',
                      equity: wallet.equity ?? 0,
                      profit: wallet.profit ?? 0,
                      balance: wallet.balance ?? 0,
                      currency: wallet.homeCurrency,
                      accountCategory: AccountCategory.wallets,
                      onTap: () {
                        final selectedAccount =
                            tradingAccounts.firstOrNullWhere(
                              (tradingAccount) =>
                                  tradingAccount.accountNumber ==
                                  wallet.accountNumber,
                            )!;

                        walletDetailsBottomSheet(
                          listViewContext,
                          selectedAccount,
                        ).then((_) {
                          listViewContext.read<AccountsBloc>().add(
                            const AccountsEvent.refreshAccounts(),
                          );
                        });
                      },
                    );
                  },
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: footer,
                    ),
                  ),
                ),
              ],
            );
          }(),
          AccountsErrorProcessState() => Center(
            child: DuploText(
              text: EquitiLocalization.of(context).trader_somethingWentWrong,
              style: textStyles.textSm,
            ),
          ),
        };
      },
    );
  }
}
