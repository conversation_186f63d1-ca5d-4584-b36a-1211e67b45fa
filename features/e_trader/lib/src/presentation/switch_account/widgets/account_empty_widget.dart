import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/add_new_account_button.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class AccountEmptyWidget extends StatelessWidget {
  const AccountEmptyWidget({
    required this.selectedTradingEnvironment,
    required this.selectedCategoryIndex,
    required this.onAddAccountTap,
    super.key,
  });

  final TradingEnvironment selectedTradingEnvironment;
  final int selectedCategoryIndex;
  final VoidCallback onAddAccountTap;

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        <PERSON><PERSON>(),
        trader.Assets.images.noAccount.svg(),
        const SizedBox(height: 16),
        DuploText(
          text: EquitiLocalization.of(context).trader_noAccountsMessage(
            EquitiLocalization.of(context).trader_live,
          ),
          style: textStyles.textLg,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        DuploText(
          text: EquitiLocalization.of(context).trader_noLiveAccountsDescription,
          style: textStyles.textSm,
          textAlign: TextAlign.center,
          color: theme.text.textSecondary,
        ),
        Spacer(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: AddNewAccountButton(
            selectedCategoryIndex: selectedCategoryIndex,
            onTap: onAddAccountTap,
          ),
        ),
      ],
    );
  }
}
