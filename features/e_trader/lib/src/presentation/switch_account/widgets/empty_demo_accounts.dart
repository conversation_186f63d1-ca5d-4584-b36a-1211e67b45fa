import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;

import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

class EmptyDemoAccounts extends StatelessWidget {
  const EmptyDemoAccounts({super.key, required this.onTap});
  final void Function() onTap;
  @override
  Widget build(BuildContext context) {
    final l10n = EquitiLocalization.of(context);
    final style = context.duploTextStyles;
    final theme = context.duploTheme;
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 43),
                height: 170,
                width: 170,
                child: DuploLottieView.asset(
                  lightAnimation: trader.Assets.lottie.emptyDemoAnimationLight,
                  darkAnimation: trader.Assets.lottie.emptyDemoAnimationDark,
                ),
              ),
              SizedBox(height: 32),
              DuploText(
                textAlign: TextAlign.start,
                text: l10n.trader_emptyDemoAccountsTitle,
                style: style.textXl,
                color: theme.text.textPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
              SizedBox(height: 8),

              Padding(
                padding: const EdgeInsetsGeometry.fromSTEB(16, 0, 16, 0),
                child: DuploText(
                  textAlign: TextAlign.start,
                  text: l10n.trader_emptyDemoAccountsDescription,
                  style: style.textSm,
                  color: theme.text.textSecondary,
                ),
              ),
              _FeatureList(
                items: [
                  l10n.trader_tradeVirtual,
                  l10n.trader_realMarket,
                  l10n.trader_buildConfidence,
                ],
              ),
            ],
          ),
        ),

        Padding(
          padding: const EdgeInsets.fromLTRB(2.0, 0, 2, 32),
          child: DuploButton.defaultPrimary(
            semanticsIdentifier: 'create_demo_account',
            useFullWidth: true,
            trailingIcon: trader.Assets.images.forwardChevron.keyName,
            title: l10n.trader_emptyDemoAccountsButton,
            onTap: onTap,
          ),
        ),
      ],
    );
  }
}

class _FeatureList extends StatelessWidget {
  const _FeatureList({required this.items});

  final List<String> items;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsGeometry.fromSTEB(16, 24, 0, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            items
                .map(
                  (text) => Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _FeatureItem(text: text),
                  ),
                )
                .toList(),
      ),
    );
  }
}

class _FeatureItem extends StatelessWidget {
  const _FeatureItem({required this.text});

  final String text;

  @override
  Widget build(BuildContext context) {
    final style = context.duploTextStyles;
    final theme = context.duploTheme;

    return Row(
      children: [
        trader.Assets.images.check.svg(height: 16, width: 16),
        const SizedBox(width: 8),
        DuploText(
          text: text,
          style: style.textSm,
          color: theme.text.textSecondary,
        ),
      ],
    );
  }
}
