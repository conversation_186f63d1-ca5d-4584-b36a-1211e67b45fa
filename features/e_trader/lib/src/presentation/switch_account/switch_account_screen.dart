// ignore_for_file: avoid-passing-async-when-sync-expected

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/di/trading_environment_dependencies.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/switch_account_app_bar.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/switch_account_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class SwitchAccountScreen extends StatefulWidget {
  const SwitchAccountScreen({
    super.key,
    this.accountType = AccountType.trading,
    this.blockPop = false,
  });

  final AccountType accountType;
  final bool blockPop;

  @override
  State<SwitchAccountScreen> createState() => _SwitchAccountScreenState();
}

class _SwitchAccountScreenState extends State<SwitchAccountScreen>
    with SingleTickerProviderStateMixin, PerformanceObserverMixin {
  late TabController _tabController;
  int _selectedCategoryIndex = 0;
  late TradingEnvironment _selectedTradingEnvironment = TradingEnvironment.live;
  late Future<void> _dependencyRegistrationFuture;

  @override
  void initState() {
    super.initState();
    _selectedCategoryIndex =
        widget.accountType == AccountType.landingWallet ? 1 : 0;

    // Register dependencies with correct environment
    _dependencyRegistrationFuture = TradingEnvironmentDependencies.register(
      isDemo: _selectedTradingEnvironment == TradingEnvironment.demo,
    );

    _tabController = TabController(length: 2, vsync: this);
    _tabController.animation!.addListener(_onTabAnimationChanged);
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;

    return PopScope(
      canPop: !widget.blockPop,
      onPopInvokedWithResult: (didPop, result) {
        if (widget.blockPop) {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        backgroundColor: theme.background.bgSecondary,
        appBar: SwitchAccountAppBar(
          initialAccountType: _selectedTradingEnvironment,
          onAccountTypeChanged: (selectedAccountType) async {
            await TradingEnvironmentDependencies.register(
              isDemo: selectedAccountType == TradingEnvironment.demo,
            );
            if (mounted) {
              setState(() {
                _selectedTradingEnvironment = selectedAccountType;
                _selectedCategoryIndex = 0;
                _tabController.animateTo(_selectedCategoryIndex);
              });
            }
          },
        ),
        body: FutureBuilder<void>(
          future: _dependencyRegistrationFuture,
          builder: (builderContext, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Text(
                  'Error initializing: ${snapshot.error ?? 'Unknown error'}',
                ),
              );
            }

            return BlocProvider(
              key: ValueKey(_selectedTradingEnvironment),
              create:
                  (_) =>
                      diContainer<AccountsBloc>()..add(AccountsEvent.fetch()),
              child: SwitchAccountContent(
                selectedTradingEnvironment: _selectedTradingEnvironment,
                selectedCategoryIndex: _selectedCategoryIndex,
                onAccountCategoryChanged: (selectedCategory) {
                  setState(() {
                    _selectedCategoryIndex = switch (selectedCategory) {
                      AccountCategory.accounts => 0,
                      AccountCategory.wallets => 1,
                    };
                  });
                  _tabController.animateTo(_selectedCategoryIndex);
                },
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.animation!.removeListener(_onTabAnimationChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabAnimationChanged() {
    final animationValue = _tabController.animation!.value;
    final newIndex = animationValue.round();

    if (newIndex != _selectedCategoryIndex) {
      setState(() {
        _selectedCategoryIndex = newIndex;
      });
    }
  }
}
