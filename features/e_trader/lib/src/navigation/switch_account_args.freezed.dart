// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'switch_account_args.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SwitchAccountArgs {

 AccountType get accountType; bool get blockPop;
/// Create a copy of SwitchAccountArgs
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SwitchAccountArgsCopyWith<SwitchAccountArgs> get copyWith => _$SwitchAccountArgsCopyWithImpl<SwitchAccountArgs>(this as SwitchAccountArgs, _$identity);

  /// Serializes this SwitchAccountArgs to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SwitchAccountArgs&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.blockPop, blockPop) || other.blockPop == blockPop));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accountType,blockPop);

@override
String toString() {
  return 'SwitchAccountArgs(accountType: $accountType, blockPop: $blockPop)';
}


}

/// @nodoc
abstract mixin class $SwitchAccountArgsCopyWith<$Res>  {
  factory $SwitchAccountArgsCopyWith(SwitchAccountArgs value, $Res Function(SwitchAccountArgs) _then) = _$SwitchAccountArgsCopyWithImpl;
@useResult
$Res call({
 AccountType accountType, bool blockPop
});




}
/// @nodoc
class _$SwitchAccountArgsCopyWithImpl<$Res>
    implements $SwitchAccountArgsCopyWith<$Res> {
  _$SwitchAccountArgsCopyWithImpl(this._self, this._then);

  final SwitchAccountArgs _self;
  final $Res Function(SwitchAccountArgs) _then;

/// Create a copy of SwitchAccountArgs
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountType = null,Object? blockPop = null,}) {
  return _then(_self.copyWith(
accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,blockPop: null == blockPop ? _self.blockPop : blockPop // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _SwitchAccountArgs implements SwitchAccountArgs {
  const _SwitchAccountArgs({required this.accountType, this.blockPop = false});
  factory _SwitchAccountArgs.fromJson(Map<String, dynamic> json) => _$SwitchAccountArgsFromJson(json);

@override final  AccountType accountType;
@override@JsonKey() final  bool blockPop;

/// Create a copy of SwitchAccountArgs
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SwitchAccountArgsCopyWith<_SwitchAccountArgs> get copyWith => __$SwitchAccountArgsCopyWithImpl<_SwitchAccountArgs>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SwitchAccountArgsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SwitchAccountArgs&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.blockPop, blockPop) || other.blockPop == blockPop));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accountType,blockPop);

@override
String toString() {
  return 'SwitchAccountArgs(accountType: $accountType, blockPop: $blockPop)';
}


}

/// @nodoc
abstract mixin class _$SwitchAccountArgsCopyWith<$Res> implements $SwitchAccountArgsCopyWith<$Res> {
  factory _$SwitchAccountArgsCopyWith(_SwitchAccountArgs value, $Res Function(_SwitchAccountArgs) _then) = __$SwitchAccountArgsCopyWithImpl;
@override @useResult
$Res call({
 AccountType accountType, bool blockPop
});




}
/// @nodoc
class __$SwitchAccountArgsCopyWithImpl<$Res>
    implements _$SwitchAccountArgsCopyWith<$Res> {
  __$SwitchAccountArgsCopyWithImpl(this._self, this._then);

  final _SwitchAccountArgs _self;
  final $Res Function(_SwitchAccountArgs) _then;

/// Create a copy of SwitchAccountArgs
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountType = null,Object? blockPop = null,}) {
  return _then(_SwitchAccountArgs(
accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,blockPop: null == blockPop ? _self.blockPop : blockPop // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
