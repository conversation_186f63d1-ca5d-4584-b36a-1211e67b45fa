import 'package:domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'switch_account_args.freezed.dart';
part 'switch_account_args.g.dart';

@freezed
abstract class SwitchAccountArgs with _$SwitchAccountArgs {
  const factory SwitchAccountArgs({
    required AccountType accountType,
    @Default(false) bool blockPop,
  }) = _SwitchAccountArgs;

  factory SwitchAccountArgs.fromJson(Map<String, dynamic> json) =>
      _$SwitchAccountArgsFromJson(json);
}
