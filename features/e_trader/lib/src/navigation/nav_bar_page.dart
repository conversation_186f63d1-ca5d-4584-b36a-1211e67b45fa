import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/navigation_bottom_bar.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/src/widgets/framework.dart';

class NavBarPage extends EquitiPage {
  const NavBarPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    return NavigationBottomBar();
  }

  @override
  String get label => EquitiTraderRouteSchema.navBarRoute.label;

  @override
  String get url => EquitiTraderRouteSchema.navBarRoute.url;
}
