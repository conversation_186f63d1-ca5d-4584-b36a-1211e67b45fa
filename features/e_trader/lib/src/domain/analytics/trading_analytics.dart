import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/close_trade_response_model.dart';
import 'package:e_trader/src/data/api/create_order_model.dart';
import 'package:e_trader/src/data/api/create_order_request_model.dart';
import 'package:e_trader/src/data/api/create_trade_model.dart';
import 'package:e_trader/src/data/api/create_trade_request_model.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:equiti_analytics/equiti_analytics.dart';

import 'analytics_metadata_sanitizer.dart';

class TradingAnalytics {
  final AnalyticsService _analyticsService;
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  const TradingAnalytics(
    this._analyticsService,
    this._getSelectedAccountUseCase,
  );

  Map<String, dynamic> generateStaticMetadata() {
    final account = _getSelectedAccountUseCase();
    final metadata = <String, dynamic>{};

    // Only add non-null values and ensure they are valid types
    if (account?.accountNumber != null && account!.accountNumber.isNotEmpty) {
      metadata["accountNumber"] = account.accountNumber;
    }

    return metadata;
  }

  Future<bool> symbolSelected(String symbol) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      'symbol': symbol,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.symbolSelected.eventType,
      eventName: TradeAnalyticsEvent.symbolSelected.eventName,
      metadata: metadata,
    );
  }

  Future<String?> startInteraction(String actionName) async {
    return await _analyticsService.startInteraction(actionName);
  }

  void endInteraction(String? interactionId) {
    if (interactionId == null) return;
    _analyticsService.endInteraction(interactionId);
  }

  Future<bool> placingTrade(
    CreateTradeRequestModel model,
    String currentPrice,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": model.symbolCode,
      "tradeType": model.tradeType.name,
      "volume": model.volume,
      "stopLoss": model.stopLoss,
      "takeProfit": model.takeProfit,
      "userCurrentPrice": currentPrice,
      "interactionId": interactionId ?? "",
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.placingTrade.eventType,
      eventName: TradeAnalyticsEvent.placingTrade.eventName,
      metadata: metadata,
    );
  }

  Future<bool> placingOrder(
    CreateOrderRequestModel model,
    String currentPrice,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": model.symbolName,
      "tradeType": model.tradeType.name,
      "volume": model.volume,
      "stopLoss": model.stopLoss,
      "takeProfit": model.takeProfit,
      "price": model.price,
      "userCurrentPrice": currentPrice,
      "interactionId": interactionId ?? "",
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.placingOrder.eventType,
      eventName: TradeAnalyticsEvent.placingOrder.eventName,
      metadata: metadata,
    );
  }

  Future<bool> tradePlacedResult(
    CreateTradeModel? model,
    String symbol,
    String currentPrice,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      if (model != null) ...{
        "isSuccess": model.isSuccess,
        "positionId": model.positionId,
        "openPrice": model.openPrice,
        "errorMessage": model.errorMessage,
        "symbol": symbol,
        "userCurrentPrice": currentPrice,
        "interactionId": interactionId ?? "",
      },
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.tradePlacedResult.eventType,
      eventName: TradeAnalyticsEvent.tradePlacedResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> orderPlacedResult(
    CreateOrderModel? model,
    String symbol,
    String currentPrice,
    String? interactionId,
    String? errorMessage,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": model?.isOk ?? false,
      "errorMessage": errorMessage ?? model?.errorMessage,
      "presentInCache": model?.presentInCache,
      "serverCode": model?.serverCode,
      "accountGroup": model?.accountGroup,
      "leverage": model?.leverage,
      "symbol": symbol,
      "userCurrentPrice": currentPrice,
      "interactionId": interactionId ?? "",
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.orderPlacedResult.eventType,
      eventName: TradeAnalyticsEvent.orderPlacedResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> startCloseTrade(
    CloseTradeRequestModel model,
    CloseTradeType type,
    String symbol,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "tradeType": type.name,
      "numberOfPositions": model.positions.length,
      "positions": model.positions.map((e) => e.toJson()).toList().toString(),
      "symbol": symbol,
      "interactionId": interactionId ?? "",
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startCloseTrade.eventType,
      eventName: TradeAnalyticsEvent.startCloseTrade.eventName,
      metadata: metadata,
    );
  }

  Future<bool> closeTradeResult(
    CloseTradeResponseModel? model,
    String type,
    String symbol,
    String? interactionId,
    String? errorMessage,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": model?.success ?? false,
      "symbol": symbol,
      "tradeType": type,
      if (model != null) ...{
        "resourceId": model.resourceId,
        "status": model.status,
      },
      "interactionId": interactionId,
      "errorMessage": errorMessage,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.closeTradeResult.eventType,
      eventName: TradeAnalyticsEvent.closeTradeResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> startDeleteAlert(
    String symbol,
    String alertId,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "alertId": alertId,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startDeleteAlert.eventType,
      eventName: TradeAnalyticsEvent.startDeleteAlert.eventName,
      metadata: metadata,
    );
  }

  Future<bool> deleteAlertResult(
    bool isSuccess,
    String symbol,
    String alertId,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": isSuccess,
      "symbol": symbol,
      "alertId": alertId,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.deleteAlertResult.eventType,
      eventName: TradeAnalyticsEvent.deleteAlertResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> startDeleteOrder(
    String symbol,
    String orderId,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "orderId": orderId,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startDeleteOrder.eventType,
      eventName: TradeAnalyticsEvent.startDeleteOrder.eventName,
      metadata: metadata,
    );
  }

  Future<bool> deleteOrderResult(
    bool isSuccess,
    String symbol,
    String orderId,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": isSuccess,
      "symbol": symbol,
      "orderId": orderId,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.deleteOrderResult.eventType,
      eventName: TradeAnalyticsEvent.deleteOrderResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> positionsAdded(List<Map<String, dynamic>>? positions) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "numberOfPositions": positions?.length ?? 0,
      "positions": positions,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.positionAdded.eventType,
      eventName: TradeAnalyticsEvent.positionAdded.eventName,
      metadata: metadata,
    );
  }

  Future<bool> positionsDeleted(List<Map<String, dynamic>>? positions) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "numberOfPositions": positions?.length ?? 0,
      "positions": positions,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.positionDeleted.eventType,
      eventName: TradeAnalyticsEvent.positionDeleted.eventName,
      metadata: metadata,
    );
  }

  Future<bool> ordersAdded(List<Map<String, dynamic>>? orders) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "numberOfOrders": orders?.length ?? 0,
      "orders": orders,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.orderAdded.eventType,
      eventName: TradeAnalyticsEvent.orderAdded.eventName,
      metadata: metadata,
    );
  }

  Future<bool> ordersDeleted(List<Map<String, dynamic>>? orders) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "numberOfOrders": orders?.length ?? 0,
      "orders": orders,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.orderDeleted.eventType,
      eventName: TradeAnalyticsEvent.orderDeleted.eventName,
      metadata: metadata,
    );
  }

  Future<bool> alertsAdded(List<Map<String, dynamic>>? alerts) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "numberOfAlerts": alerts?.length ?? 0,
      "alerts": alerts,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.alertAdded.eventType,
      eventName: TradeAnalyticsEvent.alertAdded.eventName,
      metadata: metadata,
    );
  }

  Future<bool> alertsDeleted(List<Map<String, dynamic>>? alerts) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "numberOfAlerts": alerts?.length ?? 0,
      "alerts": alerts,
    });

    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.alertDeleted.eventType,
      eventName: TradeAnalyticsEvent.alertDeleted.eventName,
      metadata: metadata,
    );
  }

  Future<bool> startModifyTrade(
    String symbol,
    String positionId,
    String tp,
    String sl,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "positionId": positionId,
      "takeProfit": tp,
      "stopLoss": sl,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startModifyTrade.eventType,
      eventName: TradeAnalyticsEvent.startModifyTrade.eventName,
      metadata: metadata,
    );
  }

  Future<bool> modifyTradeResult(
    bool isSuccess,
    String symbol,
    String positionId,
    String tp,
    String sl,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": isSuccess,
      "symbol": symbol,
      "positionId": positionId,
      "takeProfit": tp,
      "stopLoss": sl,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.modifyTradeResult.eventType,
      eventName: TradeAnalyticsEvent.modifyTradeResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> startModifyOrder(
    String symbol,
    String orderId,
    String orderPrice,
    String tp,
    String sl,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "orderPrice": orderPrice,
      "symbol": symbol,
      "orderId": orderId,
      "takeProfit": tp,
      "stopLoss": sl,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startModifyOrder.eventType,
      eventName: TradeAnalyticsEvent.startModifyOrder.eventName,
      metadata: metadata,
    );
  }

  Future<bool> modifyOrderResult(
    bool isSuccess,
    String symbol,
    String orderId,
    String orderPrice,
    String tp,
    String sl,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": isSuccess,
      "symbol": symbol,
      "orderId": orderId,
      "orderPrice": orderPrice,
      "takeProfit": tp,
      "stopLoss": sl,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.modifyOrderResult.eventType,
      eventName: TradeAnalyticsEvent.modifyOrderResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> startModifyAlert(
    String symbol,
    String alertId,
    String price,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "alertId": alertId,
      "triggerPrice": price,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startModifyAlert.eventType,
      eventName: TradeAnalyticsEvent.startModifyAlert.eventName,
      metadata: metadata,
    );
  }

  Future<bool> modifyAlertResult(
    bool isSuccess,
    String symbol,
    String alertId,
    String price,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": isSuccess,
      "symbol": symbol,
      "alertId": alertId,
      "triggerPrice": price,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.modifyAlertResult.eventType,
      eventName: TradeAnalyticsEvent.modifyAlertResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> loadPortfolioPositionDetails(String? interactionId) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.loadPortfolioPositionDetails.eventType,
      eventName: TradeAnalyticsEvent.loadPortfolioPositionDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> loadPortfolioTradeDetails(String? interactionId) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.loadPortfolioTradeDetails.eventType,
      eventName: TradeAnalyticsEvent.loadPortfolioTradeDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> loadPortfolioOrderDetails(String? interactionId) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.loadPortfolioOrderDetails.eventType,
      eventName: TradeAnalyticsEvent.loadPortfolioOrderDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> loadPortfolioAlertDetails(String? interactionId) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.loadPortfolioAlertDetails.eventType,
      eventName: TradeAnalyticsEvent.loadPortfolioAlertDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> doneLoadingPositionDetails(
    String symbol,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.doneLoadingPositionDetails.eventType,
      eventName: TradeAnalyticsEvent.doneLoadingPositionDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> doneLoadingTradeDetails(
    String symbol,
    String positionId,
    String openPrice,
    String tp,
    String sl,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "positionId": positionId,
      "openPrice": openPrice,
      "takeProfit": tp,
      "stopLoss": sl,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.doneLoadingTradeDetails.eventType,
      eventName: TradeAnalyticsEvent.doneLoadingTradeDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> doneLoadingOrderDetails(
    String symbol,
    String orderId,
    String orderPrice,
    String tp,
    String sl,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "orderId": orderId,
      "orderPrice": orderPrice,
      "takeProfit": tp,
      "stopLoss": sl,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.doneLoadingOrderDetails.eventType,
      eventName: TradeAnalyticsEvent.doneLoadingOrderDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> doneLoadingAlertDetails(
    String symbol,
    String alertId,
    String price,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "alertId": alertId,
      "triggerPrice": price,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.doneLoadingAlertDetails.eventType,
      eventName: TradeAnalyticsEvent.doneLoadingAlertDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> failedLoadingPositionDetails(
    String error,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "error": error,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.failedLoadingPositionDetails.eventType,
      eventName: TradeAnalyticsEvent.failedLoadingPositionDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> failedLoadingTradeDetails(
    String error,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "error": error,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.failedLoadingTradeDetails.eventType,
      eventName: TradeAnalyticsEvent.failedLoadingTradeDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> failedLoadingOrderDetails(
    String error,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "error": error,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.failedLoadingOrderDetails.eventType,
      eventName: TradeAnalyticsEvent.failedLoadingOrderDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> failedLoadingAlertDetails(
    String error,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "error": error,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.failedLoadingAlertDetails.eventType,
      eventName: TradeAnalyticsEvent.failedLoadingAlertDetails.eventName,
      metadata: metadata,
    );
  }

  Future<bool> placingAlert(
    String symbol,
    String price,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "symbol": symbol,
      "triggerPrice": price,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.placingAlert.eventType,
      eventName: TradeAnalyticsEvent.placingAlert.eventName,
      metadata: metadata,
    );
  }

  Future<bool> alertPlacedResult(
    bool isSuccess,
    String symbol,
    String price,
    String? interactionId,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "isSuccess": isSuccess,
      "symbol": symbol,
      "triggerPrice": price,
      "interactionId": interactionId ?? "",
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.alertPlacedResult.eventType,
      eventName: TradeAnalyticsEvent.alertPlacedResult.eventName,
      metadata: metadata,
    );
  }

  Future<bool> searchSymbol(
    String searchText,
    bool isSuccess,
    int resultsFound,
  ) async {
    final metadata = AnalyticsMetadataSanitizer.sanitize({
      ...generateStaticMetadata(),
      "searchText": searchText,
      "isSuccess": isSuccess,
      "resultsFound": resultsFound,
    });
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.searchSymbol.eventType,
      eventName: TradeAnalyticsEvent.searchSymbol.eventName,
      metadata: metadata,
    );
  }
}

enum TradeOrderTabType { tradeTab, orderTab }

enum CloseTradeType { partialClose, close, quickClose }
