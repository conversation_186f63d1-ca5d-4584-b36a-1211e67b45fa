// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_detail_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
ProductDetailInfo _$ProductDetailInfoFromJson(
  Map<String, dynamic> json
) {
    return _ProductDetailsInfo.fromJson(
      json
    );
}

/// @nodoc
mixin _$ProductDetailInfo {

 bool get isWatchlist; String get productId; String? get productCategory; String? get productCategoryId; String? get productName; String? get tickerName; String? get platformName; String? get description; String? get assetClass; String? get assetType; String? get baseCurrency; String? get contractSizeUnit; double? get contractSize; double? get spread; double? get marginRate; double? get maxLot; double? get minLot; double? get lotsSteps; String? get commissionDescription; String? get expiryDate; double? get swapLong; double? get swapShort;
/// Create a copy of ProductDetailInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductDetailInfoCopyWith<ProductDetailInfo> get copyWith => _$ProductDetailInfoCopyWithImpl<ProductDetailInfo>(this as ProductDetailInfo, _$identity);

  /// Serializes this ProductDetailInfo to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductDetailInfo&&(identical(other.isWatchlist, isWatchlist) || other.isWatchlist == isWatchlist)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productCategory, productCategory) || other.productCategory == productCategory)&&(identical(other.productCategoryId, productCategoryId) || other.productCategoryId == productCategoryId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.tickerName, tickerName) || other.tickerName == tickerName)&&(identical(other.platformName, platformName) || other.platformName == platformName)&&(identical(other.description, description) || other.description == description)&&(identical(other.assetClass, assetClass) || other.assetClass == assetClass)&&(identical(other.assetType, assetType) || other.assetType == assetType)&&(identical(other.baseCurrency, baseCurrency) || other.baseCurrency == baseCurrency)&&(identical(other.contractSizeUnit, contractSizeUnit) || other.contractSizeUnit == contractSizeUnit)&&(identical(other.contractSize, contractSize) || other.contractSize == contractSize)&&(identical(other.spread, spread) || other.spread == spread)&&(identical(other.marginRate, marginRate) || other.marginRate == marginRate)&&(identical(other.maxLot, maxLot) || other.maxLot == maxLot)&&(identical(other.minLot, minLot) || other.minLot == minLot)&&(identical(other.lotsSteps, lotsSteps) || other.lotsSteps == lotsSteps)&&(identical(other.commissionDescription, commissionDescription) || other.commissionDescription == commissionDescription)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.swapLong, swapLong) || other.swapLong == swapLong)&&(identical(other.swapShort, swapShort) || other.swapShort == swapShort));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,isWatchlist,productId,productCategory,productCategoryId,productName,tickerName,platformName,description,assetClass,assetType,baseCurrency,contractSizeUnit,contractSize,spread,marginRate,maxLot,minLot,lotsSteps,commissionDescription,expiryDate,swapLong,swapShort]);

@override
String toString() {
  return 'ProductDetailInfo(isWatchlist: $isWatchlist, productId: $productId, productCategory: $productCategory, productCategoryId: $productCategoryId, productName: $productName, tickerName: $tickerName, platformName: $platformName, description: $description, assetClass: $assetClass, assetType: $assetType, baseCurrency: $baseCurrency, contractSizeUnit: $contractSizeUnit, contractSize: $contractSize, spread: $spread, marginRate: $marginRate, maxLot: $maxLot, minLot: $minLot, lotsSteps: $lotsSteps, commissionDescription: $commissionDescription, expiryDate: $expiryDate, swapLong: $swapLong, swapShort: $swapShort)';
}


}

/// @nodoc
abstract mixin class $ProductDetailInfoCopyWith<$Res>  {
  factory $ProductDetailInfoCopyWith(ProductDetailInfo value, $Res Function(ProductDetailInfo) _then) = _$ProductDetailInfoCopyWithImpl;
@useResult
$Res call({
 bool isWatchlist, String productId, String? productCategory, String? productCategoryId, String? productName, String? tickerName, String? platformName, String? description, String? assetClass, String? assetType, String? baseCurrency, String? contractSizeUnit, double? contractSize, double? spread, double? marginRate, double? maxLot, double? minLot, double? lotsSteps, String? commissionDescription, String? expiryDate, double? swapLong, double? swapShort
});




}
/// @nodoc
class _$ProductDetailInfoCopyWithImpl<$Res>
    implements $ProductDetailInfoCopyWith<$Res> {
  _$ProductDetailInfoCopyWithImpl(this._self, this._then);

  final ProductDetailInfo _self;
  final $Res Function(ProductDetailInfo) _then;

/// Create a copy of ProductDetailInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isWatchlist = null,Object? productId = null,Object? productCategory = freezed,Object? productCategoryId = freezed,Object? productName = freezed,Object? tickerName = freezed,Object? platformName = freezed,Object? description = freezed,Object? assetClass = freezed,Object? assetType = freezed,Object? baseCurrency = freezed,Object? contractSizeUnit = freezed,Object? contractSize = freezed,Object? spread = freezed,Object? marginRate = freezed,Object? maxLot = freezed,Object? minLot = freezed,Object? lotsSteps = freezed,Object? commissionDescription = freezed,Object? expiryDate = freezed,Object? swapLong = freezed,Object? swapShort = freezed,}) {
  return _then(_self.copyWith(
isWatchlist: null == isWatchlist ? _self.isWatchlist : isWatchlist // ignore: cast_nullable_to_non_nullable
as bool,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productCategory: freezed == productCategory ? _self.productCategory : productCategory // ignore: cast_nullable_to_non_nullable
as String?,productCategoryId: freezed == productCategoryId ? _self.productCategoryId : productCategoryId // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,tickerName: freezed == tickerName ? _self.tickerName : tickerName // ignore: cast_nullable_to_non_nullable
as String?,platformName: freezed == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,assetClass: freezed == assetClass ? _self.assetClass : assetClass // ignore: cast_nullable_to_non_nullable
as String?,assetType: freezed == assetType ? _self.assetType : assetType // ignore: cast_nullable_to_non_nullable
as String?,baseCurrency: freezed == baseCurrency ? _self.baseCurrency : baseCurrency // ignore: cast_nullable_to_non_nullable
as String?,contractSizeUnit: freezed == contractSizeUnit ? _self.contractSizeUnit : contractSizeUnit // ignore: cast_nullable_to_non_nullable
as String?,contractSize: freezed == contractSize ? _self.contractSize : contractSize // ignore: cast_nullable_to_non_nullable
as double?,spread: freezed == spread ? _self.spread : spread // ignore: cast_nullable_to_non_nullable
as double?,marginRate: freezed == marginRate ? _self.marginRate : marginRate // ignore: cast_nullable_to_non_nullable
as double?,maxLot: freezed == maxLot ? _self.maxLot : maxLot // ignore: cast_nullable_to_non_nullable
as double?,minLot: freezed == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double?,lotsSteps: freezed == lotsSteps ? _self.lotsSteps : lotsSteps // ignore: cast_nullable_to_non_nullable
as double?,commissionDescription: freezed == commissionDescription ? _self.commissionDescription : commissionDescription // ignore: cast_nullable_to_non_nullable
as String?,expiryDate: freezed == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as String?,swapLong: freezed == swapLong ? _self.swapLong : swapLong // ignore: cast_nullable_to_non_nullable
as double?,swapShort: freezed == swapShort ? _self.swapShort : swapShort // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductDetailsInfo implements ProductDetailInfo {
  const _ProductDetailsInfo({this.isWatchlist = false, required this.productId, this.productCategory, this.productCategoryId, this.productName, this.tickerName, this.platformName, this.description, this.assetClass, this.assetType, this.baseCurrency, this.contractSizeUnit, this.contractSize, this.spread, this.marginRate, this.maxLot, this.minLot, this.lotsSteps, this.commissionDescription, this.expiryDate, this.swapLong, this.swapShort});
  factory _ProductDetailsInfo.fromJson(Map<String, dynamic> json) => _$ProductDetailsInfoFromJson(json);

@override@JsonKey() final  bool isWatchlist;
@override final  String productId;
@override final  String? productCategory;
@override final  String? productCategoryId;
@override final  String? productName;
@override final  String? tickerName;
@override final  String? platformName;
@override final  String? description;
@override final  String? assetClass;
@override final  String? assetType;
@override final  String? baseCurrency;
@override final  String? contractSizeUnit;
@override final  double? contractSize;
@override final  double? spread;
@override final  double? marginRate;
@override final  double? maxLot;
@override final  double? minLot;
@override final  double? lotsSteps;
@override final  String? commissionDescription;
@override final  String? expiryDate;
@override final  double? swapLong;
@override final  double? swapShort;

/// Create a copy of ProductDetailInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductDetailsInfoCopyWith<_ProductDetailsInfo> get copyWith => __$ProductDetailsInfoCopyWithImpl<_ProductDetailsInfo>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductDetailsInfoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductDetailsInfo&&(identical(other.isWatchlist, isWatchlist) || other.isWatchlist == isWatchlist)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.productCategory, productCategory) || other.productCategory == productCategory)&&(identical(other.productCategoryId, productCategoryId) || other.productCategoryId == productCategoryId)&&(identical(other.productName, productName) || other.productName == productName)&&(identical(other.tickerName, tickerName) || other.tickerName == tickerName)&&(identical(other.platformName, platformName) || other.platformName == platformName)&&(identical(other.description, description) || other.description == description)&&(identical(other.assetClass, assetClass) || other.assetClass == assetClass)&&(identical(other.assetType, assetType) || other.assetType == assetType)&&(identical(other.baseCurrency, baseCurrency) || other.baseCurrency == baseCurrency)&&(identical(other.contractSizeUnit, contractSizeUnit) || other.contractSizeUnit == contractSizeUnit)&&(identical(other.contractSize, contractSize) || other.contractSize == contractSize)&&(identical(other.spread, spread) || other.spread == spread)&&(identical(other.marginRate, marginRate) || other.marginRate == marginRate)&&(identical(other.maxLot, maxLot) || other.maxLot == maxLot)&&(identical(other.minLot, minLot) || other.minLot == minLot)&&(identical(other.lotsSteps, lotsSteps) || other.lotsSteps == lotsSteps)&&(identical(other.commissionDescription, commissionDescription) || other.commissionDescription == commissionDescription)&&(identical(other.expiryDate, expiryDate) || other.expiryDate == expiryDate)&&(identical(other.swapLong, swapLong) || other.swapLong == swapLong)&&(identical(other.swapShort, swapShort) || other.swapShort == swapShort));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,isWatchlist,productId,productCategory,productCategoryId,productName,tickerName,platformName,description,assetClass,assetType,baseCurrency,contractSizeUnit,contractSize,spread,marginRate,maxLot,minLot,lotsSteps,commissionDescription,expiryDate,swapLong,swapShort]);

@override
String toString() {
  return 'ProductDetailInfo(isWatchlist: $isWatchlist, productId: $productId, productCategory: $productCategory, productCategoryId: $productCategoryId, productName: $productName, tickerName: $tickerName, platformName: $platformName, description: $description, assetClass: $assetClass, assetType: $assetType, baseCurrency: $baseCurrency, contractSizeUnit: $contractSizeUnit, contractSize: $contractSize, spread: $spread, marginRate: $marginRate, maxLot: $maxLot, minLot: $minLot, lotsSteps: $lotsSteps, commissionDescription: $commissionDescription, expiryDate: $expiryDate, swapLong: $swapLong, swapShort: $swapShort)';
}


}

/// @nodoc
abstract mixin class _$ProductDetailsInfoCopyWith<$Res> implements $ProductDetailInfoCopyWith<$Res> {
  factory _$ProductDetailsInfoCopyWith(_ProductDetailsInfo value, $Res Function(_ProductDetailsInfo) _then) = __$ProductDetailsInfoCopyWithImpl;
@override @useResult
$Res call({
 bool isWatchlist, String productId, String? productCategory, String? productCategoryId, String? productName, String? tickerName, String? platformName, String? description, String? assetClass, String? assetType, String? baseCurrency, String? contractSizeUnit, double? contractSize, double? spread, double? marginRate, double? maxLot, double? minLot, double? lotsSteps, String? commissionDescription, String? expiryDate, double? swapLong, double? swapShort
});




}
/// @nodoc
class __$ProductDetailsInfoCopyWithImpl<$Res>
    implements _$ProductDetailsInfoCopyWith<$Res> {
  __$ProductDetailsInfoCopyWithImpl(this._self, this._then);

  final _ProductDetailsInfo _self;
  final $Res Function(_ProductDetailsInfo) _then;

/// Create a copy of ProductDetailInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isWatchlist = null,Object? productId = null,Object? productCategory = freezed,Object? productCategoryId = freezed,Object? productName = freezed,Object? tickerName = freezed,Object? platformName = freezed,Object? description = freezed,Object? assetClass = freezed,Object? assetType = freezed,Object? baseCurrency = freezed,Object? contractSizeUnit = freezed,Object? contractSize = freezed,Object? spread = freezed,Object? marginRate = freezed,Object? maxLot = freezed,Object? minLot = freezed,Object? lotsSteps = freezed,Object? commissionDescription = freezed,Object? expiryDate = freezed,Object? swapLong = freezed,Object? swapShort = freezed,}) {
  return _then(_ProductDetailsInfo(
isWatchlist: null == isWatchlist ? _self.isWatchlist : isWatchlist // ignore: cast_nullable_to_non_nullable
as bool,productId: null == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String,productCategory: freezed == productCategory ? _self.productCategory : productCategory // ignore: cast_nullable_to_non_nullable
as String?,productCategoryId: freezed == productCategoryId ? _self.productCategoryId : productCategoryId // ignore: cast_nullable_to_non_nullable
as String?,productName: freezed == productName ? _self.productName : productName // ignore: cast_nullable_to_non_nullable
as String?,tickerName: freezed == tickerName ? _self.tickerName : tickerName // ignore: cast_nullable_to_non_nullable
as String?,platformName: freezed == platformName ? _self.platformName : platformName // ignore: cast_nullable_to_non_nullable
as String?,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,assetClass: freezed == assetClass ? _self.assetClass : assetClass // ignore: cast_nullable_to_non_nullable
as String?,assetType: freezed == assetType ? _self.assetType : assetType // ignore: cast_nullable_to_non_nullable
as String?,baseCurrency: freezed == baseCurrency ? _self.baseCurrency : baseCurrency // ignore: cast_nullable_to_non_nullable
as String?,contractSizeUnit: freezed == contractSizeUnit ? _self.contractSizeUnit : contractSizeUnit // ignore: cast_nullable_to_non_nullable
as String?,contractSize: freezed == contractSize ? _self.contractSize : contractSize // ignore: cast_nullable_to_non_nullable
as double?,spread: freezed == spread ? _self.spread : spread // ignore: cast_nullable_to_non_nullable
as double?,marginRate: freezed == marginRate ? _self.marginRate : marginRate // ignore: cast_nullable_to_non_nullable
as double?,maxLot: freezed == maxLot ? _self.maxLot : maxLot // ignore: cast_nullable_to_non_nullable
as double?,minLot: freezed == minLot ? _self.minLot : minLot // ignore: cast_nullable_to_non_nullable
as double?,lotsSteps: freezed == lotsSteps ? _self.lotsSteps : lotsSteps // ignore: cast_nullable_to_non_nullable
as double?,commissionDescription: freezed == commissionDescription ? _self.commissionDescription : commissionDescription // ignore: cast_nullable_to_non_nullable
as String?,expiryDate: freezed == expiryDate ? _self.expiryDate : expiryDate // ignore: cast_nullable_to_non_nullable
as String?,swapLong: freezed == swapLong ? _self.swapLong : swapLong // ignore: cast_nullable_to_non_nullable
as double?,swapShort: freezed == swapShort ? _self.swapShort : swapShort // ignore: cast_nullable_to_non_nullable
as double?,
  ));
}


}

// dart format on
