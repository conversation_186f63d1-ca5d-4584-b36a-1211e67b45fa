// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_detail_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductDetailsInfo _$ProductDetailsInfoFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_ProductDetailsInfo', json, ($checkedConvert) {
  final val = _ProductDetailsInfo(
    isWatchlist: $checkedConvert('isWatchlist', (v) => v as bool? ?? false),
    productId: $checkedConvert('productId', (v) => v as String),
    productCategory: $checkedConvert('productCategory', (v) => v as String?),
    productCategoryId: $checkedConvert(
      'productCategoryId',
      (v) => v as String?,
    ),
    productName: $checkedConvert('productName', (v) => v as String?),
    tickerName: $checkedConvert('tickerName', (v) => v as String?),
    platformName: $checkedConvert('platformName', (v) => v as String?),
    description: $checkedConvert('description', (v) => v as String?),
    assetClass: $checkedConvert('assetClass', (v) => v as String?),
    assetType: $checkedConvert('assetType', (v) => v as String?),
    baseCurrency: $checkedConvert('baseCurrency', (v) => v as String?),
    contractSizeUnit: $checkedConvert('contractSizeUnit', (v) => v as String?),
    contractSize: $checkedConvert(
      'contractSize',
      (v) => (v as num?)?.toDouble(),
    ),
    spread: $checkedConvert('spread', (v) => (v as num?)?.toDouble()),
    marginRate: $checkedConvert('marginRate', (v) => (v as num?)?.toDouble()),
    maxLot: $checkedConvert('maxLot', (v) => (v as num?)?.toDouble()),
    minLot: $checkedConvert('minLot', (v) => (v as num?)?.toDouble()),
    lotsSteps: $checkedConvert('lotsSteps', (v) => (v as num?)?.toDouble()),
    commissionDescription: $checkedConvert(
      'commissionDescription',
      (v) => v as String?,
    ),
    expiryDate: $checkedConvert('expiryDate', (v) => v as String?),
    swapLong: $checkedConvert('swapLong', (v) => (v as num?)?.toDouble()),
    swapShort: $checkedConvert('swapShort', (v) => (v as num?)?.toDouble()),
  );
  return val;
});

Map<String, dynamic> _$ProductDetailsInfoToJson(
  _ProductDetailsInfo instance,
) => <String, dynamic>{
  'isWatchlist': instance.isWatchlist,
  'productId': instance.productId,
  if (instance.productCategory case final value?) 'productCategory': value,
  if (instance.productCategoryId case final value?) 'productCategoryId': value,
  if (instance.productName case final value?) 'productName': value,
  if (instance.tickerName case final value?) 'tickerName': value,
  if (instance.platformName case final value?) 'platformName': value,
  if (instance.description case final value?) 'description': value,
  if (instance.assetClass case final value?) 'assetClass': value,
  if (instance.assetType case final value?) 'assetType': value,
  if (instance.baseCurrency case final value?) 'baseCurrency': value,
  if (instance.contractSizeUnit case final value?) 'contractSizeUnit': value,
  if (instance.contractSize case final value?) 'contractSize': value,
  if (instance.spread case final value?) 'spread': value,
  if (instance.marginRate case final value?) 'marginRate': value,
  if (instance.maxLot case final value?) 'maxLot': value,
  if (instance.minLot case final value?) 'minLot': value,
  if (instance.lotsSteps case final value?) 'lotsSteps': value,
  if (instance.commissionDescription case final value?)
    'commissionDescription': value,
  if (instance.expiryDate case final value?) 'expiryDate': value,
  if (instance.swapLong case final value?) 'swapLong': value,
  if (instance.swapShort case final value?) 'swapShort': value,
};
