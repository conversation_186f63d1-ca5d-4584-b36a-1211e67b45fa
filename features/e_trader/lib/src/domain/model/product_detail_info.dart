import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_detail_info.freezed.dart';
part 'product_detail_info.g.dart'; // Needed for JSON serialization

@Freezed(fromJson: true, toJson: true)
abstract class ProductDetailInfo with _$ProductDetailInfo {
  const factory ProductDetailInfo({
    @Default(false) bool isWatchlist,
    required String productId,
    String? productCategory,
    String? productCategoryId,
    String? productName,
    String? tickerName,
    String? platformName,
    String? description,
    String? assetClass,
    String? assetType,
    String? baseCurrency,
    String? contractSizeUnit,
    double? contractSize,
    double? spread,
    double? marginRate,
    double? maxLot,
    double? minLot,
    double? lotsSteps,
    String? commissionDescription,
    String? expiryDate,
    double? swapLong,
    double? swapShort,
  }) = _ProductDetailsInfo;

  factory ProductDetailInfo.fromJson(Map<String, dynamic> json) {
    final modifiedJson = Map<String, dynamic>.from(json);
    final expiryDate = modifiedJson['expiryDate'] as String?;
    if (expiryDate != null && expiryDate.startsWith('0001-01-01')) {
      modifiedJson['expiryDate'] = null;
    }
    return _$ProductDetailInfoFromJson(modifiedJson);
  }
}
