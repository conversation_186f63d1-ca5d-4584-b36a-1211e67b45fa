// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsChartGen {
  const $AssetsChartGen();

  /// Directory path: assets/chart/charting_library
  $AssetsChartChartingLibraryGen get chartingLibrary =>
      const $AssetsChartChartingLibraryGen();

  /// File path: assets/chart/index.html
  String get index => 'packages/e_trader/assets/chart/index.html';

  /// Directory path: assets/chart/src
  $AssetsChartSrcGen get src => const $AssetsChartSrcGen();

  /// List of all assets
  List<String> get values => [index];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/Bug.svg
  SvgGenImage get bug => const SvgGenImage.vec('assets/images/Bug.svg');

  /// File path: assets/images/account.svg
  SvgGenImage get account => const SvgGenImage.vec('assets/images/account.svg');

  /// File path: assets/images/account_Margin_level_segments.svg
  SvgGenImage get accountMarginLevelSegments =>
      const SvgGenImage.vec('assets/images/account_Margin_level_segments.svg');

  /// File path: assets/images/alert_triangle.svg
  SvgGenImage get alertTriangle =>
      const SvgGenImage.vec('assets/images/alert_triangle.svg');

  /// File path: assets/images/arrow.svg
  SvgGenImage get arrow => const SvgGenImage.vec('assets/images/arrow.svg');

  /// File path: assets/images/arrow_left.svg
  SvgGenImage get arrowLeft =>
      const SvgGenImage.vec('assets/images/arrow_left.svg');

  /// File path: assets/images/back_button.svg
  SvgGenImage get backButton =>
      const SvgGenImage.vec('assets/images/back_button.svg');

  /// File path: assets/images/bank_ic.svg
  SvgGenImage get bankIc => const SvgGenImage.vec('assets/images/bank_ic.svg');

  /// File path: assets/images/bar-chart.svg
  SvgGenImage get barChart =>
      const SvgGenImage.vec('assets/images/bar-chart.svg');

  /// File path: assets/images/buy_complete.svg
  SvgGenImage get buyComplete =>
      const SvgGenImage.vec('assets/images/buy_complete.svg');

  /// File path: assets/images/buy_order.svg
  SvgGenImage get buyOrder =>
      const SvgGenImage.vec('assets/images/buy_order.svg');

  /// File path: assets/images/calendar-add.svg
  SvgGenImage get calendarAdd =>
      const SvgGenImage.vec('assets/images/calendar-add.svg');

  /// File path: assets/images/calendar.svg
  SvgGenImage get calendar =>
      const SvgGenImage.vec('assets/images/calendar.svg');

  /// File path: assets/images/chart-error.svg
  SvgGenImage get chartError =>
      const SvgGenImage.vec('assets/images/chart-error.svg');

  /// File path: assets/images/check-circle.svg
  SvgGenImage get checkCircle =>
      const SvgGenImage.vec('assets/images/check-circle.svg');

  /// File path: assets/images/check.svg
  SvgGenImage get check => const SvgGenImage.vec('assets/images/check.svg');

  /// File path: assets/images/chevron-down.svg
  SvgGenImage get chevronDownSvg =>
      const SvgGenImage.vec('assets/images/chevron-down.svg');

  /// File path: assets/images/chevron_down.svg
  SvgGenImage get chevronDownSvg_ =>
      const SvgGenImage.vec('assets/images/chevron_down.svg');

  /// File path: assets/images/chevron.svg
  SvgGenImage get chevron => const SvgGenImage.vec('assets/images/chevron.svg');

  /// File path: assets/images/chevron_right.svg
  SvgGenImage get chevronRight =>
      const SvgGenImage.vec('assets/images/chevron_right.svg');

  /// File path: assets/images/clock-fast-forward.svg
  SvgGenImage get clockFastForward =>
      const SvgGenImage.vec('assets/images/clock-fast-forward.svg');

  /// File path: assets/images/close.svg
  SvgGenImage get close => const SvgGenImage.vec('assets/images/close.svg');

  /// File path: assets/images/copy.svg
  SvgGenImage get copy => const SvgGenImage.vec('assets/images/copy.svg');

  /// File path: assets/images/credit_card_icon_light.svg
  SvgGenImage get creditCardIconLight =>
      const SvgGenImage.vec('assets/images/credit_card_icon_light.svg');

  /// File path: assets/images/delete_keyboard_key.svg
  SvgGenImage get deleteKeyboardKey =>
      const SvgGenImage.vec('assets/images/delete_keyboard_key.svg');

  /// File path: assets/images/dinarak_light.svg
  SvgGenImage get dinarakLight =>
      const SvgGenImage.vec('assets/images/dinarak_light.svg');

  /// File path: assets/images/discover_icon.svg
  SvgGenImage get discoverIcon =>
      const SvgGenImage.vec('assets/images/discover_icon.svg');

  /// File path: assets/images/dj_logo.svg
  SvgGenImage get djLogo => const SvgGenImage.vec('assets/images/dj_logo.svg');

  /// File path: assets/images/dot.svg
  SvgGenImage get dot => const SvgGenImage.vec('assets/images/dot.svg');

  /// File path: assets/images/duplicate_number.svg
  SvgGenImage get duplicateNumber =>
      const SvgGenImage.vec('assets/images/duplicate_number.svg');

  /// File path: assets/images/edit.svg
  SvgGenImage get edit => const SvgGenImage.vec('assets/images/edit.svg');

  /// File path: assets/images/emptyTradings.svg
  SvgGenImage get emptyTradings =>
      const SvgGenImage.vec('assets/images/emptyTradings.svg');

  /// File path: assets/images/empty_report.svg
  SvgGenImage get emptyReport =>
      const SvgGenImage.vec('assets/images/empty_report.svg');

  /// File path: assets/images/empty_search.svg
  SvgGenImage get emptySearch =>
      const SvgGenImage.vec('assets/images/empty_search.svg');

  /// File path: assets/images/empty_statement.svg
  SvgGenImage get emptyStatement =>
      const SvgGenImage.vec('assets/images/empty_statement.svg');

  /// File path: assets/images/empty_watchlist.svg
  SvgGenImage get emptyWatchlist =>
      const SvgGenImage.vec('assets/images/empty_watchlist.svg');

  /// File path: assets/images/equiti_avatar_placeholder_dark.svg
  SvgGenImage get equitiAvatarPlaceholderDark =>
      const SvgGenImage.vec('assets/images/equiti_avatar_placeholder_dark.svg');

  /// File path: assets/images/equiti_avatar_placeholder_light.svg
  SvgGenImage get equitiAvatarPlaceholderLight => const SvgGenImage.vec(
    'assets/images/equiti_avatar_placeholder_light.svg',
  );

  /// File path: assets/images/equiti_mono.svg
  SvgGenImage get equitiMono =>
      const SvgGenImage.vec('assets/images/equiti_mono.svg');

  /// File path: assets/images/equiti_mono_dark.svg
  SvgGenImage get equitiMonoDark =>
      const SvgGenImage.vec('assets/images/equiti_mono_dark.svg');

  /// File path: assets/images/expansion_arrow_ic.svg
  SvgGenImage get expansionArrowIc =>
      const SvgGenImage.vec('assets/images/expansion_arrow_ic.svg');

  /// File path: assets/images/filled_star_dark_mode.svg
  SvgGenImage get filledStarDarkMode =>
      const SvgGenImage.vec('assets/images/filled_star_dark_mode.svg');

  /// File path: assets/images/filled_star_light_mode.svg
  SvgGenImage get filledStarLightMode =>
      const SvgGenImage.vec('assets/images/filled_star_light_mode.svg');

  /// File path: assets/images/filter.svg
  SvgGenImage get filter => const SvgGenImage.vec('assets/images/filter.svg');

  /// File path: assets/images/forex.svg
  SvgGenImage get forex => const SvgGenImage.vec('assets/images/forex.svg');

  /// File path: assets/images/forward_chevron.svg
  SvgGenImage get forwardChevron =>
      const SvgGenImage.vec('assets/images/forward_chevron.svg');

  /// File path: assets/images/funding_credit_awarded.svg
  SvgGenImage get fundingCreditAwarded =>
      const SvgGenImage.vec('assets/images/funding_credit_awarded.svg');

  /// File path: assets/images/funding_deposit.svg
  SvgGenImage get fundingDeposit =>
      const SvgGenImage.vec('assets/images/funding_deposit.svg');

  /// File path: assets/images/funding_dividends_paid.svg
  SvgGenImage get fundingDividendsPaid =>
      const SvgGenImage.vec('assets/images/funding_dividends_paid.svg');

  /// File path: assets/images/funding_empty_list.svg
  SvgGenImage get fundingEmptyList =>
      const SvgGenImage.vec('assets/images/funding_empty_list.svg');

  /// File path: assets/images/funding_no_results.svg
  SvgGenImage get fundingNoResults =>
      const SvgGenImage.vec('assets/images/funding_no_results.svg');

  /// File path: assets/images/funding_transfer.svg
  SvgGenImage get fundingTransfer =>
      const SvgGenImage.vec('assets/images/funding_transfer.svg');

  /// File path: assets/images/funding_withdraw.svg
  SvgGenImage get fundingWithdraw =>
      const SvgGenImage.vec('assets/images/funding_withdraw.svg');

  /// File path: assets/images/gate2pay_ic.svg
  SvgGenImage get gate2payIc =>
      const SvgGenImage.vec('assets/images/gate2pay_ic.svg');

  /// File path: assets/images/help_icon.svg
  SvgGenImage get helpIcon =>
      const SvgGenImage.vec('assets/images/help_icon.svg');

  /// File path: assets/images/info-circle.svg
  SvgGenImage get infoCircle =>
      const SvgGenImage.vec('assets/images/info-circle.svg');

  /// File path: assets/images/insights_error.svg
  SvgGenImage get insightsError =>
      const SvgGenImage.vec('assets/images/insights_error.svg');

  /// File path: assets/images/line-chart-up.svg
  SvgGenImage get lineChartUp =>
      const SvgGenImage.vec('assets/images/line-chart-up.svg');

  /// File path: assets/images/line-chart.svg
  SvgGenImage get lineChart =>
      const SvgGenImage.vec('assets/images/line-chart.svg');

  /// File path: assets/images/markets_icon.svg
  SvgGenImage get marketsIcon =>
      const SvgGenImage.vec('assets/images/markets_icon.svg');

  /// File path: assets/images/maximize_ic.svg
  SvgGenImage get maximizeIc =>
      const SvgGenImage.vec('assets/images/maximize_ic.svg');

  /// File path: assets/images/more_icon.svg
  SvgGenImage get moreIcon =>
      const SvgGenImage.vec('assets/images/more_icon.svg');

  /// File path: assets/images/mt4_colored.svg
  SvgGenImage get mt4Colored =>
      const SvgGenImage.vec('assets/images/mt4_colored.svg');

  /// File path: assets/images/mt4_colored_dark.svg
  SvgGenImage get mt4ColoredDark =>
      const SvgGenImage.vec('assets/images/mt4_colored_dark.svg');

  /// File path: assets/images/mt4_mono.svg
  SvgGenImage get mt4Mono =>
      const SvgGenImage.vec('assets/images/mt4_mono.svg');

  /// File path: assets/images/mt4_mono_dark.svg
  SvgGenImage get mt4MonoDark =>
      const SvgGenImage.vec('assets/images/mt4_mono_dark.svg');

  /// File path: assets/images/mt4_not_supported.svg
  SvgGenImage get mt4NotSupported =>
      const SvgGenImage.vec('assets/images/mt4_not_supported.svg');

  /// File path: assets/images/mt5_colored.svg
  SvgGenImage get mt5Colored =>
      const SvgGenImage.vec('assets/images/mt5_colored.svg');

  /// File path: assets/images/mt5_colored_dark.svg
  SvgGenImage get mt5ColoredDark =>
      const SvgGenImage.vec('assets/images/mt5_colored_dark.svg');

  /// File path: assets/images/mt5_mono.svg
  SvgGenImage get mt5Mono =>
      const SvgGenImage.vec('assets/images/mt5_mono.svg');

  /// File path: assets/images/mt5_mono_dark.svg
  SvgGenImage get mt5MonoDark =>
      const SvgGenImage.vec('assets/images/mt5_mono_dark.svg');

  /// File path: assets/images/neteller_ic.svg
  SvgGenImage get netellerIc =>
      const SvgGenImage.vec('assets/images/neteller_ic.svg');

  /// File path: assets/images/no_account.svg
  SvgGenImage get noAccount =>
      const SvgGenImage.vec('assets/images/no_account.svg');

  /// File path: assets/images/no_active_price_alerts.svg
  SvgGenImage get noActivePriceAlerts =>
      const SvgGenImage.vec('assets/images/no_active_price_alerts.svg');

  /// File path: assets/images/no_wallet.svg
  SvgGenImage get noWallet =>
      const SvgGenImage.vec('assets/images/no_wallet.svg');

  /// File path: assets/images/performance_icon.svg
  SvgGenImage get performanceIcon =>
      const SvgGenImage.vec('assets/images/performance_icon.svg');

  /// File path: assets/images/pie-chart.svg
  SvgGenImage get pieChart =>
      const SvgGenImage.vec('assets/images/pie-chart.svg');

  /// Directory path: assets/images/png
  $AssetsImagesPngGen get png => const $AssetsImagesPngGen();

  /// File path: assets/images/portfolio_empty_alert_list.svg
  SvgGenImage get portfolioEmptyAlertList =>
      const SvgGenImage.vec('assets/images/portfolio_empty_alert_list.svg');

  /// File path: assets/images/portfolio_empty_insight_list.svg
  SvgGenImage get portfolioEmptyInsightList =>
      const SvgGenImage.vec('assets/images/portfolio_empty_insight_list.svg');

  /// File path: assets/images/portfolio_empty_order_list.svg
  SvgGenImage get portfolioEmptyOrderList =>
      const SvgGenImage.vec('assets/images/portfolio_empty_order_list.svg');

  /// File path: assets/images/portfolio_empty_position_list.svg
  SvgGenImage get portfolioEmptyPositionList =>
      const SvgGenImage.vec('assets/images/portfolio_empty_position_list.svg');

  /// File path: assets/images/portfolio_empty_trade_list.svg
  SvgGenImage get portfolioEmptyTradeList =>
      const SvgGenImage.vec('assets/images/portfolio_empty_trade_list.svg');

  /// File path: assets/images/portfolio_icon.svg
  SvgGenImage get portfolioIcon =>
      const SvgGenImage.vec('assets/images/portfolio_icon.svg');

  /// File path: assets/images/price_alert.svg
  SvgGenImage get priceAlert =>
      const SvgGenImage.vec('assets/images/price_alert.svg');

  /// File path: assets/images/price_alert_clock.svg
  SvgGenImage get priceAlertClock =>
      const SvgGenImage.vec('assets/images/price_alert_clock.svg');

  /// File path: assets/images/price_arrow_ic.svg
  SvgGenImage get priceArrowIc =>
      const SvgGenImage.vec('assets/images/price_arrow_ic.svg');

  /// File path: assets/images/search-green.svg
  SvgGenImage get searchGreen =>
      const SvgGenImage.vec('assets/images/search-green.svg');

  /// File path: assets/images/search-lg.svg
  SvgGenImage get searchLg =>
      const SvgGenImage.vec('assets/images/search-lg.svg');

  /// File path: assets/images/search_error.svg
  SvgGenImage get searchError =>
      const SvgGenImage.vec('assets/images/search_error.svg');

  /// File path: assets/images/sell_complete.svg
  SvgGenImage get sellComplete =>
      const SvgGenImage.vec('assets/images/sell_complete.svg');

  /// File path: assets/images/sell_order.svg
  SvgGenImage get sellOrder =>
      const SvgGenImage.vec('assets/images/sell_order.svg');

  /// File path: assets/images/server.svg
  SvgGenImage get server => const SvgGenImage.vec('assets/images/server.svg');

  /// File path: assets/images/settings.svg
  SvgGenImage get settings =>
      const SvgGenImage.vec('assets/images/settings.svg');

  /// File path: assets/images/skrill_ic.svg
  SvgGenImage get skrillIc =>
      const SvgGenImage.vec('assets/images/skrill_ic.svg');

  /// File path: assets/images/sort.svg
  SvgGenImage get sort => const SvgGenImage.vec('assets/images/sort.svg');

  /// File path: assets/images/star.svg
  SvgGenImage get star => const SvgGenImage.vec('assets/images/star.svg');

  /// File path: assets/images/statement_icon.svg
  SvgGenImage get statementIcon =>
      const SvgGenImage.vec('assets/images/statement_icon.svg');

  /// File path: assets/images/too_many_attempts.svg
  SvgGenImage get tooManyAttempts =>
      const SvgGenImage.vec('assets/images/too_many_attempts.svg');

  /// File path: assets/images/trading_no_results.svg
  SvgGenImage get tradingNoResults =>
      const SvgGenImage.vec('assets/images/trading_no_results.svg');

  /// File path: assets/images/trending_down_ic.svg
  SvgGenImage get trendingDownIc =>
      const SvgGenImage.vec('assets/images/trending_down_ic.svg');

  /// File path: assets/images/trending_up_ic.svg
  SvgGenImage get trendingUpIc =>
      const SvgGenImage.vec('assets/images/trending_up_ic.svg');

  /// File path: assets/images/trouble_with_otp.svg
  SvgGenImage get troubleWithOtp =>
      const SvgGenImage.vec('assets/images/trouble_with_otp.svg');

  /// File path: assets/images/view.svg
  SvgGenImage get view => const SvgGenImage.vec('assets/images/view.svg');

  /// File path: assets/images/wallet_details_empty.svg
  SvgGenImage get walletDetailsEmpty =>
      const SvgGenImage.vec('assets/images/wallet_details_empty.svg');

  /// File path: assets/images/wallet_details_error.svg
  SvgGenImage get walletDetailsError =>
      const SvgGenImage.vec('assets/images/wallet_details_error.svg');

  /// File path: assets/images/watchlist.svg
  SvgGenImage get watchlist =>
      const SvgGenImage.vec('assets/images/watchlist.svg');

  /// File path: assets/images/x-circle.svg
  SvgGenImage get xCircle =>
      const SvgGenImage.vec('assets/images/x-circle.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    bug,
    account,
    accountMarginLevelSegments,
    alertTriangle,
    arrow,
    arrowLeft,
    backButton,
    bankIc,
    barChart,
    buyComplete,
    buyOrder,
    calendarAdd,
    calendar,
    chartError,
    checkCircle,
    check,
    chevronDownSvg,
    chevronDownSvg_,
    chevron,
    chevronRight,
    clockFastForward,
    close,
    copy,
    creditCardIconLight,
    deleteKeyboardKey,
    dinarakLight,
    discoverIcon,
    djLogo,
    dot,
    duplicateNumber,
    edit,
    emptyTradings,
    emptyReport,
    emptySearch,
    emptyStatement,
    emptyWatchlist,
    equitiAvatarPlaceholderDark,
    equitiAvatarPlaceholderLight,
    equitiMono,
    equitiMonoDark,
    expansionArrowIc,
    filledStarDarkMode,
    filledStarLightMode,
    filter,
    forex,
    forwardChevron,
    fundingCreditAwarded,
    fundingDeposit,
    fundingDividendsPaid,
    fundingEmptyList,
    fundingNoResults,
    fundingTransfer,
    fundingWithdraw,
    gate2payIc,
    helpIcon,
    infoCircle,
    insightsError,
    lineChartUp,
    lineChart,
    marketsIcon,
    maximizeIc,
    moreIcon,
    mt4Colored,
    mt4ColoredDark,
    mt4Mono,
    mt4MonoDark,
    mt4NotSupported,
    mt5Colored,
    mt5ColoredDark,
    mt5Mono,
    mt5MonoDark,
    netellerIc,
    noAccount,
    noActivePriceAlerts,
    noWallet,
    performanceIcon,
    pieChart,
    portfolioEmptyAlertList,
    portfolioEmptyInsightList,
    portfolioEmptyOrderList,
    portfolioEmptyPositionList,
    portfolioEmptyTradeList,
    portfolioIcon,
    priceAlert,
    priceAlertClock,
    priceArrowIc,
    searchGreen,
    searchLg,
    searchError,
    sellComplete,
    sellOrder,
    server,
    settings,
    skrillIc,
    sort,
    star,
    statementIcon,
    tooManyAttempts,
    tradingNoResults,
    trendingDownIc,
    trendingUpIc,
    troubleWithOtp,
    view,
    walletDetailsEmpty,
    walletDetailsError,
    watchlist,
    xCircle,
  ];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/empty_demo_animation_dark.json
  String get emptyDemoAnimationDark =>
      'packages/e_trader/assets/lottie/empty_demo_animation_dark.json';

  /// File path: assets/lottie/empty_demo_animation_light.json
  String get emptyDemoAnimationLight =>
      'packages/e_trader/assets/lottie/empty_demo_animation_light.json';

  /// List of all assets
  List<String> get values => [emptyDemoAnimationDark, emptyDemoAnimationLight];
}

class $AssetsChartChartingLibraryGen {
  const $AssetsChartChartingLibraryGen();

  /// Directory path: assets/chart/charting_library/bundles
  $AssetsChartChartingLibraryBundlesGen get bundles =>
      const $AssetsChartChartingLibraryBundlesGen();

  /// File path: assets/chart/charting_library/charting_library.cjs.js
  String get chartingLibraryCjs =>
      'packages/e_trader/assets/chart/charting_library/charting_library.cjs.js';

  /// File path: assets/chart/charting_library/charting_library.d.ts
  String get chartingLibraryD =>
      'packages/e_trader/assets/chart/charting_library/charting_library.d.ts';

  /// File path: assets/chart/charting_library/charting_library.esm.js
  String get chartingLibraryEsm =>
      'packages/e_trader/assets/chart/charting_library/charting_library.esm.js';

  /// File path: assets/chart/charting_library/charting_library.js
  String get chartingLibrary =>
      'packages/e_trader/assets/chart/charting_library/charting_library.js';

  /// File path: assets/chart/charting_library/charting_library.standalone.js
  String get chartingLibraryStandalone =>
      'packages/e_trader/assets/chart/charting_library/charting_library.standalone.js';

  /// File path: assets/chart/charting_library/datafeed-api.d.ts
  String get datafeedApiD =>
      'packages/e_trader/assets/chart/charting_library/datafeed-api.d.ts';

  /// File path: assets/chart/charting_library/package.json
  String get package =>
      'packages/e_trader/assets/chart/charting_library/package.json';

  /// File path: assets/chart/charting_library/sameorigin.html
  String get sameorigin =>
      'packages/e_trader/assets/chart/charting_library/sameorigin.html';

  /// List of all assets
  List<String> get values => [
    chartingLibraryCjs,
    chartingLibraryD,
    chartingLibraryEsm,
    chartingLibrary,
    chartingLibraryStandalone,
    datafeedApiD,
    package,
    sameorigin,
  ];
}

class $AssetsChartSrcGen {
  const $AssetsChartSrcGen();

  /// File path: assets/chart/src/datafeed.js
  String get datafeed => 'packages/e_trader/assets/chart/src/datafeed.js';

  /// File path: assets/chart/src/layout_adabter.js
  String get layoutAdabter =>
      'packages/e_trader/assets/chart/src/layout_adabter.js';

  /// File path: assets/chart/src/main.js
  String get main => 'packages/e_trader/assets/chart/src/main.js';

  /// List of all assets
  List<String> get values => [datafeed, layoutAdabter, main];
}

class $AssetsImagesPngGen {
  const $AssetsImagesPngGen();

  /// File path: assets/images/png/cliq.png
  AssetGenImage get cliq => const AssetGenImage('assets/images/png/cliq.png');

  /// File path: assets/images/png/local_bank_icon_light.png
  AssetGenImage get localBankIconLight =>
      const AssetGenImage('assets/images/png/local_bank_icon_light.png');

  /// List of all assets
  List<AssetGenImage> get values => [cliq, localBankIconLight];
}

class $AssetsChartChartingLibraryBundlesGen {
  const $AssetsChartChartingLibraryBundlesGen();

  /// File path: assets/chart/charting_library/bundles/1042.7bd6143015a4458ca0eb.js
  String get a10427bd6143015a4458ca0eb =>
      'packages/e_trader/assets/chart/charting_library/bundles/1042.7bd6143015a4458ca0eb.js';

  /// File path: assets/chart/charting_library/bundles/1053.16c0fd7539d08ad5ffd3.css
  String get a105316c0fd7539d08ad5ffd3 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1053.16c0fd7539d08ad5ffd3.css';

  /// File path: assets/chart/charting_library/bundles/1053.16c0fd7539d08ad5ffd3.rtl.css
  String get a105316c0fd7539d08ad5ffd3Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1053.16c0fd7539d08ad5ffd3.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1227.68542dac293294290d44.css
  String get a122768542dac293294290d44 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1227.68542dac293294290d44.css';

  /// File path: assets/chart/charting_library/bundles/1227.68542dac293294290d44.rtl.css
  String get a122768542dac293294290d44Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1227.68542dac293294290d44.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1259.b80c727a6df944a103fe.css
  String get a1259B80c727a6df944a103fe =>
      'packages/e_trader/assets/chart/charting_library/bundles/1259.b80c727a6df944a103fe.css';

  /// File path: assets/chart/charting_library/bundles/1259.b80c727a6df944a103fe.rtl.css
  String get a1259B80c727a6df944a103feRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1259.b80c727a6df944a103fe.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1298.d597f50113da0645dcf5.css
  String get a1298D597f50113da0645dcf5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1298.d597f50113da0645dcf5.css';

  /// File path: assets/chart/charting_library/bundles/1298.d597f50113da0645dcf5.rtl.css
  String get a1298D597f50113da0645dcf5Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1298.d597f50113da0645dcf5.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1335.5f0edd452b4452a9eaf4.css
  String get a13355f0edd452b4452a9eaf4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1335.5f0edd452b4452a9eaf4.css';

  /// File path: assets/chart/charting_library/bundles/1335.5f0edd452b4452a9eaf4.rtl.css
  String get a13355f0edd452b4452a9eaf4Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1335.5f0edd452b4452a9eaf4.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1398.d778724528a9b5665050.css
  String get a1398D778724528a9b5665050 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1398.d778724528a9b5665050.css';

  /// File path: assets/chart/charting_library/bundles/1398.d778724528a9b5665050.rtl.css
  String get a1398D778724528a9b5665050Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1398.d778724528a9b5665050.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1538.b6bc85c0060285eaeced.css
  String get a1538B6bc85c0060285eaeced =>
      'packages/e_trader/assets/chart/charting_library/bundles/1538.b6bc85c0060285eaeced.css';

  /// File path: assets/chart/charting_library/bundles/1538.b6bc85c0060285eaeced.rtl.css
  String get a1538B6bc85c0060285eaecedRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1538.b6bc85c0060285eaeced.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1553.c076714f5e24887f0b94.js
  String get a1553C076714f5e24887f0b94 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1553.c076714f5e24887f0b94.js';

  /// File path: assets/chart/charting_library/bundles/1729.0f1e7f0f19efe1232ab6.css
  String get a17290f1e7f0f19efe1232ab6 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1729.0f1e7f0f19efe1232ab6.css';

  /// File path: assets/chart/charting_library/bundles/1729.0f1e7f0f19efe1232ab6.rtl.css
  String get a17290f1e7f0f19efe1232ab6Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1729.0f1e7f0f19efe1232ab6.rtl.css';

  /// File path: assets/chart/charting_library/bundles/1782.637b414d2efcd03840f5.css
  String get a1782637b414d2efcd03840f5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/1782.637b414d2efcd03840f5.css';

  /// File path: assets/chart/charting_library/bundles/1782.637b414d2efcd03840f5.rtl.css
  String get a1782637b414d2efcd03840f5Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/1782.637b414d2efcd03840f5.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2038.9fc99258845013ecf959.css
  String get a20389fc99258845013ecf959 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2038.9fc99258845013ecf959.css';

  /// File path: assets/chart/charting_library/bundles/2038.9fc99258845013ecf959.rtl.css
  String get a20389fc99258845013ecf959Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2038.9fc99258845013ecf959.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2079.f2a9f3dd7d4f8cb9f2fc.js
  String get a2079F2a9f3dd7d4f8cb9f2fc =>
      'packages/e_trader/assets/chart/charting_library/bundles/2079.f2a9f3dd7d4f8cb9f2fc.js';

  /// File path: assets/chart/charting_library/bundles/2106.407cb827c0acab444e09.css
  String get a2106407cb827c0acab444e09 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2106.407cb827c0acab444e09.css';

  /// File path: assets/chart/charting_library/bundles/2106.407cb827c0acab444e09.rtl.css
  String get a2106407cb827c0acab444e09Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2106.407cb827c0acab444e09.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2153.302cc6392cc9f067008b.css
  String get a2153302cc6392cc9f067008b =>
      'packages/e_trader/assets/chart/charting_library/bundles/2153.302cc6392cc9f067008b.css';

  /// File path: assets/chart/charting_library/bundles/2153.302cc6392cc9f067008b.rtl.css
  String get a2153302cc6392cc9f067008bRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2153.302cc6392cc9f067008b.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2157.d1cab62b805a1a6282a1.js
  String get a2157D1cab62b805a1a6282a1 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2157.d1cab62b805a1a6282a1.js';

  /// File path: assets/chart/charting_library/bundles/2198.2762b6c7b8aaa0156d33.css
  String get a21982762b6c7b8aaa0156d33 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2198.2762b6c7b8aaa0156d33.css';

  /// File path: assets/chart/charting_library/bundles/2198.2762b6c7b8aaa0156d33.rtl.css
  String get a21982762b6c7b8aaa0156d33Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2198.2762b6c7b8aaa0156d33.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2208.2c33dcc3a2ea34861c2c.css
  String get a22082c33dcc3a2ea34861c2c =>
      'packages/e_trader/assets/chart/charting_library/bundles/2208.2c33dcc3a2ea34861c2c.css';

  /// File path: assets/chart/charting_library/bundles/2208.2c33dcc3a2ea34861c2c.rtl.css
  String get a22082c33dcc3a2ea34861c2cRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2208.2c33dcc3a2ea34861c2c.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2227.e46f1f2d53203e1fb152.js
  String get a2227E46f1f2d53203e1fb152 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2227.e46f1f2d53203e1fb152.js';

  /// File path: assets/chart/charting_library/bundles/223.103e68709eae15d3109a.css
  String get a223103e68709eae15d3109a =>
      'packages/e_trader/assets/chart/charting_library/bundles/223.103e68709eae15d3109a.css';

  /// File path: assets/chart/charting_library/bundles/223.103e68709eae15d3109a.rtl.css
  String get a223103e68709eae15d3109aRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/223.103e68709eae15d3109a.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2248.362fa6a7ab1f3e3b06c4.css
  String get a2248362fa6a7ab1f3e3b06c4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2248.362fa6a7ab1f3e3b06c4.css';

  /// File path: assets/chart/charting_library/bundles/2248.362fa6a7ab1f3e3b06c4.rtl.css
  String get a2248362fa6a7ab1f3e3b06c4Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2248.362fa6a7ab1f3e3b06c4.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2264.1e73b010b4ad6956c0d3.js
  String get a22641e73b010b4ad6956c0d3 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2264.1e73b010b4ad6956c0d3.js';

  /// File path: assets/chart/charting_library/bundles/2417.7835cfcd422c2f0478a4.css
  String get a24177835cfcd422c2f0478a4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2417.7835cfcd422c2f0478a4.css';

  /// File path: assets/chart/charting_library/bundles/2417.7835cfcd422c2f0478a4.rtl.css
  String get a24177835cfcd422c2f0478a4Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2417.7835cfcd422c2f0478a4.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2603.67a756e92dbe51a30f72.css
  String get a260367a756e92dbe51a30f72 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2603.67a756e92dbe51a30f72.css';

  /// File path: assets/chart/charting_library/bundles/2603.67a756e92dbe51a30f72.rtl.css
  String get a260367a756e92dbe51a30f72Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2603.67a756e92dbe51a30f72.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2751.e13accd5da6697245484.js
  String get a2751E13accd5da6697245484 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2751.e13accd5da6697245484.js';

  /// File path: assets/chart/charting_library/bundles/2767.25805ef40a93fc603316.css
  String get a276725805ef40a93fc603316 =>
      'packages/e_trader/assets/chart/charting_library/bundles/2767.25805ef40a93fc603316.css';

  /// File path: assets/chart/charting_library/bundles/2767.25805ef40a93fc603316.rtl.css
  String get a276725805ef40a93fc603316Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2767.25805ef40a93fc603316.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2841.9384677f17d8e3fe6f1e.css
  String get a28419384677f17d8e3fe6f1e =>
      'packages/e_trader/assets/chart/charting_library/bundles/2841.9384677f17d8e3fe6f1e.css';

  /// File path: assets/chart/charting_library/bundles/2841.9384677f17d8e3fe6f1e.rtl.css
  String get a28419384677f17d8e3fe6f1eRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2841.9384677f17d8e3fe6f1e.rtl.css';

  /// File path: assets/chart/charting_library/bundles/291.cdb1f8bec5b9d4688794.css
  String get a291Cdb1f8bec5b9d4688794 =>
      'packages/e_trader/assets/chart/charting_library/bundles/291.cdb1f8bec5b9d4688794.css';

  /// File path: assets/chart/charting_library/bundles/291.cdb1f8bec5b9d4688794.rtl.css
  String get a291Cdb1f8bec5b9d4688794Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/291.cdb1f8bec5b9d4688794.rtl.css';

  /// File path: assets/chart/charting_library/bundles/2950.f052bbea83b78b27ffba.css
  String get a2950F052bbea83b78b27ffba =>
      'packages/e_trader/assets/chart/charting_library/bundles/2950.f052bbea83b78b27ffba.css';

  /// File path: assets/chart/charting_library/bundles/2950.f052bbea83b78b27ffba.rtl.css
  String get a2950F052bbea83b78b27ffbaRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/2950.f052bbea83b78b27ffba.rtl.css';

  /// File path: assets/chart/charting_library/bundles/302.f0da356af4ffe12ac66d.css
  String get a302F0da356af4ffe12ac66d =>
      'packages/e_trader/assets/chart/charting_library/bundles/302.f0da356af4ffe12ac66d.css';

  /// File path: assets/chart/charting_library/bundles/302.f0da356af4ffe12ac66d.rtl.css
  String get a302F0da356af4ffe12ac66dRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/302.f0da356af4ffe12ac66d.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3060.fbb750fd312778403036.css
  String get a3060Fbb750fd312778403036 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3060.fbb750fd312778403036.css';

  /// File path: assets/chart/charting_library/bundles/3060.fbb750fd312778403036.rtl.css
  String get a3060Fbb750fd312778403036Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3060.fbb750fd312778403036.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3098.e317e819fbead3a8b108.js
  String get a3098E317e819fbead3a8b108 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3098.e317e819fbead3a8b108.js';

  /// File path: assets/chart/charting_library/bundles/3114.74916a9532052e2cfa84.css
  String get a311474916a9532052e2cfa84 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3114.74916a9532052e2cfa84.css';

  /// File path: assets/chart/charting_library/bundles/3114.74916a9532052e2cfa84.rtl.css
  String get a311474916a9532052e2cfa84Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3114.74916a9532052e2cfa84.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3204.bd0eb51c8ff7ca736c6b.css
  String get a3204Bd0eb51c8ff7ca736c6b =>
      'packages/e_trader/assets/chart/charting_library/bundles/3204.bd0eb51c8ff7ca736c6b.css';

  /// File path: assets/chart/charting_library/bundles/3204.bd0eb51c8ff7ca736c6b.rtl.css
  String get a3204Bd0eb51c8ff7ca736c6bRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3204.bd0eb51c8ff7ca736c6b.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3322.296df13c49b70d3e0af6.css
  String get a3322296df13c49b70d3e0af6 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3322.296df13c49b70d3e0af6.css';

  /// File path: assets/chart/charting_library/bundles/3322.296df13c49b70d3e0af6.rtl.css
  String get a3322296df13c49b70d3e0af6Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3322.296df13c49b70d3e0af6.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3443.5802260e3c522b563151.js
  String get a34435802260e3c522b563151 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3443.5802260e3c522b563151.js';

  /// File path: assets/chart/charting_library/bundles/3463.9eaf72c1c0a6d8f2cacd.css
  String get a34639eaf72c1c0a6d8f2cacd =>
      'packages/e_trader/assets/chart/charting_library/bundles/3463.9eaf72c1c0a6d8f2cacd.css';

  /// File path: assets/chart/charting_library/bundles/3463.9eaf72c1c0a6d8f2cacd.rtl.css
  String get a34639eaf72c1c0a6d8f2cacdRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3463.9eaf72c1c0a6d8f2cacd.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3504.22bc26609f4f438a0ff5.js
  String get a350422bc26609f4f438a0ff5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3504.22bc26609f4f438a0ff5.js';

  /// File path: assets/chart/charting_library/bundles/3538.7894b0f2ada5563099b2.css
  String get a35387894b0f2ada5563099b2 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3538.7894b0f2ada5563099b2.css';

  /// File path: assets/chart/charting_library/bundles/3538.7894b0f2ada5563099b2.rtl.css
  String get a35387894b0f2ada5563099b2Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3538.7894b0f2ada5563099b2.rtl.css';

  /// File path: assets/chart/charting_library/bundles/3547.2cd7133d1e05f50985a5.css
  String get a35472cd7133d1e05f50985a5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3547.2cd7133d1e05f50985a5.css';

  /// File path: assets/chart/charting_library/bundles/3547.2cd7133d1e05f50985a5.rtl.css
  String get a35472cd7133d1e05f50985a5Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3547.2cd7133d1e05f50985a5.rtl.css';

  /// File path: assets/chart/charting_library/bundles/361.0b7d3fd5d9c0b7234428.js
  String get a3610b7d3fd5d9c0b7234428 =>
      'packages/e_trader/assets/chart/charting_library/bundles/361.0b7d3fd5d9c0b7234428.js';

  /// File path: assets/chart/charting_library/bundles/3762.2ec6c50ea553cf1e0197.css
  String get a37622ec6c50ea553cf1e0197 =>
      'packages/e_trader/assets/chart/charting_library/bundles/3762.2ec6c50ea553cf1e0197.css';

  /// File path: assets/chart/charting_library/bundles/3762.2ec6c50ea553cf1e0197.rtl.css
  String get a37622ec6c50ea553cf1e0197Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/3762.2ec6c50ea553cf1e0197.rtl.css';

  /// File path: assets/chart/charting_library/bundles/412.c71231d81b196034eefa.css
  String get a412C71231d81b196034eefa =>
      'packages/e_trader/assets/chart/charting_library/bundles/412.c71231d81b196034eefa.css';

  /// File path: assets/chart/charting_library/bundles/412.c71231d81b196034eefa.rtl.css
  String get a412C71231d81b196034eefaRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/412.c71231d81b196034eefa.rtl.css';

  /// File path: assets/chart/charting_library/bundles/4392.f247c8bc262c51d15d8e.css
  String get a4392F247c8bc262c51d15d8e =>
      'packages/e_trader/assets/chart/charting_library/bundles/4392.f247c8bc262c51d15d8e.css';

  /// File path: assets/chart/charting_library/bundles/4392.f247c8bc262c51d15d8e.rtl.css
  String get a4392F247c8bc262c51d15d8eRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/4392.f247c8bc262c51d15d8e.rtl.css';

  /// File path: assets/chart/charting_library/bundles/4447.f947855b7ae01dfb68f8.css
  String get a4447F947855b7ae01dfb68f8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/4447.f947855b7ae01dfb68f8.css';

  /// File path: assets/chart/charting_library/bundles/4447.f947855b7ae01dfb68f8.rtl.css
  String get a4447F947855b7ae01dfb68f8Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/4447.f947855b7ae01dfb68f8.rtl.css';

  /// File path: assets/chart/charting_library/bundles/4482.d8a5a63efb2739dac2bf.js
  String get a4482D8a5a63efb2739dac2bf =>
      'packages/e_trader/assets/chart/charting_library/bundles/4482.d8a5a63efb2739dac2bf.js';

  /// File path: assets/chart/charting_library/bundles/4632.c0ade5e298b9c20b0703.css
  String get a4632C0ade5e298b9c20b0703 =>
      'packages/e_trader/assets/chart/charting_library/bundles/4632.c0ade5e298b9c20b0703.css';

  /// File path: assets/chart/charting_library/bundles/4632.c0ade5e298b9c20b0703.rtl.css
  String get a4632C0ade5e298b9c20b0703Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/4632.c0ade5e298b9c20b0703.rtl.css';

  /// File path: assets/chart/charting_library/bundles/4774.94117311dcd036db8fc3.js
  String get a477494117311dcd036db8fc3 =>
      'packages/e_trader/assets/chart/charting_library/bundles/4774.94117311dcd036db8fc3.js';

  /// File path: assets/chart/charting_library/bundles/4804.e61dfd0736b1e1dc484f.js
  String get a4804E61dfd0736b1e1dc484f =>
      'packages/e_trader/assets/chart/charting_library/bundles/4804.e61dfd0736b1e1dc484f.js';

  /// File path: assets/chart/charting_library/bundles/4814.8f9f36a0b818caadd4c6.css
  String get a48148f9f36a0b818caadd4c6 =>
      'packages/e_trader/assets/chart/charting_library/bundles/4814.8f9f36a0b818caadd4c6.css';

  /// File path: assets/chart/charting_library/bundles/4814.8f9f36a0b818caadd4c6.rtl.css
  String get a48148f9f36a0b818caadd4c6Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/4814.8f9f36a0b818caadd4c6.rtl.css';

  /// File path: assets/chart/charting_library/bundles/4959.bbafa4b076f2c64bd203.css
  String get a4959Bbafa4b076f2c64bd203 =>
      'packages/e_trader/assets/chart/charting_library/bundles/4959.bbafa4b076f2c64bd203.css';

  /// File path: assets/chart/charting_library/bundles/4959.bbafa4b076f2c64bd203.rtl.css
  String get a4959Bbafa4b076f2c64bd203Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/4959.bbafa4b076f2c64bd203.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5075.8c45fa8c1b1852b25a53.js
  String get a50758c45fa8c1b1852b25a53 =>
      'packages/e_trader/assets/chart/charting_library/bundles/5075.8c45fa8c1b1852b25a53.js';

  /// File path: assets/chart/charting_library/bundles/5301.9c3036a1f307fa98f03a.js
  String get a53019c3036a1f307fa98f03a =>
      'packages/e_trader/assets/chart/charting_library/bundles/5301.9c3036a1f307fa98f03a.js';

  /// File path: assets/chart/charting_library/bundles/5446.24be1f27837a64b8646b.css
  String get a544624be1f27837a64b8646b =>
      'packages/e_trader/assets/chart/charting_library/bundles/5446.24be1f27837a64b8646b.css';

  /// File path: assets/chart/charting_library/bundles/5446.24be1f27837a64b8646b.rtl.css
  String get a544624be1f27837a64b8646bRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5446.24be1f27837a64b8646b.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5450.11f0aed150d079c71677.js
  String get a545011f0aed150d079c71677 =>
      'packages/e_trader/assets/chart/charting_library/bundles/5450.11f0aed150d079c71677.js';

  /// File path: assets/chart/charting_library/bundles/5458.0ad67886dc6df4a03094.css
  String get a54580ad67886dc6df4a03094 =>
      'packages/e_trader/assets/chart/charting_library/bundles/5458.0ad67886dc6df4a03094.css';

  /// File path: assets/chart/charting_library/bundles/5458.0ad67886dc6df4a03094.rtl.css
  String get a54580ad67886dc6df4a03094Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5458.0ad67886dc6df4a03094.rtl.css';

  /// File path: assets/chart/charting_library/bundles/55.fbe45f08e8a580b70f48.css
  String get a55Fbe45f08e8a580b70f48 =>
      'packages/e_trader/assets/chart/charting_library/bundles/55.fbe45f08e8a580b70f48.css';

  /// File path: assets/chart/charting_library/bundles/55.fbe45f08e8a580b70f48.rtl.css
  String get a55Fbe45f08e8a580b70f48Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/55.fbe45f08e8a580b70f48.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5514.81333b83f4e18e9cde99.css
  String get a551481333b83f4e18e9cde99 =>
      'packages/e_trader/assets/chart/charting_library/bundles/5514.81333b83f4e18e9cde99.css';

  /// File path: assets/chart/charting_library/bundles/5514.81333b83f4e18e9cde99.rtl.css
  String get a551481333b83f4e18e9cde99Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5514.81333b83f4e18e9cde99.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5546.681a9e7b91dfc741f79f.css
  String get a5546681a9e7b91dfc741f79f =>
      'packages/e_trader/assets/chart/charting_library/bundles/5546.681a9e7b91dfc741f79f.css';

  /// File path: assets/chart/charting_library/bundles/5546.681a9e7b91dfc741f79f.rtl.css
  String get a5546681a9e7b91dfc741f79fRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5546.681a9e7b91dfc741f79f.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5622.b8f2257b27ac82b2d12e.css
  String get a5622B8f2257b27ac82b2d12e =>
      'packages/e_trader/assets/chart/charting_library/bundles/5622.b8f2257b27ac82b2d12e.css';

  /// File path: assets/chart/charting_library/bundles/5622.b8f2257b27ac82b2d12e.rtl.css
  String get a5622B8f2257b27ac82b2d12eRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5622.b8f2257b27ac82b2d12e.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5666.234fdce3b58e2b7f4b38.css
  String get a5666234fdce3b58e2b7f4b38 =>
      'packages/e_trader/assets/chart/charting_library/bundles/5666.234fdce3b58e2b7f4b38.css';

  /// File path: assets/chart/charting_library/bundles/5666.234fdce3b58e2b7f4b38.rtl.css
  String get a5666234fdce3b58e2b7f4b38Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5666.234fdce3b58e2b7f4b38.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5715.5f6b9b0b26b050b9aaaf.js
  String get a57155f6b9b0b26b050b9aaaf =>
      'packages/e_trader/assets/chart/charting_library/bundles/5715.5f6b9b0b26b050b9aaaf.js';

  /// File path: assets/chart/charting_library/bundles/5877.e211c1f134e8a786af4f.css
  String get a5877E211c1f134e8a786af4f =>
      'packages/e_trader/assets/chart/charting_library/bundles/5877.e211c1f134e8a786af4f.css';

  /// File path: assets/chart/charting_library/bundles/5877.e211c1f134e8a786af4f.rtl.css
  String get a5877E211c1f134e8a786af4fRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5877.e211c1f134e8a786af4f.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5883.86db8dd61a862770480d.css
  String get a588386db8dd61a862770480d =>
      'packages/e_trader/assets/chart/charting_library/bundles/5883.86db8dd61a862770480d.css';

  /// File path: assets/chart/charting_library/bundles/5883.86db8dd61a862770480d.rtl.css
  String get a588386db8dd61a862770480dRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5883.86db8dd61a862770480d.rtl.css';

  /// File path: assets/chart/charting_library/bundles/5922.fbf362211645ecd654fa.css
  String get a5922Fbf362211645ecd654fa =>
      'packages/e_trader/assets/chart/charting_library/bundles/5922.fbf362211645ecd654fa.css';

  /// File path: assets/chart/charting_library/bundles/5922.fbf362211645ecd654fa.rtl.css
  String get a5922Fbf362211645ecd654faRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/5922.fbf362211645ecd654fa.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6014.98494a842e9d8e3e6fa4.js
  String get a601498494a842e9d8e3e6fa4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/6014.98494a842e9d8e3e6fa4.js';

  /// File path: assets/chart/charting_library/bundles/6085.7b1bd95c4ea1c9f8ad7a.css
  String get a60857b1bd95c4ea1c9f8ad7a =>
      'packages/e_trader/assets/chart/charting_library/bundles/6085.7b1bd95c4ea1c9f8ad7a.css';

  /// File path: assets/chart/charting_library/bundles/6085.7b1bd95c4ea1c9f8ad7a.rtl.css
  String get a60857b1bd95c4ea1c9f8ad7aRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6085.7b1bd95c4ea1c9f8ad7a.rtl.css';

  /// File path: assets/chart/charting_library/bundles/61.6420f5b2ff091a8d8a5f.css
  String get a616420f5b2ff091a8d8a5f =>
      'packages/e_trader/assets/chart/charting_library/bundles/61.6420f5b2ff091a8d8a5f.css';

  /// File path: assets/chart/charting_library/bundles/61.6420f5b2ff091a8d8a5f.rtl.css
  String get a616420f5b2ff091a8d8a5fRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/61.6420f5b2ff091a8d8a5f.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6107.b8b526751e7230cd2e69.css
  String get a6107B8b526751e7230cd2e69 =>
      'packages/e_trader/assets/chart/charting_library/bundles/6107.b8b526751e7230cd2e69.css';

  /// File path: assets/chart/charting_library/bundles/6107.b8b526751e7230cd2e69.rtl.css
  String get a6107B8b526751e7230cd2e69Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6107.b8b526751e7230cd2e69.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6220.b02054ace78f1bbd7ab4.css
  String get a6220B02054ace78f1bbd7ab4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/6220.b02054ace78f1bbd7ab4.css';

  /// File path: assets/chart/charting_library/bundles/6220.b02054ace78f1bbd7ab4.rtl.css
  String get a6220B02054ace78f1bbd7ab4Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6220.b02054ace78f1bbd7ab4.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6246.3ecbd24f95eff1b7dd4f.css
  String get a62463ecbd24f95eff1b7dd4f =>
      'packages/e_trader/assets/chart/charting_library/bundles/6246.3ecbd24f95eff1b7dd4f.css';

  /// File path: assets/chart/charting_library/bundles/6246.3ecbd24f95eff1b7dd4f.rtl.css
  String get a62463ecbd24f95eff1b7dd4fRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6246.3ecbd24f95eff1b7dd4f.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6408.53f95c8edf441cf41f5e.js
  String get a640853f95c8edf441cf41f5e =>
      'packages/e_trader/assets/chart/charting_library/bundles/6408.53f95c8edf441cf41f5e.js';

  /// File path: assets/chart/charting_library/bundles/6625.8ead11e183058d3b8778.css
  String get a66258ead11e183058d3b8778 =>
      'packages/e_trader/assets/chart/charting_library/bundles/6625.8ead11e183058d3b8778.css';

  /// File path: assets/chart/charting_library/bundles/6625.8ead11e183058d3b8778.rtl.css
  String get a66258ead11e183058d3b8778Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6625.8ead11e183058d3b8778.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6665.0c4a6582bd484370f525.css
  String get a66650c4a6582bd484370f525 =>
      'packages/e_trader/assets/chart/charting_library/bundles/6665.0c4a6582bd484370f525.css';

  /// File path: assets/chart/charting_library/bundles/6665.0c4a6582bd484370f525.rtl.css
  String get a66650c4a6582bd484370f525Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6665.0c4a6582bd484370f525.rtl.css';

  /// File path: assets/chart/charting_library/bundles/6955.d365d11fe6e348cae0ec.css
  String get a6955D365d11fe6e348cae0ec =>
      'packages/e_trader/assets/chart/charting_library/bundles/6955.d365d11fe6e348cae0ec.css';

  /// File path: assets/chart/charting_library/bundles/6955.d365d11fe6e348cae0ec.rtl.css
  String get a6955D365d11fe6e348cae0ecRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/6955.d365d11fe6e348cae0ec.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7092.505a377d19e2d0f1294f.css
  String get a7092505a377d19e2d0f1294f =>
      'packages/e_trader/assets/chart/charting_library/bundles/7092.505a377d19e2d0f1294f.css';

  /// File path: assets/chart/charting_library/bundles/7092.505a377d19e2d0f1294f.rtl.css
  String get a7092505a377d19e2d0f1294fRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7092.505a377d19e2d0f1294f.rtl.css';

  /// File path: assets/chart/charting_library/bundles/71.cfe59e9cc92d04ef185d.css
  String get a71Cfe59e9cc92d04ef185d =>
      'packages/e_trader/assets/chart/charting_library/bundles/71.cfe59e9cc92d04ef185d.css';

  /// File path: assets/chart/charting_library/bundles/71.cfe59e9cc92d04ef185d.rtl.css
  String get a71Cfe59e9cc92d04ef185dRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/71.cfe59e9cc92d04ef185d.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7125.ee8a75f271c0eade9d69.css
  String get a7125Ee8a75f271c0eade9d69 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7125.ee8a75f271c0eade9d69.css';

  /// File path: assets/chart/charting_library/bundles/7125.ee8a75f271c0eade9d69.rtl.css
  String get a7125Ee8a75f271c0eade9d69Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7125.ee8a75f271c0eade9d69.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7223.a5a98f21c4a7b2ef69f7.js
  String get a7223A5a98f21c4a7b2ef69f7 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7223.a5a98f21c4a7b2ef69f7.js';

  /// File path: assets/chart/charting_library/bundles/7241.6134620811847ea8ea05.css
  String get a72416134620811847ea8ea05 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7241.6134620811847ea8ea05.css';

  /// File path: assets/chart/charting_library/bundles/7241.6134620811847ea8ea05.rtl.css
  String get a72416134620811847ea8ea05Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7241.6134620811847ea8ea05.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7346.a2efeed47130dd4e832c.js
  String get a7346A2efeed47130dd4e832c =>
      'packages/e_trader/assets/chart/charting_library/bundles/7346.a2efeed47130dd4e832c.js';

  /// File path: assets/chart/charting_library/bundles/7353.3aff08076e0bff7df116.css
  String get a73533aff08076e0bff7df116 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7353.3aff08076e0bff7df116.css';

  /// File path: assets/chart/charting_library/bundles/7353.3aff08076e0bff7df116.rtl.css
  String get a73533aff08076e0bff7df116Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7353.3aff08076e0bff7df116.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7399.bdaaf55cfdf2cf1aca38.css
  String get a7399Bdaaf55cfdf2cf1aca38 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7399.bdaaf55cfdf2cf1aca38.css';

  /// File path: assets/chart/charting_library/bundles/7399.bdaaf55cfdf2cf1aca38.rtl.css
  String get a7399Bdaaf55cfdf2cf1aca38Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7399.bdaaf55cfdf2cf1aca38.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7528.79eb8932f26b90cc0746.css
  String get a752879eb8932f26b90cc0746 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7528.79eb8932f26b90cc0746.css';

  /// File path: assets/chart/charting_library/bundles/7528.79eb8932f26b90cc0746.rtl.css
  String get a752879eb8932f26b90cc0746Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7528.79eb8932f26b90cc0746.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7530.fd97c91a6994393f8c34.css
  String get a7530Fd97c91a6994393f8c34 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7530.fd97c91a6994393f8c34.css';

  /// File path: assets/chart/charting_library/bundles/7530.fd97c91a6994393f8c34.rtl.css
  String get a7530Fd97c91a6994393f8c34Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7530.fd97c91a6994393f8c34.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7593.ab709fe310ea76a66dae.js
  String get a7593Ab709fe310ea76a66dae =>
      'packages/e_trader/assets/chart/charting_library/bundles/7593.ab709fe310ea76a66dae.js';

  /// File path: assets/chart/charting_library/bundles/7727.51511f925000b99093e3.css
  String get a772751511f925000b99093e3 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7727.51511f925000b99093e3.css';

  /// File path: assets/chart/charting_library/bundles/7727.51511f925000b99093e3.rtl.css
  String get a772751511f925000b99093e3Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7727.51511f925000b99093e3.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7746.8c7ce523e0bf413e28f8.js
  String get a77468c7ce523e0bf413e28f8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7746.8c7ce523e0bf413e28f8.js';

  /// File path: assets/chart/charting_library/bundles/7769.687807fe02a928ff5a51.css
  String get a7769687807fe02a928ff5a51 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7769.687807fe02a928ff5a51.css';

  /// File path: assets/chart/charting_library/bundles/7769.687807fe02a928ff5a51.rtl.css
  String get a7769687807fe02a928ff5a51Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7769.687807fe02a928ff5a51.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7811.bc4bc25228d45f97e53e.css
  String get a7811Bc4bc25228d45f97e53e =>
      'packages/e_trader/assets/chart/charting_library/bundles/7811.bc4bc25228d45f97e53e.css';

  /// File path: assets/chart/charting_library/bundles/7811.bc4bc25228d45f97e53e.rtl.css
  String get a7811Bc4bc25228d45f97e53eRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7811.bc4bc25228d45f97e53e.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7844.459b24d0a3ebd19f4872.css
  String get a7844459b24d0a3ebd19f4872 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7844.459b24d0a3ebd19f4872.css';

  /// File path: assets/chart/charting_library/bundles/7844.459b24d0a3ebd19f4872.rtl.css
  String get a7844459b24d0a3ebd19f4872Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7844.459b24d0a3ebd19f4872.rtl.css';

  /// File path: assets/chart/charting_library/bundles/7902.bd66a7acb8da83298887.css
  String get a7902Bd66a7acb8da83298887 =>
      'packages/e_trader/assets/chart/charting_library/bundles/7902.bd66a7acb8da83298887.css';

  /// File path: assets/chart/charting_library/bundles/7902.bd66a7acb8da83298887.rtl.css
  String get a7902Bd66a7acb8da83298887Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/7902.bd66a7acb8da83298887.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8065.3a61b8e1d0959436a7ef.css
  String get a80653a61b8e1d0959436a7ef =>
      'packages/e_trader/assets/chart/charting_library/bundles/8065.3a61b8e1d0959436a7ef.css';

  /// File path: assets/chart/charting_library/bundles/8065.3a61b8e1d0959436a7ef.rtl.css
  String get a80653a61b8e1d0959436a7efRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8065.3a61b8e1d0959436a7ef.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8073.4e8847f52f692b35ff45.css
  String get a80734e8847f52f692b35ff45 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8073.4e8847f52f692b35ff45.css';

  /// File path: assets/chart/charting_library/bundles/8073.4e8847f52f692b35ff45.rtl.css
  String get a80734e8847f52f692b35ff45Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8073.4e8847f52f692b35ff45.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8077.abb400ed43eea3166fbf.css
  String get a8077Abb400ed43eea3166fbf =>
      'packages/e_trader/assets/chart/charting_library/bundles/8077.abb400ed43eea3166fbf.css';

  /// File path: assets/chart/charting_library/bundles/8077.abb400ed43eea3166fbf.rtl.css
  String get a8077Abb400ed43eea3166fbfRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8077.abb400ed43eea3166fbf.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8316.7f1805a45329003b0966.css
  String get a83167f1805a45329003b0966 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8316.7f1805a45329003b0966.css';

  /// File path: assets/chart/charting_library/bundles/8316.7f1805a45329003b0966.rtl.css
  String get a83167f1805a45329003b0966Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8316.7f1805a45329003b0966.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8432.c18381bc85b04ca9ccc7.css
  String get a8432C18381bc85b04ca9ccc7 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8432.c18381bc85b04ca9ccc7.css';

  /// File path: assets/chart/charting_library/bundles/8432.c18381bc85b04ca9ccc7.rtl.css
  String get a8432C18381bc85b04ca9ccc7Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8432.c18381bc85b04ca9ccc7.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8467.683a2458d61cd83980b6.css
  String get a8467683a2458d61cd83980b6 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8467.683a2458d61cd83980b6.css';

  /// File path: assets/chart/charting_library/bundles/8467.683a2458d61cd83980b6.rtl.css
  String get a8467683a2458d61cd83980b6Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8467.683a2458d61cd83980b6.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8596.b84315f4350430cdb348.css
  String get a8596B84315f4350430cdb348 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8596.b84315f4350430cdb348.css';

  /// File path: assets/chart/charting_library/bundles/8596.b84315f4350430cdb348.rtl.css
  String get a8596B84315f4350430cdb348Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8596.b84315f4350430cdb348.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8711.68711d7c06b81336aaac.css
  String get a871168711d7c06b81336aaac =>
      'packages/e_trader/assets/chart/charting_library/bundles/8711.68711d7c06b81336aaac.css';

  /// File path: assets/chart/charting_library/bundles/8711.68711d7c06b81336aaac.rtl.css
  String get a871168711d7c06b81336aaacRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8711.68711d7c06b81336aaac.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8722.38f718c32ecc5d2a148c.css
  String get a872238f718c32ecc5d2a148c =>
      'packages/e_trader/assets/chart/charting_library/bundles/8722.38f718c32ecc5d2a148c.css';

  /// File path: assets/chart/charting_library/bundles/8722.38f718c32ecc5d2a148c.rtl.css
  String get a872238f718c32ecc5d2a148cRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8722.38f718c32ecc5d2a148c.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8732.c64d758dad3d1c98dd39.css
  String get a8732C64d758dad3d1c98dd39 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8732.c64d758dad3d1c98dd39.css';

  /// File path: assets/chart/charting_library/bundles/8732.c64d758dad3d1c98dd39.rtl.css
  String get a8732C64d758dad3d1c98dd39Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8732.c64d758dad3d1c98dd39.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8775.2071de405f3a4c584501.css
  String get a87752071de405f3a4c584501 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8775.2071de405f3a4c584501.css';

  /// File path: assets/chart/charting_library/bundles/8775.2071de405f3a4c584501.rtl.css
  String get a87752071de405f3a4c584501Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8775.2071de405f3a4c584501.rtl.css';

  /// File path: assets/chart/charting_library/bundles/8843.79dc7cad0f9ac7a07eb5.css
  String get a884379dc7cad0f9ac7a07eb5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/8843.79dc7cad0f9ac7a07eb5.css';

  /// File path: assets/chart/charting_library/bundles/8843.79dc7cad0f9ac7a07eb5.rtl.css
  String get a884379dc7cad0f9ac7a07eb5Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/8843.79dc7cad0f9ac7a07eb5.rtl.css';

  /// File path: assets/chart/charting_library/bundles/889.21219e9cb2ac6cf1923e.css
  String get a88921219e9cb2ac6cf1923e =>
      'packages/e_trader/assets/chart/charting_library/bundles/889.21219e9cb2ac6cf1923e.css';

  /// File path: assets/chart/charting_library/bundles/889.21219e9cb2ac6cf1923e.rtl.css
  String get a88921219e9cb2ac6cf1923eRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/889.21219e9cb2ac6cf1923e.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9259.dbe21dc892e62e500e95.css
  String get a9259Dbe21dc892e62e500e95 =>
      'packages/e_trader/assets/chart/charting_library/bundles/9259.dbe21dc892e62e500e95.css';

  /// File path: assets/chart/charting_library/bundles/9259.dbe21dc892e62e500e95.rtl.css
  String get a9259Dbe21dc892e62e500e95Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9259.dbe21dc892e62e500e95.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9275.5f5074179a4a1a2fbab9.css
  String get a92755f5074179a4a1a2fbab9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/9275.5f5074179a4a1a2fbab9.css';

  /// File path: assets/chart/charting_library/bundles/9275.5f5074179a4a1a2fbab9.rtl.css
  String get a92755f5074179a4a1a2fbab9Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9275.5f5074179a4a1a2fbab9.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9476.61e084db0b1f1178a85a.css
  String get a947661e084db0b1f1178a85a =>
      'packages/e_trader/assets/chart/charting_library/bundles/9476.61e084db0b1f1178a85a.css';

  /// File path: assets/chart/charting_library/bundles/9476.61e084db0b1f1178a85a.rtl.css
  String get a947661e084db0b1f1178a85aRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9476.61e084db0b1f1178a85a.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9486.8577632fdab29ee53ddf.css
  String get a94868577632fdab29ee53ddf =>
      'packages/e_trader/assets/chart/charting_library/bundles/9486.8577632fdab29ee53ddf.css';

  /// File path: assets/chart/charting_library/bundles/9486.8577632fdab29ee53ddf.rtl.css
  String get a94868577632fdab29ee53ddfRtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9486.8577632fdab29ee53ddf.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9662.03109f673cda5962c847.css
  String get a966203109f673cda5962c847 =>
      'packages/e_trader/assets/chart/charting_library/bundles/9662.03109f673cda5962c847.css';

  /// File path: assets/chart/charting_library/bundles/9662.03109f673cda5962c847.rtl.css
  String get a966203109f673cda5962c847Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9662.03109f673cda5962c847.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9796.0efc740bbb6e80b6cee1.css
  String get a97960efc740bbb6e80b6cee1 =>
      'packages/e_trader/assets/chart/charting_library/bundles/9796.0efc740bbb6e80b6cee1.css';

  /// File path: assets/chart/charting_library/bundles/9796.0efc740bbb6e80b6cee1.rtl.css
  String get a97960efc740bbb6e80b6cee1Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9796.0efc740bbb6e80b6cee1.rtl.css';

  /// File path: assets/chart/charting_library/bundles/9978.48bdee1c05feaec65062.css
  String get a997848bdee1c05feaec65062 =>
      'packages/e_trader/assets/chart/charting_library/bundles/9978.48bdee1c05feaec65062.css';

  /// File path: assets/chart/charting_library/bundles/9978.48bdee1c05feaec65062.rtl.css
  String get a997848bdee1c05feaec65062Rtl =>
      'packages/e_trader/assets/chart/charting_library/bundles/9978.48bdee1c05feaec65062.rtl.css';

  /// File path: assets/chart/charting_library/bundles/EuclidCircular.be8f862db48c2976009f.woff2
  String get euclidCircularBe8f862db48c2976009f =>
      'packages/e_trader/assets/chart/charting_library/bundles/EuclidCircular.be8f862db48c2976009f.woff2';

  /// File path: assets/chart/charting_library/bundles/add-compare-dialog.2cc982d9b1e8e99945fe.js
  String get addCompareDialog2cc982d9b1e8e99945fe =>
      'packages/e_trader/assets/chart/charting_library/bundles/add-compare-dialog.2cc982d9b1e8e99945fe.js';

  /// File path: assets/chart/charting_library/bundles/ar.101.72bd74b7fdc5a07c178b.js
  String get ar10172bd74b7fdc5a07c178b =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.101.72bd74b7fdc5a07c178b.js';

  /// File path: assets/chart/charting_library/bundles/ar.1184.c5f17f9f42004072d84f.js
  String get ar1184C5f17f9f42004072d84f =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.1184.c5f17f9f42004072d84f.js';

  /// File path: assets/chart/charting_library/bundles/ar.1595.4e8ee1f5341933dc2165.js
  String get ar15954e8ee1f5341933dc2165 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.1595.4e8ee1f5341933dc2165.js';

  /// File path: assets/chart/charting_library/bundles/ar.1962.663fd5f1898f9edbcd25.js
  String get ar1962663fd5f1898f9edbcd25 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.1962.663fd5f1898f9edbcd25.js';

  /// File path: assets/chart/charting_library/bundles/ar.2238.115385d01d9d309f8373.js
  String get ar2238115385d01d9d309f8373 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2238.115385d01d9d309f8373.js';

  /// File path: assets/chart/charting_library/bundles/ar.2257.8a5740e6ce979613f4b1.js
  String get ar22578a5740e6ce979613f4b1 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2257.8a5740e6ce979613f4b1.js';

  /// File path: assets/chart/charting_library/bundles/ar.2312.a9353e46c20c3019a091.js
  String get ar2312A9353e46c20c3019a091 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2312.a9353e46c20c3019a091.js';

  /// File path: assets/chart/charting_library/bundles/ar.2364.ca2fb98290b9af9edd4c.js
  String get ar2364Ca2fb98290b9af9edd4c =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2364.ca2fb98290b9af9edd4c.js';

  /// File path: assets/chart/charting_library/bundles/ar.2530.141b9b02516d03268ee2.js
  String get ar2530141b9b02516d03268ee2 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2530.141b9b02516d03268ee2.js';

  /// File path: assets/chart/charting_library/bundles/ar.2646.0e5c56264dcb2375a006.js
  String get ar26460e5c56264dcb2375a006 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2646.0e5c56264dcb2375a006.js';

  /// File path: assets/chart/charting_library/bundles/ar.2831.7b64dd9e84577cc11ab5.js
  String get ar28317b64dd9e84577cc11ab5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2831.7b64dd9e84577cc11ab5.js';

  /// File path: assets/chart/charting_library/bundles/ar.2870.65ad39b5928539d29f33.js
  String get ar287065ad39b5928539d29f33 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.2870.65ad39b5928539d29f33.js';

  /// File path: assets/chart/charting_library/bundles/ar.359.950cbed0e812beac44d9.js
  String get ar359950cbed0e812beac44d9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.359.950cbed0e812beac44d9.js';

  /// File path: assets/chart/charting_library/bundles/ar.3796.785de1d1030dd5d4aa3e.js
  String get ar3796785de1d1030dd5d4aa3e =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.3796.785de1d1030dd5d4aa3e.js';

  /// File path: assets/chart/charting_library/bundles/ar.4040.ba1136e07b4e1fe8feea.js
  String get ar4040Ba1136e07b4e1fe8feea =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.4040.ba1136e07b4e1fe8feea.js';

  /// File path: assets/chart/charting_library/bundles/ar.4109.9fc6a921b69b97da31b8.js
  String get ar41099fc6a921b69b97da31b8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.4109.9fc6a921b69b97da31b8.js';

  /// File path: assets/chart/charting_library/bundles/ar.4166.f8388b9e8fd545e22535.js
  String get ar4166F8388b9e8fd545e22535 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.4166.f8388b9e8fd545e22535.js';

  /// File path: assets/chart/charting_library/bundles/ar.4703.3516cef20b4eaac928c6.js
  String get ar47033516cef20b4eaac928c6 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.4703.3516cef20b4eaac928c6.js';

  /// File path: assets/chart/charting_library/bundles/ar.5683.466462f4d6271f8d13c4.js
  String get ar5683466462f4d6271f8d13c4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.5683.466462f4d6271f8d13c4.js';

  /// File path: assets/chart/charting_library/bundles/ar.5757.bd43099c3855492254a0.js
  String get ar5757Bd43099c3855492254a0 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.5757.bd43099c3855492254a0.js';

  /// File path: assets/chart/charting_library/bundles/ar.6150.6e505059a4769c317b3d.js
  String get ar61506e505059a4769c317b3d =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.6150.6e505059a4769c317b3d.js';

  /// File path: assets/chart/charting_library/bundles/ar.6302.093ac5958c9add03ba99.js
  String get ar6302093ac5958c9add03ba99 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.6302.093ac5958c9add03ba99.js';

  /// File path: assets/chart/charting_library/bundles/ar.6342.c27e9d39485768c198f1.js
  String get ar6342C27e9d39485768c198f1 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.6342.c27e9d39485768c198f1.js';

  /// File path: assets/chart/charting_library/bundles/ar.6703.b71c9c2bcb964acc4d35.js
  String get ar6703B71c9c2bcb964acc4d35 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.6703.b71c9c2bcb964acc4d35.js';

  /// File path: assets/chart/charting_library/bundles/ar.6778.2272d2145f9d59a3f912.js
  String get ar67782272d2145f9d59a3f912 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.6778.2272d2145f9d59a3f912.js';

  /// File path: assets/chart/charting_library/bundles/ar.6822.cf7a18b60df26a835ea8.js
  String get ar6822Cf7a18b60df26a835ea8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.6822.cf7a18b60df26a835ea8.js';

  /// File path: assets/chart/charting_library/bundles/ar.711.2177d12e315b6889d875.js
  String get ar7112177d12e315b6889d875 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.711.2177d12e315b6889d875.js';

  /// File path: assets/chart/charting_library/bundles/ar.7230.e56bc4f0aa99693293b7.js
  String get ar7230E56bc4f0aa99693293b7 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.7230.e56bc4f0aa99693293b7.js';

  /// File path: assets/chart/charting_library/bundles/ar.8066.105eec5eae4e3b3b80c8.js
  String get ar8066105eec5eae4e3b3b80c8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.8066.105eec5eae4e3b3b80c8.js';

  /// File path: assets/chart/charting_library/bundles/ar.8370.ffb31a4ccb6eb8556720.js
  String get ar8370Ffb31a4ccb6eb8556720 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.8370.ffb31a4ccb6eb8556720.js';

  /// File path: assets/chart/charting_library/bundles/ar.8622.3c29960f5935444d279e.js
  String get ar86223c29960f5935444d279e =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.8622.3c29960f5935444d279e.js';

  /// File path: assets/chart/charting_library/bundles/ar.877.0ee92395c6661b759971.js
  String get ar8770ee92395c6661b759971 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.877.0ee92395c6661b759971.js';

  /// File path: assets/chart/charting_library/bundles/ar.9093.58448b805fe08d2b8434.js
  String get ar909358448b805fe08d2b8434 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.9093.58448b805fe08d2b8434.js';

  /// File path: assets/chart/charting_library/bundles/ar.9321.cd0fba64286aeb8f33e9.js
  String get ar9321Cd0fba64286aeb8f33e9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ar.9321.cd0fba64286aeb8f33e9.js';

  /// File path: assets/chart/charting_library/bundles/change-interval-dialog.da81f65148b6614199ad.js
  String get changeIntervalDialogDa81f65148b6614199ad =>
      'packages/e_trader/assets/chart/charting_library/bundles/change-interval-dialog.da81f65148b6614199ad.js';

  /// File path: assets/chart/charting_library/bundles/chart-bottom-toolbar.44c5ff5339e5cebbbf16.js
  String get chartBottomToolbar44c5ff5339e5cebbbf16 =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-bottom-toolbar.44c5ff5339e5cebbbf16.js';

  /// File path: assets/chart/charting_library/bundles/chart-event-hint.f7cb977f0edde0ff7be9.js
  String get chartEventHintF7cb977f0edde0ff7be9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-event-hint.f7cb977f0edde0ff7be9.js';

  /// File path: assets/chart/charting_library/bundles/chart-screenshot-hint.b3f237e44d9bf442a7f7.js
  String get chartScreenshotHintB3f237e44d9bf442a7f7 =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-screenshot-hint.b3f237e44d9bf442a7f7.js';

  /// File path: assets/chart/charting_library/bundles/chart-storage-external-adapter.9c1267d07e48b8d8f53f.js
  String get chartStorageExternalAdapter9c1267d07e48b8d8f53f =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-storage-external-adapter.9c1267d07e48b8d8f53f.js';

  /// File path: assets/chart/charting_library/bundles/chart-storage-library-http.30c4bc9701c3b3a3f587.js
  String get chartStorageLibraryHttp30c4bc9701c3b3a3f587 =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-storage-library-http.30c4bc9701c3b3a3f587.js';

  /// File path: assets/chart/charting_library/bundles/chart-text-editor-renderer.8e4cca2d8edbaa930b6a.js
  String get chartTextEditorRenderer8e4cca2d8edbaa930b6a =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-text-editor-renderer.8e4cca2d8edbaa930b6a.js';

  /// File path: assets/chart/charting_library/bundles/chart-widget-gui.6f7c69eb427cab3bfa2e.js
  String get chartWidgetGui6f7c69eb427cab3bfa2e =>
      'packages/e_trader/assets/chart/charting_library/bundles/chart-widget-gui.6f7c69eb427cab3bfa2e.js';

  /// File path: assets/chart/charting_library/bundles/compare-model.fed2af92643b5038a181.js
  String get compareModelFed2af92643b5038a181 =>
      'packages/e_trader/assets/chart/charting_library/bundles/compare-model.fed2af92643b5038a181.js';

  /// File path: assets/chart/charting_library/bundles/context-menu-renderer.48559dc306537add0a09.js
  String get contextMenuRenderer48559dc306537add0a09 =>
      'packages/e_trader/assets/chart/charting_library/bundles/context-menu-renderer.48559dc306537add0a09.js';

  /// File path: assets/chart/charting_library/bundles/currency-label-menu.1ff82a82705dcb2779fa.js
  String get currencyLabelMenu1ff82a82705dcb2779fa =>
      'packages/e_trader/assets/chart/charting_library/bundles/currency-label-menu.1ff82a82705dcb2779fa.js';

  /// File path: assets/chart/charting_library/bundles/custom-intervals-add-dialog.c6b5b8efd68510b58e58.js
  String get customIntervalsAddDialogC6b5b8efd68510b58e58 =>
      'packages/e_trader/assets/chart/charting_library/bundles/custom-intervals-add-dialog.c6b5b8efd68510b58e58.js';

  /// File path: assets/chart/charting_library/bundles/custom-themes-api.757d290b9817f33f2bd9.js
  String get customThemesApi757d290b9817f33f2bd9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/custom-themes-api.757d290b9817f33f2bd9.js';

  /// File path: assets/chart/charting_library/bundles/dot.3d617b6b01edba83a7f4.cur
  String get dot3d617b6b01edba83a7f4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/dot.3d617b6b01edba83a7f4.cur';

  /// File path: assets/chart/charting_library/bundles/drawing-toolbar.3ba66e94e40268b30c85.js
  String get drawingToolbar3ba66e94e40268b30c85 =>
      'packages/e_trader/assets/chart/charting_library/bundles/drawing-toolbar.3ba66e94e40268b30c85.js';

  /// File path: assets/chart/charting_library/bundles/empty-coin-dark.d6d07bff92d7e4dff5ad.svg
  SvgGenImage get emptyCoinDarkD6d07bff92d7e4dff5ad => const SvgGenImage(
    'assets/chart/charting_library/bundles/empty-coin-dark.d6d07bff92d7e4dff5ad.svg',
  );

  /// File path: assets/chart/charting_library/bundles/empty-coin-light.6d0b731ac6f489f06e65.svg
  SvgGenImage get emptyCoinLight6d0b731ac6f489f06e65 => const SvgGenImage(
    'assets/chart/charting_library/bundles/empty-coin-light.6d0b731ac6f489f06e65.svg',
  );

  /// File path: assets/chart/charting_library/bundles/en.101.72bd74b7fdc5a07c178b.js
  String get en10172bd74b7fdc5a07c178b =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.101.72bd74b7fdc5a07c178b.js';

  /// File path: assets/chart/charting_library/bundles/en.1184.c5f17f9f42004072d84f.js
  String get en1184C5f17f9f42004072d84f =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.1184.c5f17f9f42004072d84f.js';

  /// File path: assets/chart/charting_library/bundles/en.1595.4e8ee1f5341933dc2165.js
  String get en15954e8ee1f5341933dc2165 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.1595.4e8ee1f5341933dc2165.js';

  /// File path: assets/chart/charting_library/bundles/en.1962.663fd5f1898f9edbcd25.js
  String get en1962663fd5f1898f9edbcd25 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.1962.663fd5f1898f9edbcd25.js';

  /// File path: assets/chart/charting_library/bundles/en.2238.115385d01d9d309f8373.js
  String get en2238115385d01d9d309f8373 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2238.115385d01d9d309f8373.js';

  /// File path: assets/chart/charting_library/bundles/en.2257.8a5740e6ce979613f4b1.js
  String get en22578a5740e6ce979613f4b1 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2257.8a5740e6ce979613f4b1.js';

  /// File path: assets/chart/charting_library/bundles/en.2312.a9353e46c20c3019a091.js
  String get en2312A9353e46c20c3019a091 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2312.a9353e46c20c3019a091.js';

  /// File path: assets/chart/charting_library/bundles/en.2364.ca2fb98290b9af9edd4c.js
  String get en2364Ca2fb98290b9af9edd4c =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2364.ca2fb98290b9af9edd4c.js';

  /// File path: assets/chart/charting_library/bundles/en.2530.141b9b02516d03268ee2.js
  String get en2530141b9b02516d03268ee2 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2530.141b9b02516d03268ee2.js';

  /// File path: assets/chart/charting_library/bundles/en.2646.0e5c56264dcb2375a006.js
  String get en26460e5c56264dcb2375a006 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2646.0e5c56264dcb2375a006.js';

  /// File path: assets/chart/charting_library/bundles/en.2831.7b64dd9e84577cc11ab5.js
  String get en28317b64dd9e84577cc11ab5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2831.7b64dd9e84577cc11ab5.js';

  /// File path: assets/chart/charting_library/bundles/en.2870.65ad39b5928539d29f33.js
  String get en287065ad39b5928539d29f33 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.2870.65ad39b5928539d29f33.js';

  /// File path: assets/chart/charting_library/bundles/en.359.950cbed0e812beac44d9.js
  String get en359950cbed0e812beac44d9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.359.950cbed0e812beac44d9.js';

  /// File path: assets/chart/charting_library/bundles/en.3796.785de1d1030dd5d4aa3e.js
  String get en3796785de1d1030dd5d4aa3e =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.3796.785de1d1030dd5d4aa3e.js';

  /// File path: assets/chart/charting_library/bundles/en.4040.ba1136e07b4e1fe8feea.js
  String get en4040Ba1136e07b4e1fe8feea =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.4040.ba1136e07b4e1fe8feea.js';

  /// File path: assets/chart/charting_library/bundles/en.4109.9fc6a921b69b97da31b8.js
  String get en41099fc6a921b69b97da31b8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.4109.9fc6a921b69b97da31b8.js';

  /// File path: assets/chart/charting_library/bundles/en.4166.f8388b9e8fd545e22535.js
  String get en4166F8388b9e8fd545e22535 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.4166.f8388b9e8fd545e22535.js';

  /// File path: assets/chart/charting_library/bundles/en.4703.3516cef20b4eaac928c6.js
  String get en47033516cef20b4eaac928c6 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.4703.3516cef20b4eaac928c6.js';

  /// File path: assets/chart/charting_library/bundles/en.5683.466462f4d6271f8d13c4.js
  String get en5683466462f4d6271f8d13c4 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.5683.466462f4d6271f8d13c4.js';

  /// File path: assets/chart/charting_library/bundles/en.5757.bd43099c3855492254a0.js
  String get en5757Bd43099c3855492254a0 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.5757.bd43099c3855492254a0.js';

  /// File path: assets/chart/charting_library/bundles/en.6150.6e505059a4769c317b3d.js
  String get en61506e505059a4769c317b3d =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.6150.6e505059a4769c317b3d.js';

  /// File path: assets/chart/charting_library/bundles/en.6302.093ac5958c9add03ba99.js
  String get en6302093ac5958c9add03ba99 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.6302.093ac5958c9add03ba99.js';

  /// File path: assets/chart/charting_library/bundles/en.6342.c27e9d39485768c198f1.js
  String get en6342C27e9d39485768c198f1 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.6342.c27e9d39485768c198f1.js';

  /// File path: assets/chart/charting_library/bundles/en.6703.b71c9c2bcb964acc4d35.js
  String get en6703B71c9c2bcb964acc4d35 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.6703.b71c9c2bcb964acc4d35.js';

  /// File path: assets/chart/charting_library/bundles/en.6778.2272d2145f9d59a3f912.js
  String get en67782272d2145f9d59a3f912 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.6778.2272d2145f9d59a3f912.js';

  /// File path: assets/chart/charting_library/bundles/en.6822.cf7a18b60df26a835ea8.js
  String get en6822Cf7a18b60df26a835ea8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.6822.cf7a18b60df26a835ea8.js';

  /// File path: assets/chart/charting_library/bundles/en.711.2177d12e315b6889d875.js
  String get en7112177d12e315b6889d875 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.711.2177d12e315b6889d875.js';

  /// File path: assets/chart/charting_library/bundles/en.7230.e56bc4f0aa99693293b7.js
  String get en7230E56bc4f0aa99693293b7 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.7230.e56bc4f0aa99693293b7.js';

  /// File path: assets/chart/charting_library/bundles/en.8066.105eec5eae4e3b3b80c8.js
  String get en8066105eec5eae4e3b3b80c8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.8066.105eec5eae4e3b3b80c8.js';

  /// File path: assets/chart/charting_library/bundles/en.8370.ffb31a4ccb6eb8556720.js
  String get en8370Ffb31a4ccb6eb8556720 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.8370.ffb31a4ccb6eb8556720.js';

  /// File path: assets/chart/charting_library/bundles/en.8622.3c29960f5935444d279e.js
  String get en86223c29960f5935444d279e =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.8622.3c29960f5935444d279e.js';

  /// File path: assets/chart/charting_library/bundles/en.877.0ee92395c6661b759971.js
  String get en8770ee92395c6661b759971 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.877.0ee92395c6661b759971.js';

  /// File path: assets/chart/charting_library/bundles/en.9093.58448b805fe08d2b8434.js
  String get en909358448b805fe08d2b8434 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.9093.58448b805fe08d2b8434.js';

  /// File path: assets/chart/charting_library/bundles/en.9321.cd0fba64286aeb8f33e9.js
  String get en9321Cd0fba64286aeb8f33e9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/en.9321.cd0fba64286aeb8f33e9.js';

  /// File path: assets/chart/charting_library/bundles/eraser.********************.cur
  String get eraserC80610a04a92d2465b03 =>
      'packages/e_trader/assets/chart/charting_library/bundles/eraser.********************.cur';

  /// File path: assets/chart/charting_library/bundles/export-data.06f5ad74fd75a83a5b96.js
  String get exportData06f5ad74fd75a83a5b96 =>
      'packages/e_trader/assets/chart/charting_library/bundles/export-data.06f5ad74fd75a83a5b96.js';

  /// File path: assets/chart/charting_library/bundles/favorite-drawings-api.ec1abe78f54ce91e86da.js
  String get favoriteDrawingsApiEc1abe78f54ce91e86da =>
      'packages/e_trader/assets/chart/charting_library/bundles/favorite-drawings-api.ec1abe78f54ce91e86da.js';

  /// File path: assets/chart/charting_library/bundles/favorite-indicators.8b28c68ab9b75bd4cffc.js
  String get favoriteIndicators8b28c68ab9b75bd4cffc =>
      'packages/e_trader/assets/chart/charting_library/bundles/favorite-indicators.8b28c68ab9b75bd4cffc.js';

  /// File path: assets/chart/charting_library/bundles/floating-toolbars.9129b5550e965c345e37.js
  String get floatingToolbars9129b5550e965c345e37 =>
      'packages/e_trader/assets/chart/charting_library/bundles/floating-toolbars.9129b5550e965c345e37.js';

  /// File path: assets/chart/charting_library/bundles/full-tooltips-popup.2853535b8ae33d3d848c.js
  String get fullTooltipsPopup2853535b8ae33d3d848c =>
      'packages/e_trader/assets/chart/charting_library/bundles/full-tooltips-popup.2853535b8ae33d3d848c.js';

  /// File path: assets/chart/charting_library/bundles/general-chart-properties-dialog.ae6ea6ac9aefb8257b95.js
  String get generalChartPropertiesDialogAe6ea6ac9aefb8257b95 =>
      'packages/e_trader/assets/chart/charting_library/bundles/general-chart-properties-dialog.ae6ea6ac9aefb8257b95.js';

  /// File path: assets/chart/charting_library/bundles/general-property-page.e2157981df25e8464d9e.js
  String get generalPropertyPageE2157981df25e8464d9e =>
      'packages/e_trader/assets/chart/charting_library/bundles/general-property-page.e2157981df25e8464d9e.js';

  /// File path: assets/chart/charting_library/bundles/get-error-card.60b010a94494ccdc034c.js
  String get getErrorCard60b010a94494ccdc034c =>
      'packages/e_trader/assets/chart/charting_library/bundles/get-error-card.60b010a94494ccdc034c.js';

  /// File path: assets/chart/charting_library/bundles/global-search-dialog.9bd6aafc7993d2f56125.js
  String get globalSearchDialog9bd6aafc7993d2f56125 =>
      'packages/e_trader/assets/chart/charting_library/bundles/global-search-dialog.9bd6aafc7993d2f56125.js';

  /// File path: assets/chart/charting_library/bundles/go-to-date-dialog-impl.a894bb4185dce1f7f39e.js
  String get goToDateDialogImplA894bb4185dce1f7f39e =>
      'packages/e_trader/assets/chart/charting_library/bundles/go-to-date-dialog-impl.a894bb4185dce1f7f39e.js';

  /// File path: assets/chart/charting_library/bundles/hammerjs.6e30e0c48af40bf2f6c0.js
  String get hammerjs6e30e0c48af40bf2f6c0 =>
      'packages/e_trader/assets/chart/charting_library/bundles/hammerjs.6e30e0c48af40bf2f6c0.js';

  /// File path: assets/chart/charting_library/bundles/header-toolbar.509c33f5e1dd207b3f2a.js
  String get headerToolbar509c33f5e1dd207b3f2a =>
      'packages/e_trader/assets/chart/charting_library/bundles/header-toolbar.509c33f5e1dd207b3f2a.js';

  /// File path: assets/chart/charting_library/bundles/ichart-storage.1144e5a1b4f8503ee572.js
  String get ichartStorage1144e5a1b4f8503ee572 =>
      'packages/e_trader/assets/chart/charting_library/bundles/ichart-storage.1144e5a1b4f8503ee572.js';

  /// File path: assets/chart/charting_library/bundles/insert-image-dialog.40902cb87d5d3cbc92e8.js
  String get insertImageDialog40902cb87d5d3cbc92e8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/insert-image-dialog.40902cb87d5d3cbc92e8.js';

  /// File path: assets/chart/charting_library/bundles/library.8fdacc60e5256d6fcc84.js
  String get library8fdacc60e5256d6fcc84 =>
      'packages/e_trader/assets/chart/charting_library/bundles/library.8fdacc60e5256d6fcc84.js';

  /// File path: assets/chart/charting_library/bundles/line-tools-icons.85c5e32f730963cc2d77.js
  String get lineToolsIcons85c5e32f730963cc2d77 =>
      'packages/e_trader/assets/chart/charting_library/bundles/line-tools-icons.85c5e32f730963cc2d77.js';

  /// File path: assets/chart/charting_library/bundles/line-tools-synchronizer.a3d57451eacd53dd89da.js
  String get lineToolsSynchronizerA3d57451eacd53dd89da =>
      'packages/e_trader/assets/chart/charting_library/bundles/line-tools-synchronizer.a3d57451eacd53dd89da.js';

  /// File path: assets/chart/charting_library/bundles/load-chart-dialog.3891627d68b6d6053f24.js
  String get loadChartDialog3891627d68b6d6053f24 =>
      'packages/e_trader/assets/chart/charting_library/bundles/load-chart-dialog.3891627d68b6d6053f24.js';

  /// File path: assets/chart/charting_library/bundles/lollipop-tooltip-renderer.ee88da6f546931c62cb9.js
  String get lollipopTooltipRendererEe88da6f546931c62cb9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/lollipop-tooltip-renderer.ee88da6f546931c62cb9.js';

  /// File path: assets/chart/charting_library/bundles/lt-icons-atlas.3e361230833342ac97b9.js
  String get ltIconsAtlas3e361230833342ac97b9 =>
      'packages/e_trader/assets/chart/charting_library/bundles/lt-icons-atlas.3e361230833342ac97b9.js';

  /// File path: assets/chart/charting_library/bundles/lt-pane-views.57a165725f4ab83a49ed.js
  String get ltPaneViews57a165725f4ab83a49ed =>
      'packages/e_trader/assets/chart/charting_library/bundles/lt-pane-views.57a165725f4ab83a49ed.js';

  /// File path: assets/chart/charting_library/bundles/lt-property-pages-with-definitions.e411878d6fc0600c61b8.js
  String get ltPropertyPagesWithDefinitionsE411878d6fc0600c61b8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/lt-property-pages-with-definitions.e411878d6fc0600c61b8.js';

  /// File path: assets/chart/charting_library/bundles/lt-stickers-atlas.94f0c0b09e739934e586.js
  String get ltStickersAtlas94f0c0b09e739934e586 =>
      'packages/e_trader/assets/chart/charting_library/bundles/lt-stickers-atlas.94f0c0b09e739934e586.js';

  /// File path: assets/chart/charting_library/bundles/manage-drawings-dialog.8170358663062d76c1d5.js
  String get manageDrawingsDialog8170358663062d76c1d5 =>
      'packages/e_trader/assets/chart/charting_library/bundles/manage-drawings-dialog.8170358663062d76c1d5.js';

  /// File path: assets/chart/charting_library/bundles/mock-dark.16b5f3a431f502b03ae3.svg
  SvgGenImage get mockDark16b5f3a431f502b03ae3 => const SvgGenImage(
    'assets/chart/charting_library/bundles/mock-dark.16b5f3a431f502b03ae3.svg',
  );

  /// File path: assets/chart/charting_library/bundles/mock-light.d201313017eb2c1b989f.svg
  SvgGenImage get mockLightD201313017eb2c1b989f => const SvgGenImage(
    'assets/chart/charting_library/bundles/mock-light.d201313017eb2c1b989f.svg',
  );

  /// File path: assets/chart/charting_library/bundles/new-confirm-inputs-dialog.75020f7929f7774b9c4e.js
  String get newConfirmInputsDialog75020f7929f7774b9c4e =>
      'packages/e_trader/assets/chart/charting_library/bundles/new-confirm-inputs-dialog.75020f7929f7774b9c4e.js';

  /// File path: assets/chart/charting_library/bundles/new-edit-object-dialog.cb5864f96bd63e9d2148.js
  String get newEditObjectDialogCb5864f96bd63e9d2148 =>
      'packages/e_trader/assets/chart/charting_library/bundles/new-edit-object-dialog.cb5864f96bd63e9d2148.js';

  /// File path: assets/chart/charting_library/bundles/object-tree-dialog.2447088c66b4ccee845f.js
  String get objectTreeDialog2447088c66b4ccee845f =>
      'packages/e_trader/assets/chart/charting_library/bundles/object-tree-dialog.2447088c66b4ccee845f.js';

  /// File path: assets/chart/charting_library/bundles/opacity-pattern.4d8fbb552dde3db26f4a.svg
  SvgGenImage get opacityPattern4d8fbb552dde3db26f4a => const SvgGenImage(
    'assets/chart/charting_library/bundles/opacity-pattern.4d8fbb552dde3db26f4a.svg',
  );

  /// File path: assets/chart/charting_library/bundles/performance.769cf9dda2ede7d12b74.svg
  SvgGenImage get performance769cf9dda2ede7d12b74 => const SvgGenImage(
    'assets/chart/charting_library/bundles/performance.769cf9dda2ede7d12b74.svg',
  );

  /// File path: assets/chart/charting_library/bundles/prediction-clock-white.c4675d37769f1df4c9ec.png
  AssetGenImage
  get predictionClockWhiteC4675d37769f1df4c9ec => const AssetGenImage(
    'assets/chart/charting_library/bundles/prediction-clock-white.c4675d37769f1df4c9ec.png',
  );

  /// File path: assets/chart/charting_library/bundles/prediction-failure-white.a838a6689f951970e715.png
  AssetGenImage
  get predictionFailureWhiteA838a6689f951970e715 => const AssetGenImage(
    'assets/chart/charting_library/bundles/prediction-failure-white.a838a6689f951970e715.png',
  );

  /// File path: assets/chart/charting_library/bundles/prediction-success-white.2fb9966b4c0f3529a2ea.png
  AssetGenImage
  get predictionSuccessWhite2fb9966b4c0f3529a2ea => const AssetGenImage(
    'assets/chart/charting_library/bundles/prediction-success-white.2fb9966b4c0f3529a2ea.png',
  );

  /// File path: assets/chart/charting_library/bundles/restricted-toolset.0d05314ef639092abfd8.js
  String get restrictedToolset0d05314ef639092abfd8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/restricted-toolset.0d05314ef639092abfd8.js';

  /// File path: assets/chart/charting_library/bundles/runtime.0c59f90a8699f12ff40f.js
  String get runtime0c59f90a8699f12ff40f =>
      'packages/e_trader/assets/chart/charting_library/bundles/runtime.0c59f90a8699f12ff40f.js';

  /// File path: assets/chart/charting_library/bundles/series-icons-map.8c0383b52d89ca46a1b8.js
  String get seriesIconsMap8c0383b52d89ca46a1b8 =>
      'packages/e_trader/assets/chart/charting_library/bundles/series-icons-map.8c0383b52d89ca46a1b8.js';

  /// File path: assets/chart/charting_library/bundles/series-pane-views.3ef313cda7877ec9c336.js
  String get seriesPaneViews3ef313cda7877ec9c336 =>
      'packages/e_trader/assets/chart/charting_library/bundles/series-pane-views.3ef313cda7877ec9c336.js';

  /// File path: assets/chart/charting_library/bundles/share-chart-to-social-utils.e6a25f4006607ca48421.js
  String get shareChartToSocialUtilsE6a25f4006607ca48421 =>
      'packages/e_trader/assets/chart/charting_library/bundles/share-chart-to-social-utils.e6a25f4006607ca48421.js';

  /// File path: assets/chart/charting_library/bundles/show-theme-save-dialog.2efb90ef53bd11686325.js
  String get showThemeSaveDialog2efb90ef53bd11686325 =>
      'packages/e_trader/assets/chart/charting_library/bundles/show-theme-save-dialog.2efb90ef53bd11686325.js';

  /// File path: assets/chart/charting_library/bundles/simple-dialog.caf1cb0bd206c046c24b.js
  String get simpleDialogCaf1cb0bd206c046c24b =>
      'packages/e_trader/assets/chart/charting_library/bundles/simple-dialog.caf1cb0bd206c046c24b.js';

  /// File path: assets/chart/charting_library/bundles/source-properties-editor.2d66a9b78a49ad9d13b0.js
  String get sourcePropertiesEditor2d66a9b78a49ad9d13b0 =>
      'packages/e_trader/assets/chart/charting_library/bundles/source-properties-editor.2d66a9b78a49ad9d13b0.js';

  /// File path: assets/chart/charting_library/bundles/studies.137042b318030eec091b.js
  String get studies137042b318030eec091b =>
      'packages/e_trader/assets/chart/charting_library/bundles/studies.137042b318030eec091b.js';

  /// File path: assets/chart/charting_library/bundles/study-inputs-pane-views.f5305fce3617de1a0299.js
  String get studyInputsPaneViewsF5305fce3617de1a0299 =>
      'packages/e_trader/assets/chart/charting_library/bundles/study-inputs-pane-views.f5305fce3617de1a0299.js';

  /// File path: assets/chart/charting_library/bundles/study-market.06053806bc00e9223e89.js
  String get studyMarket06053806bc00e9223e89 =>
      'packages/e_trader/assets/chart/charting_library/bundles/study-market.06053806bc00e9223e89.js';

  /// File path: assets/chart/charting_library/bundles/study-pane-views.430ecb3d50d053b6e7fa.js
  String get studyPaneViews430ecb3d50d053b6e7fa =>
      'packages/e_trader/assets/chart/charting_library/bundles/study-pane-views.430ecb3d50d053b6e7fa.js';

  /// File path: assets/chart/charting_library/bundles/study-property-pages-with-definitions.2a9e8968482963bc5b1d.js
  String get studyPropertyPagesWithDefinitions2a9e8968482963bc5b1d =>
      'packages/e_trader/assets/chart/charting_library/bundles/study-property-pages-with-definitions.2a9e8968482963bc5b1d.js';

  /// File path: assets/chart/charting_library/bundles/study-template-dialog.78491209cf586d0ba30d.js
  String get studyTemplateDialog78491209cf586d0ba30d =>
      'packages/e_trader/assets/chart/charting_library/bundles/study-template-dialog.78491209cf586d0ba30d.js';

  /// File path: assets/chart/charting_library/bundles/symbol-info-dialog-impl.70ad638f4893ae2c4f86.js
  String get symbolInfoDialogImpl70ad638f4893ae2c4f86 =>
      'packages/e_trader/assets/chart/charting_library/bundles/symbol-info-dialog-impl.70ad638f4893ae2c4f86.js';

  /// File path: assets/chart/charting_library/bundles/symbol-search-dialog.c1573b04ce801649f4ce.js
  String get symbolSearchDialogC1573b04ce801649f4ce =>
      'packages/e_trader/assets/chart/charting_library/bundles/symbol-search-dialog.c1573b04ce801649f4ce.js';

  /// File path: assets/chart/charting_library/bundles/take-chart-image-impl.d04906fb9a536ff5aedf.js
  String get takeChartImageImplD04906fb9a536ff5aedf =>
      'packages/e_trader/assets/chart/charting_library/bundles/take-chart-image-impl.d04906fb9a536ff5aedf.js';

  /// File path: assets/chart/charting_library/bundles/user-defined-bars-marks-tooltip.77d8dc2f2d5b3ab9e50a.js
  String get userDefinedBarsMarksTooltip77d8dc2f2d5b3ab9e50a =>
      'packages/e_trader/assets/chart/charting_library/bundles/user-defined-bars-marks-tooltip.77d8dc2f2d5b3ab9e50a.js';

  /// List of all assets
  List<dynamic> get values => [
    a10427bd6143015a4458ca0eb,
    a105316c0fd7539d08ad5ffd3,
    a105316c0fd7539d08ad5ffd3Rtl,
    a122768542dac293294290d44,
    a122768542dac293294290d44Rtl,
    a1259B80c727a6df944a103fe,
    a1259B80c727a6df944a103feRtl,
    a1298D597f50113da0645dcf5,
    a1298D597f50113da0645dcf5Rtl,
    a13355f0edd452b4452a9eaf4,
    a13355f0edd452b4452a9eaf4Rtl,
    a1398D778724528a9b5665050,
    a1398D778724528a9b5665050Rtl,
    a1538B6bc85c0060285eaeced,
    a1538B6bc85c0060285eaecedRtl,
    a1553C076714f5e24887f0b94,
    a17290f1e7f0f19efe1232ab6,
    a17290f1e7f0f19efe1232ab6Rtl,
    a1782637b414d2efcd03840f5,
    a1782637b414d2efcd03840f5Rtl,
    a20389fc99258845013ecf959,
    a20389fc99258845013ecf959Rtl,
    a2079F2a9f3dd7d4f8cb9f2fc,
    a2106407cb827c0acab444e09,
    a2106407cb827c0acab444e09Rtl,
    a2153302cc6392cc9f067008b,
    a2153302cc6392cc9f067008bRtl,
    a2157D1cab62b805a1a6282a1,
    a21982762b6c7b8aaa0156d33,
    a21982762b6c7b8aaa0156d33Rtl,
    a22082c33dcc3a2ea34861c2c,
    a22082c33dcc3a2ea34861c2cRtl,
    a2227E46f1f2d53203e1fb152,
    a223103e68709eae15d3109a,
    a223103e68709eae15d3109aRtl,
    a2248362fa6a7ab1f3e3b06c4,
    a2248362fa6a7ab1f3e3b06c4Rtl,
    a22641e73b010b4ad6956c0d3,
    a24177835cfcd422c2f0478a4,
    a24177835cfcd422c2f0478a4Rtl,
    a260367a756e92dbe51a30f72,
    a260367a756e92dbe51a30f72Rtl,
    a2751E13accd5da6697245484,
    a276725805ef40a93fc603316,
    a276725805ef40a93fc603316Rtl,
    a28419384677f17d8e3fe6f1e,
    a28419384677f17d8e3fe6f1eRtl,
    a291Cdb1f8bec5b9d4688794,
    a291Cdb1f8bec5b9d4688794Rtl,
    a2950F052bbea83b78b27ffba,
    a2950F052bbea83b78b27ffbaRtl,
    a302F0da356af4ffe12ac66d,
    a302F0da356af4ffe12ac66dRtl,
    a3060Fbb750fd312778403036,
    a3060Fbb750fd312778403036Rtl,
    a3098E317e819fbead3a8b108,
    a311474916a9532052e2cfa84,
    a311474916a9532052e2cfa84Rtl,
    a3204Bd0eb51c8ff7ca736c6b,
    a3204Bd0eb51c8ff7ca736c6bRtl,
    a3322296df13c49b70d3e0af6,
    a3322296df13c49b70d3e0af6Rtl,
    a34435802260e3c522b563151,
    a34639eaf72c1c0a6d8f2cacd,
    a34639eaf72c1c0a6d8f2cacdRtl,
    a350422bc26609f4f438a0ff5,
    a35387894b0f2ada5563099b2,
    a35387894b0f2ada5563099b2Rtl,
    a35472cd7133d1e05f50985a5,
    a35472cd7133d1e05f50985a5Rtl,
    a3610b7d3fd5d9c0b7234428,
    a37622ec6c50ea553cf1e0197,
    a37622ec6c50ea553cf1e0197Rtl,
    a412C71231d81b196034eefa,
    a412C71231d81b196034eefaRtl,
    a4392F247c8bc262c51d15d8e,
    a4392F247c8bc262c51d15d8eRtl,
    a4447F947855b7ae01dfb68f8,
    a4447F947855b7ae01dfb68f8Rtl,
    a4482D8a5a63efb2739dac2bf,
    a4632C0ade5e298b9c20b0703,
    a4632C0ade5e298b9c20b0703Rtl,
    a477494117311dcd036db8fc3,
    a4804E61dfd0736b1e1dc484f,
    a48148f9f36a0b818caadd4c6,
    a48148f9f36a0b818caadd4c6Rtl,
    a4959Bbafa4b076f2c64bd203,
    a4959Bbafa4b076f2c64bd203Rtl,
    a50758c45fa8c1b1852b25a53,
    a53019c3036a1f307fa98f03a,
    a544624be1f27837a64b8646b,
    a544624be1f27837a64b8646bRtl,
    a545011f0aed150d079c71677,
    a54580ad67886dc6df4a03094,
    a54580ad67886dc6df4a03094Rtl,
    a55Fbe45f08e8a580b70f48,
    a55Fbe45f08e8a580b70f48Rtl,
    a551481333b83f4e18e9cde99,
    a551481333b83f4e18e9cde99Rtl,
    a5546681a9e7b91dfc741f79f,
    a5546681a9e7b91dfc741f79fRtl,
    a5622B8f2257b27ac82b2d12e,
    a5622B8f2257b27ac82b2d12eRtl,
    a5666234fdce3b58e2b7f4b38,
    a5666234fdce3b58e2b7f4b38Rtl,
    a57155f6b9b0b26b050b9aaaf,
    a5877E211c1f134e8a786af4f,
    a5877E211c1f134e8a786af4fRtl,
    a588386db8dd61a862770480d,
    a588386db8dd61a862770480dRtl,
    a5922Fbf362211645ecd654fa,
    a5922Fbf362211645ecd654faRtl,
    a601498494a842e9d8e3e6fa4,
    a60857b1bd95c4ea1c9f8ad7a,
    a60857b1bd95c4ea1c9f8ad7aRtl,
    a616420f5b2ff091a8d8a5f,
    a616420f5b2ff091a8d8a5fRtl,
    a6107B8b526751e7230cd2e69,
    a6107B8b526751e7230cd2e69Rtl,
    a6220B02054ace78f1bbd7ab4,
    a6220B02054ace78f1bbd7ab4Rtl,
    a62463ecbd24f95eff1b7dd4f,
    a62463ecbd24f95eff1b7dd4fRtl,
    a640853f95c8edf441cf41f5e,
    a66258ead11e183058d3b8778,
    a66258ead11e183058d3b8778Rtl,
    a66650c4a6582bd484370f525,
    a66650c4a6582bd484370f525Rtl,
    a6955D365d11fe6e348cae0ec,
    a6955D365d11fe6e348cae0ecRtl,
    a7092505a377d19e2d0f1294f,
    a7092505a377d19e2d0f1294fRtl,
    a71Cfe59e9cc92d04ef185d,
    a71Cfe59e9cc92d04ef185dRtl,
    a7125Ee8a75f271c0eade9d69,
    a7125Ee8a75f271c0eade9d69Rtl,
    a7223A5a98f21c4a7b2ef69f7,
    a72416134620811847ea8ea05,
    a72416134620811847ea8ea05Rtl,
    a7346A2efeed47130dd4e832c,
    a73533aff08076e0bff7df116,
    a73533aff08076e0bff7df116Rtl,
    a7399Bdaaf55cfdf2cf1aca38,
    a7399Bdaaf55cfdf2cf1aca38Rtl,
    a752879eb8932f26b90cc0746,
    a752879eb8932f26b90cc0746Rtl,
    a7530Fd97c91a6994393f8c34,
    a7530Fd97c91a6994393f8c34Rtl,
    a7593Ab709fe310ea76a66dae,
    a772751511f925000b99093e3,
    a772751511f925000b99093e3Rtl,
    a77468c7ce523e0bf413e28f8,
    a7769687807fe02a928ff5a51,
    a7769687807fe02a928ff5a51Rtl,
    a7811Bc4bc25228d45f97e53e,
    a7811Bc4bc25228d45f97e53eRtl,
    a7844459b24d0a3ebd19f4872,
    a7844459b24d0a3ebd19f4872Rtl,
    a7902Bd66a7acb8da83298887,
    a7902Bd66a7acb8da83298887Rtl,
    a80653a61b8e1d0959436a7ef,
    a80653a61b8e1d0959436a7efRtl,
    a80734e8847f52f692b35ff45,
    a80734e8847f52f692b35ff45Rtl,
    a8077Abb400ed43eea3166fbf,
    a8077Abb400ed43eea3166fbfRtl,
    a83167f1805a45329003b0966,
    a83167f1805a45329003b0966Rtl,
    a8432C18381bc85b04ca9ccc7,
    a8432C18381bc85b04ca9ccc7Rtl,
    a8467683a2458d61cd83980b6,
    a8467683a2458d61cd83980b6Rtl,
    a8596B84315f4350430cdb348,
    a8596B84315f4350430cdb348Rtl,
    a871168711d7c06b81336aaac,
    a871168711d7c06b81336aaacRtl,
    a872238f718c32ecc5d2a148c,
    a872238f718c32ecc5d2a148cRtl,
    a8732C64d758dad3d1c98dd39,
    a8732C64d758dad3d1c98dd39Rtl,
    a87752071de405f3a4c584501,
    a87752071de405f3a4c584501Rtl,
    a884379dc7cad0f9ac7a07eb5,
    a884379dc7cad0f9ac7a07eb5Rtl,
    a88921219e9cb2ac6cf1923e,
    a88921219e9cb2ac6cf1923eRtl,
    a9259Dbe21dc892e62e500e95,
    a9259Dbe21dc892e62e500e95Rtl,
    a92755f5074179a4a1a2fbab9,
    a92755f5074179a4a1a2fbab9Rtl,
    a947661e084db0b1f1178a85a,
    a947661e084db0b1f1178a85aRtl,
    a94868577632fdab29ee53ddf,
    a94868577632fdab29ee53ddfRtl,
    a966203109f673cda5962c847,
    a966203109f673cda5962c847Rtl,
    a97960efc740bbb6e80b6cee1,
    a97960efc740bbb6e80b6cee1Rtl,
    a997848bdee1c05feaec65062,
    a997848bdee1c05feaec65062Rtl,
    euclidCircularBe8f862db48c2976009f,
    addCompareDialog2cc982d9b1e8e99945fe,
    ar10172bd74b7fdc5a07c178b,
    ar1184C5f17f9f42004072d84f,
    ar15954e8ee1f5341933dc2165,
    ar1962663fd5f1898f9edbcd25,
    ar2238115385d01d9d309f8373,
    ar22578a5740e6ce979613f4b1,
    ar2312A9353e46c20c3019a091,
    ar2364Ca2fb98290b9af9edd4c,
    ar2530141b9b02516d03268ee2,
    ar26460e5c56264dcb2375a006,
    ar28317b64dd9e84577cc11ab5,
    ar287065ad39b5928539d29f33,
    ar359950cbed0e812beac44d9,
    ar3796785de1d1030dd5d4aa3e,
    ar4040Ba1136e07b4e1fe8feea,
    ar41099fc6a921b69b97da31b8,
    ar4166F8388b9e8fd545e22535,
    ar47033516cef20b4eaac928c6,
    ar5683466462f4d6271f8d13c4,
    ar5757Bd43099c3855492254a0,
    ar61506e505059a4769c317b3d,
    ar6302093ac5958c9add03ba99,
    ar6342C27e9d39485768c198f1,
    ar6703B71c9c2bcb964acc4d35,
    ar67782272d2145f9d59a3f912,
    ar6822Cf7a18b60df26a835ea8,
    ar7112177d12e315b6889d875,
    ar7230E56bc4f0aa99693293b7,
    ar8066105eec5eae4e3b3b80c8,
    ar8370Ffb31a4ccb6eb8556720,
    ar86223c29960f5935444d279e,
    ar8770ee92395c6661b759971,
    ar909358448b805fe08d2b8434,
    ar9321Cd0fba64286aeb8f33e9,
    changeIntervalDialogDa81f65148b6614199ad,
    chartBottomToolbar44c5ff5339e5cebbbf16,
    chartEventHintF7cb977f0edde0ff7be9,
    chartScreenshotHintB3f237e44d9bf442a7f7,
    chartStorageExternalAdapter9c1267d07e48b8d8f53f,
    chartStorageLibraryHttp30c4bc9701c3b3a3f587,
    chartTextEditorRenderer8e4cca2d8edbaa930b6a,
    chartWidgetGui6f7c69eb427cab3bfa2e,
    compareModelFed2af92643b5038a181,
    contextMenuRenderer48559dc306537add0a09,
    currencyLabelMenu1ff82a82705dcb2779fa,
    customIntervalsAddDialogC6b5b8efd68510b58e58,
    customThemesApi757d290b9817f33f2bd9,
    dot3d617b6b01edba83a7f4,
    drawingToolbar3ba66e94e40268b30c85,
    emptyCoinDarkD6d07bff92d7e4dff5ad,
    emptyCoinLight6d0b731ac6f489f06e65,
    en10172bd74b7fdc5a07c178b,
    en1184C5f17f9f42004072d84f,
    en15954e8ee1f5341933dc2165,
    en1962663fd5f1898f9edbcd25,
    en2238115385d01d9d309f8373,
    en22578a5740e6ce979613f4b1,
    en2312A9353e46c20c3019a091,
    en2364Ca2fb98290b9af9edd4c,
    en2530141b9b02516d03268ee2,
    en26460e5c56264dcb2375a006,
    en28317b64dd9e84577cc11ab5,
    en287065ad39b5928539d29f33,
    en359950cbed0e812beac44d9,
    en3796785de1d1030dd5d4aa3e,
    en4040Ba1136e07b4e1fe8feea,
    en41099fc6a921b69b97da31b8,
    en4166F8388b9e8fd545e22535,
    en47033516cef20b4eaac928c6,
    en5683466462f4d6271f8d13c4,
    en5757Bd43099c3855492254a0,
    en61506e505059a4769c317b3d,
    en6302093ac5958c9add03ba99,
    en6342C27e9d39485768c198f1,
    en6703B71c9c2bcb964acc4d35,
    en67782272d2145f9d59a3f912,
    en6822Cf7a18b60df26a835ea8,
    en7112177d12e315b6889d875,
    en7230E56bc4f0aa99693293b7,
    en8066105eec5eae4e3b3b80c8,
    en8370Ffb31a4ccb6eb8556720,
    en86223c29960f5935444d279e,
    en8770ee92395c6661b759971,
    en909358448b805fe08d2b8434,
    en9321Cd0fba64286aeb8f33e9,
    eraserC80610a04a92d2465b03,
    exportData06f5ad74fd75a83a5b96,
    favoriteDrawingsApiEc1abe78f54ce91e86da,
    favoriteIndicators8b28c68ab9b75bd4cffc,
    floatingToolbars9129b5550e965c345e37,
    fullTooltipsPopup2853535b8ae33d3d848c,
    generalChartPropertiesDialogAe6ea6ac9aefb8257b95,
    generalPropertyPageE2157981df25e8464d9e,
    getErrorCard60b010a94494ccdc034c,
    globalSearchDialog9bd6aafc7993d2f56125,
    goToDateDialogImplA894bb4185dce1f7f39e,
    hammerjs6e30e0c48af40bf2f6c0,
    headerToolbar509c33f5e1dd207b3f2a,
    ichartStorage1144e5a1b4f8503ee572,
    insertImageDialog40902cb87d5d3cbc92e8,
    library8fdacc60e5256d6fcc84,
    lineToolsIcons85c5e32f730963cc2d77,
    lineToolsSynchronizerA3d57451eacd53dd89da,
    loadChartDialog3891627d68b6d6053f24,
    lollipopTooltipRendererEe88da6f546931c62cb9,
    ltIconsAtlas3e361230833342ac97b9,
    ltPaneViews57a165725f4ab83a49ed,
    ltPropertyPagesWithDefinitionsE411878d6fc0600c61b8,
    ltStickersAtlas94f0c0b09e739934e586,
    manageDrawingsDialog8170358663062d76c1d5,
    mockDark16b5f3a431f502b03ae3,
    mockLightD201313017eb2c1b989f,
    newConfirmInputsDialog75020f7929f7774b9c4e,
    newEditObjectDialogCb5864f96bd63e9d2148,
    objectTreeDialog2447088c66b4ccee845f,
    opacityPattern4d8fbb552dde3db26f4a,
    performance769cf9dda2ede7d12b74,
    predictionClockWhiteC4675d37769f1df4c9ec,
    predictionFailureWhiteA838a6689f951970e715,
    predictionSuccessWhite2fb9966b4c0f3529a2ea,
    restrictedToolset0d05314ef639092abfd8,
    runtime0c59f90a8699f12ff40f,
    seriesIconsMap8c0383b52d89ca46a1b8,
    seriesPaneViews3ef313cda7877ec9c336,
    shareChartToSocialUtilsE6a25f4006607ca48421,
    showThemeSaveDialog2efb90ef53bd11686325,
    simpleDialogCaf1cb0bd206c046c24b,
    sourcePropertiesEditor2d66a9b78a49ad9d13b0,
    studies137042b318030eec091b,
    studyInputsPaneViewsF5305fce3617de1a0299,
    studyMarket06053806bc00e9223e89,
    studyPaneViews430ecb3d50d053b6e7fa,
    studyPropertyPagesWithDefinitions2a9e8968482963bc5b1d,
    studyTemplateDialog78491209cf586d0ba30d,
    symbolInfoDialogImpl70ad638f4893ae2c4f86,
    symbolSearchDialogC1573b04ce801649f4ce,
    takeChartImageImplD04906fb9a536ff5aedf,
    userDefinedBarsMarksTooltip77d8dc2f2d5b3ab9e50a,
  ];
}

class Assets {
  const Assets._();

  static const String package = 'e_trader';

  static const $AssetsChartGen chart = $AssetsChartGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  static const String package = 'e_trader';

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/e_trader/$_assetName';
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'e_trader';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/e_trader/$_assetName';
}
