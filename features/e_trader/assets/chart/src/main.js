// trading_chart.js

import Datafeed from "./datafeed.js";
import {
  delete<PERSON><PERSON>,
  getAllChartsAdapter,
  getSelectedChart,
  saveChart,
  setIdentifier,
} from "./layout_adabter.js";

let widget;

/**
 * Initializes and configures the TradingView chart widget.
 *
 * @param {string} identifier - The trading accountNumber + symbol name.
 * @param {string} symbolName - The trading symbol (e.g., 'BTC/USDT').
 * @param {string} defaultInterval - The default interval (e.g., '5', '30').
 * @param {string[]} disabledFeatures - Features to disable.
 * @param {string} theme - Chart theme (Dark/Light).
 * @param {string} timezone - Timezone identifier (e.g., 'Etc/UTC').
 * @param {number} chartType - Chart type (1: Line, 3: Candles, etc.)
 * @returns {Promise<object>} The initialized TradingView widget instance.
 */

export const startChart = async function (
  identifier,
  symbolName,
  defaultInterval,
  disabledFeatures,
  theme,
  timezone,
  chartType = 1, // Default to Candle if not provided
  locale = "en",
  initialPositionsVisible = true
) {
  let positionsVisible = !!initialPositionsVisible; // ensure boolean
  setIdentifier(identifier);
  widget = new TradingView.widget({
    save_load_adapter: {
      charts: [],
      studyTemplates: [],
      drawingTemplates: [],
      getAllCharts: async function () {
        return await getAllChartsAdapter();
      },
      saveChart: async function (chartData) {
        return await saveChart(
          chartData,
          widget.chart().resolution(),
          symbolName
        );
      },
      removeChart: async function (id) {
        return await deleteChart(id);
      },
      getChartContent: async function (id) {
        return await getSelectedChart(id);
      },
    },
    container: "tv_chart_container",
    symbol: symbolName,
    interval: defaultInterval,
    fullscreen: true,
    autosize: false,
    debug: false,
    theme: theme,
    datafeed: Datafeed,
    library_path: "./charting_library/",
    disabled_features: disabledFeatures,
    enabled_features: [],
    timezone: timezone,
    show_popup_button: true,
    style: chartType,
    locale: locale,
    custom_timezones: [
      {
        id: "Asia/Amman",
        alias: "Etc/GMT-3", // UTC+3 timezone
        title: "Jordan (Amman)",
      },
      {
        id: "Asia/Yerevan",
        alias: "Etc/GMT-4", // UTC+4 timezone
        title: "Armenia (Yerevan)",
      },
      {
        id: "Africa/Nairobi",
        alias: "Etc/GMT-3", // UTC+3 timezone
        title: "Kenya (Nairobi)",
      },
    ],
  });

  await new Promise((resolve) => {
    widget.headerReady().then(function () {
      const button = widget.createButton();
      button.style.padding = "0";
      button.style.border = "none";
      button.style.background = "transparent";

      // Create toggle container
      const toggleContainer = document.createElement("label");
      toggleContainer.style.display = "flex";
      toggleContainer.style.alignItems = "center";
      toggleContainer.style.cursor = "pointer";
      toggleContainer.style.gap = "6px";
      toggleContainer.title = "Show/Hide Positions";

      // Label text
      const label = document.createElement("span");
      label.textContent = "Trades";
      label.style.fontSize = "12px";
      label.style.userSelect = "none";

      // Switch wrapper
      const switchWrapper = document.createElement("div");
      switchWrapper.style.position = "relative";
      switchWrapper.style.width = "36px";
      switchWrapper.style.height = "20px";

      // Hidden checkbox
      const input = document.createElement("input");
      input.type = "checkbox";
      input.checked = positionsVisible;
      input.style.opacity = "0";
      input.style.width = "0";
      input.style.height = "0";

      // Slider visual
      const slider = document.createElement("span");
      slider.style.position = "absolute";
      slider.style.cursor = "pointer";
      slider.style.top = "0";
      slider.style.left = "0";
      slider.style.right = "0";
      slider.style.bottom = "0";
      slider.style.backgroundColor = positionsVisible ? "#4CAF50" : "#ccc";
      slider.style.borderRadius = "20px";
      slider.style.transition = "0.3s";

      // Knob
      const knob = document.createElement("span");
      knob.style.position = "absolute";
      knob.style.height = "14px";
      knob.style.width = "14px";
      knob.style.left = positionsVisible ? "18px" : "3px";
      knob.style.bottom = "3px";
      knob.style.backgroundColor = "#fff";
      knob.style.borderRadius = "50%";
      knob.style.transition = "0.3s";

      slider.appendChild(knob);
      switchWrapper.appendChild(input);
      switchWrapper.appendChild(slider);
      toggleContainer.appendChild(label);
      toggleContainer.appendChild(switchWrapper);
      button.appendChild(toggleContainer);

      // Toggle handler
      input.addEventListener("change", () => {
        positionsVisible = input.checked;

        slider.style.backgroundColor = positionsVisible ? "#4CAF50" : "#ccc";
        knob.style.left = positionsVisible ? "18px" : "3px";

        if (
          window.flutter_inappwebview &&
          window.flutter_inappwebview.callHandler
        ) {
          window.flutter_inappwebview.callHandler(
            "onTogglePositions",
            positionsVisible
          );
        }
      });
    });

    widget.onChartReady(() => {
      widget.applyOverrides({
        "paneProperties.legendProperties.showSeriesOHLC": true,
        "scalesProperties.fontSize": 10,
        "grid.visible": false,
        "mainSeriesProperties.showPriceLine": true,
        "mainSeriesProperties.style": chartType,
      });

      widget.chart().setChartType(chartType);

      // Subscribe to resolution changes
      widget
        .chart()
        .onIntervalChanged()
        .subscribe(null, (interval, obj) => {
          if (
            window.flutter_inappwebview &&
            window.flutter_inappwebview.callHandler
          ) {
            window.flutter_inappwebview.callHandler(
              "onResolutionChanged",
              interval
            );
          }
        });

      // Subscribe to chart type changes
      widget
        .chart()
        .onChartTypeChanged()
        .subscribe(null, (chartType) => {
          if (
            window.flutter_inappwebview &&
            window.flutter_inappwebview.callHandler
          ) {
            window.flutter_inappwebview.callHandler(
              "onChartTypeChanged",
              chartType
            );
          }
        });

      resolve();
    });
  });

  // Notify Flutter that chart loading is complete
  window.flutter_inappwebview.callHandler("onLoadingFinished", "");

  return widget;
};
