// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'event_details_view_data.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Event Details View''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Open news tab''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Open news tab''');
        await theAppIsRendered(tester, EventDetailsViewData());
        await iWait(tester);
        await screenshotVerifiedWithCustomPump(tester, 'event_details_view');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Open news tab''', success);
      }
    });
  });
}
