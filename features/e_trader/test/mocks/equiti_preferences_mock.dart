import 'dart:convert';

import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:preferences/preferences.dart';

class EquitiPreferencesMock extends Mo<PERSON> implements EquitiPreferences {
  void mockGetAccountNumber() {
    when(
      () => this.getValue<String>('accountNumber', ''),
    ).thenReturn('********');
    when(
      () => this.getValue<String>('symbol_sort_order', ''),
    ).thenReturn('Ascending');
    when(
      () => this.getValue<String>('symbol_price_view_type', ''),
    ).thenReturn("buy_sell_view");
    when(
      () => this.setValue<String>('accountNumber', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.setValue<String>('symbol_sort_order', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.setValue<String>('symbol_price_view_type', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<List<String>>('previousSearches', []),
    ).thenReturn(['AAPL', 'TSLA']);
    when(
      () => this.setValue<List<String>>('previousSearches', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<List<String>>('watchlist', any()),
    ).thenReturn(['AAPL', 'TSLA']);
    when(
      () => this.setValue<List<String>>('watchlist', any()),
    ).thenAnswer(((_) async => true));
    when(
      () => this.setValue<String>('watchlist', any()),
    ).thenAnswer((_) async {});
    when(
      () => this.getValue<String>('news_sort_order', any()),
    ).thenReturn('desc');
    when(
      () => this.setValue<String>('news_sort_order', any()),
    ).thenAnswer(((_) async => true));
    when(() => this.getValue<String>('chart_timing', '')).thenReturn('5');
    when(
      () => this.setValue<String>('chart_timing', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<String>('events_sort_order', any()),
    ).thenReturn('desc');
    when(
      () => this.setValue<String>('events_sort_order', any()),
    ).thenAnswer(((_) async => true));
    when(
      () => this.getValue<List<String>>('previousNewsSearches', any()),
    ).thenReturn(['desc']);
    when(
      () => this.setValue<List<String>>('previousNewsSearches', any()),
    ).thenAnswer(((_) async => true));
    when(
      () => this.getValue<List<String>>('previousEventsSearches', any()),
    ).thenReturn(['desc']);
    when(
      () => this.setValue<List<String>>('previousEventsSearches', any()),
    ).thenAnswer(((_) async => true));
    when(
      () => this.setValue<List<String>>('events_sort_order', any()),
    ).thenAnswer(((_) async => true));
    when(
      () => this.getValue<String?>('selectedAccount', null),
    ).thenReturn(_accountModelStr);
    when(
      () => this.setValue<String>('profit-selected-chart', ChartType.bar.name),
    ).thenAnswer(((_) async => true));
    when(
      () => this.getValue('profit-selected-chart', ChartType.bar.name),
    ).thenReturn(ChartType.bar.name);

    // Mock for equity chart preferences
    when(
      () => this.setValue<String>('equity-selected-chart', ChartType.bar.name),
    ).thenAnswer(((_) async => true));
    when(
      () => this.getValue('equity-selected-chart', ChartType.bar.name),
    ).thenReturn(ChartType.bar.name);

    // Mock for volume chart preferences
    when(
      () => this.setValue<String>('volume-selected-chart', ChartType.pie.name),
    ).thenAnswer(((_) async => true));
    when(
      () => this.getValue('volume-selected-chart', ChartType.bar.name),
    ).thenReturn(ChartType.pie.name); // Return the name, not the enum

    when(
      () => this.setValue<String?>(
        'selectedAccountIdForAutoSelectionOnPayments',
        "3f8858d4-e605-09c7-94b2-6459f96e60dc",
      ),
    ).thenAnswer((_) async => true);

    when(
      () => this.getValue<String?>(
        'selectedAccountIdForAutoSelectionOnPayments',
        null,
      ),
    ).thenReturn('3f8858d4-e605-09c7-94b2-6459f96e60dc');

    // Add generic mock for selectedAccountIdForAutoSelectionOnPayments setValue with any value
    when(
      () => this.setValue<String>(
        'selectedAccountIdForAutoSelectionOnPayments',
        any(),
      ),
    ).thenAnswer((_) async => true);
  }

  void mockInterfacePreferences() {
    when(
      () => this.setValue<String>('platform_dark_mode_isOn', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<bool>("platform_dark_mode_isOn", any()),
    ).thenReturn(true);
    when(
      () => this.setValue<String>('platform_haptic_feeback_isOn', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<bool>("platform_haptic_feeback_isOn", any()),
    ).thenReturn(true);
    when(
      () => this.getValue<String>('platform_selected_language', 'english'),
    ).thenReturn('arabic');
    when(
      () => this.setValue<String>('platform_selected_language', any()),
    ).thenAnswer((_) async => true);
  }

  void mockTradingPreferences() {
    when(
      () => getValue<String>('chart_resolution_AUDCAD', any()),
    ).thenReturn('1');
    when(
      () => setValue<String>('chart_resolution_AUDCAD', any()),
    ).thenAnswer((_) async {});
    when(() => getValue<int>('chart_type_AUDCAD', any())).thenReturn(1);
    when(
      () => setValue<int>('chart_type_AUDCAD', any()),
    ).thenAnswer((_) async {});
    when(() => getValue<bool>('toggle_AUDCAD', any())).thenReturn(true);
    when(() => setValue<bool>('toggle_AUDCAD', any())).thenAnswer((_) async {});
    when(
      () => this.setValue<bool>('trader_********_quickTrade_isOn', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<bool>('trader_********_quickTrade_isOn', any()),
    ).thenReturn(true);
    when(
      () => this.setValue<bool>('trader_********_dealSize_isOn', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<bool>('trader_********_dealSize_isOn', any()),
    ).thenReturn(true);
    when(
      () => this.setValue<bool>('trader_********_tps_isOn', any()),
    ).thenAnswer((_) async => true);
    when(
      () => this.getValue<bool>('trader_********_tpsl_isOn', any()),
    ).thenReturn(true);
    when(
      () => this.getValue<String>('trader_********_leverage', '100'),
    ).thenReturn('1:100');
    when(
      () => this.setValue<String>('trader_********_leverage', any()),
    ).thenAnswer((_) async => true);
    when(
      () =>
          this.setValue<bool>('trader_********_close_positions_dialog', any()),
    ).thenAnswer((_) async => true);
    when(
      () =>
          this.getValue<bool>('trader_********_close_positions_dialog', any()),
    ).thenReturn(false);
    when(
      () => this.getValue<String>('chart_resolution', any()),
    ).thenReturn("30");
    when(
      () => this.setValue<String>('chart_resolution', any()),
    ).thenAnswer((_) async => true);
    when(() => this.getValue<int>('chart_type', any())).thenReturn(1);
    when(
      () => this.setValue<String>('chart_type', any()),
    ).thenAnswer((_) async => true);
  }
}

void setupMockEquitiTraderPreferences() {
  final mock = diContainer<EquitiPreferences>() as EquitiPreferencesMock;
  mock.mockInterfacePreferences();
  mock.mockTradingPreferences();
  mock.mockGetAccountNumber();
}

final _accountModelStr = jsonEncode({
  "accountId": "3f8858d4-e605-09c7-94b2-6459f96e60dc",
  "accountStatus": "Active",
  "accountType": "Trading",
  "accountCurrency": "USD",
  "platformAccountNumber": "********",
  "clientId": "17b1a8dc-c160-5b50-0c29-6459f7cab845",
  "accountGroup":
      "EQJO\\Retail\\EX\\Direct\\SE\\USD\\FM_SP_ZZZZ_MU000_C000_0_L_BXC",
  "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558",
  "platformAccountType": "Premiere",
  "name": "Mr Mobile Team Testing",
  "primaryEmail": "<EMAIL>",
  "leverage": 400,
  "serverCode": "dulcimer-demo-01",
  "platformType": "Dulcimer",
  "leadSource": "",
  "balance": 69264.15,
  "margin": 130.35,
  "equity": 69794.85,
  "profit": 530.*************,
  "grossProfit": 530.*************,
  "dateCreated": "2023-05-09T07:33:59",
  "isDemo": false,
  "classification": "DirectClient",
  "accountIdLong": ********,
  "credit": 0,
  "accountCurrencyUsdPair": "",
  "nickName": "Primary Trading",
});
