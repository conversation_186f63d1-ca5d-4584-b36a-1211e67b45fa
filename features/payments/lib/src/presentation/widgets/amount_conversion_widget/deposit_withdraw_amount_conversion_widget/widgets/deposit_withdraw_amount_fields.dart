import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/assets/assets.gen.dart' as payments;
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/thousands_separator_input_formatter.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/debounced_amount_rounder.dart';

import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/bloc/deposit_withdraw_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:prelude/prelude.dart';

class DepositWithdrawAmountFields extends StatefulWidget {
  const DepositWithdrawAmountFields({
    super.key,
    required this.args,
    required this.selectedCurrencyController,
    required this.accountCurrencyController,
  });
  final DepositWithdrawAmountConversionArgs args;
  final TextEditingController selectedCurrencyController;
  final TextEditingController accountCurrencyController;

  @override
  State<DepositWithdrawAmountFields> createState() =>
      _DepositWithdrawAmountFieldsState();
}

class _DepositWithdrawAmountFieldsState
    extends State<DepositWithdrawAmountFields> {
  late DebouncedAmountRounder _accountRounder;
  late DebouncedAmountRounder _selectedRounder;
  String? selectedCurrency;

  @override
  void initState() {
    super.initState();
    // Note: onRoundingComplete callbacks are set up in the build method
    // where we have access to the bloc context
    _accountRounder = DebouncedAmountRounder(
      controller: widget.accountCurrencyController,
    );
    _selectedRounder = DebouncedAmountRounder(
      controller: widget.selectedCurrencyController,
    );
  }

  @override
  void dispose() {
    _accountRounder.dispose();
    _selectedRounder.dispose();
    super.dispose();
  }

  Widget _buildAccountAmountField(
    BuildContext builderContext,
    DepositWithdrawAmountConversionState state,
    EquitiLocalization localization,
    String locale,
    BuildContext context,
  ) {
    final bloc = builderContext.read<DepositWithdrawAmountConversionBloc>();

    // Set up the rounding callback with access to bloc and locale
    _accountRounder.onRoundingComplete = (roundedValue) {
      final isEmpty = roundedValue == 0.0;
      bloc.add(
        DepositWithdrawAmountConversionEvent.onAccountCurrencyAmountChanged(
          amount: roundedValue,
          isEmptyField: isEmpty,
          updatedSelectedCurrencyAmount: (amountInSelectedCurrency) {
            // Apply rounding to callback value
            final rounded = DebouncedAmountRounder.roundToTwoDecimals(
              amountInSelectedCurrency,
            );
            widget.selectedCurrencyController.text =
                EquitiFormatter.formatNumber(value: rounded, locale: locale);
          },
        ),
      );
    };

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        DuploTextField(
          suffixIcon:
              state.doesNeedConversion
                  ? Align(
                    alignment:
                        Directionality.of(context) == TextDirection.ltr
                            ? Alignment.bottomRight
                            : Alignment.bottomLeft,
                    child: DuploTagContainer.md(
                      leading: FlagProvider.getFlagFromCurrencyCode(
                        widget.args.accountCurrency,
                      ),
                      text: widget.args.accountCurrency,
                    ),
                  )
                  : Align(
                    alignment:
                        Directionality.of(context) == TextDirection.ltr
                            ? Alignment.bottomRight
                            : Alignment.bottomLeft,
                    child: DuploTap(
                      key: const Key('transfer_currency_Inkwell'),
                      child: DuploTagContainer.md(
                        leading: FlagProvider.getFlagFromCurrencyCode(
                          widget.args.accountCurrency,
                        ),
                        text: widget.args.accountCurrency,
                        trailing:
                            !widget.args.canChangeConversionCurrency
                                ? null
                                : payments.Assets.images.chevronDown.svg(),
                      ),
                      onTap:
                          !widget.args.canChangeConversionCurrency
                              ? null
                              : () {
                                DuploDropDown.customBottomSheetSelector(
                                  context: builderContext,
                                  bottomSheetTitle:
                                      localization.payments_selectCurrency,
                                  items:
                                      widget.args.currencies
                                          .map(
                                            (element) => DropDownItemModel(
                                              title: element,
                                              image:
                                                  FlagProvider.getFlagFromCurrencyCode(
                                                    element,
                                                  ),
                                            ),
                                          )
                                          .toList(),
                                  selectedIndex: widget.args.currencies
                                      .indexWhere(
                                        (element) =>
                                            element ==
                                            widget.args.accountCurrency,
                                      ),
                                  onChanged: (index) {
                                    selectedCurrency = widget.args.currencies
                                        .elementAtOrNull(index);
                                    widget.accountCurrencyController.clear();
                                    widget.selectedCurrencyController.clear();
                                    assert(
                                      selectedCurrency != null,
                                      'selectedCurrency is null',
                                    );
                                    builderContext
                                        .read<
                                          DepositWithdrawAmountConversionBloc
                                        >()
                                        .add(
                                          DepositWithdrawAmountConversionEvent.getConversionRate(
                                            selectedCurrency: selectedCurrency!,
                                          ),
                                        );
                                  },
                                );
                              },
                    ),
                  ),

          key: const Key('transfer_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: _returnAccountCurrencyFieldLabel(
            paymentType: widget.args.paymentType,
            localization: localization,
            doesNeedConversion: state.doesNeedConversion,
          ),
          hint: _returnAccountCurrencyFieldLabel(
            paymentType: widget.args.paymentType,
            localization: localization,
            doesNeedConversion: state.doesNeedConversion,
          ),
          readOnly: widget.args.isInputDisabled,
          controller: widget.accountCurrencyController,
          scrollPadding: const EdgeInsets.only(bottom: 50),
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          onChanged: (amount) {
            // Only trigger debounced rounding - bloc event will be called from callback
            _accountRounder.onTextChanged();
          },
        ),
      ],
    );
  }

  /// Builds the converted amount field
  Widget _buildSelectedCurrencyAmountField(
    BuildContext builderContext,
    DepositWithdrawAmountConversionState state,
    EquitiLocalization localization,
    String locale,
    BuildContext context,
  ) {
    final bloc = builderContext.read<DepositWithdrawAmountConversionBloc>();

    // Set up the rounding callback with access to bloc and locale
    _selectedRounder.onRoundingComplete = (roundedValue) {
      final isEmpty = roundedValue == 0.0;
      bloc.add(
        DepositWithdrawAmountConversionEvent.onSelectedCurrencyAmountChanged(
          amount: roundedValue,
          isEmptyField: isEmpty,
          updatedAccountCurrencyAmount: (amountInAccountCurrency) {
            // Apply rounding to callback value
            final rounded = DebouncedAmountRounder.roundToTwoDecimals(
              amountInAccountCurrency,
            );
            widget.accountCurrencyController.text =
                EquitiFormatter.formatNumber(value: rounded, locale: locale);
          },
        ),
      );
    };

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        DuploTextField(
          suffixIcon: Align(
            alignment:
                Directionality.of(context) == TextDirection.ltr
                    ? Alignment.bottomRight
                    : Alignment.bottomLeft,
            child: InkWell(
              key: const Key('converted_currency_selector'),
              child: DuploTagContainer.md(
                leading: FlagProvider.getFlagFromCurrencyCode(
                  state.selectedCurrency ?? '',
                ),
                text: state.selectedCurrency ?? '',
                trailing:
                    !widget.args.canChangeConversionCurrency
                        ? null
                        : payments.Assets.images.chevronDown.svg(),
              ),
              onTap:
                  !widget.args.canChangeConversionCurrency
                      ? null
                      : () {
                        DuploDropDown.customBottomSheetSelector(
                          context: builderContext,
                          bottomSheetTitle:
                              localization.payments_selectCurrency,
                          items:
                              widget.args.currencies
                                  .map(
                                    (element) => DropDownItemModel(
                                      title: element,
                                      image:
                                          FlagProvider.getFlagFromCurrencyCode(
                                            element,
                                          ),
                                    ),
                                  )
                                  .toList(),
                          selectedIndex: widget.args.currencies.indexWhere(
                            (element) => element == state.selectedCurrency,
                          ),
                          onChanged: (index) {
                            selectedCurrency = widget.args.currencies
                                .elementAtOrNull(index);
                            //added because when we prefill info and then change currency it doesn't fire callback so getting old value
                            widget.accountCurrencyController.clear();
                            widget.selectedCurrencyController.clear();
                            assert(
                              selectedCurrency != null,
                              'selectedCurrency is null',
                            );
                            bloc.add(
                              DepositWithdrawAmountConversionEvent.getConversionRate(
                                selectedCurrency: selectedCurrency!,
                              ),
                            );
                          },
                        );
                      },
            ),
          ),

          key: const Key('converted_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: _returnSelectedCurrencyFieldLabel(
            localization: localization,
            paymentType: widget.args.paymentType,
          ),
          hint: _returnSelectedCurrencyFieldLabel(
            localization: localization,
            paymentType: widget.args.paymentType,
          ),
          readOnly: widget.args.isInputDisabled,
          controller: widget.selectedCurrencyController,
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          scrollPadding: const EdgeInsets.only(bottom: 50),
          onChanged: (amount) {
            // Only trigger debounced rounding - bloc event will be called from callback
            _selectedRounder.onTextChanged();
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);
    selectedCurrency ??= widget.args.selectedCurrency;

    return BlocBuilder<
      DepositWithdrawAmountConversionBloc,
      DepositWithdrawAmountConversionState
    >(
      buildWhen: (previous, current) => previous != current,
      builder: (blocCtx, state) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: [
                if (state.doesNeedConversion) ...[
                  _buildSelectedCurrencyAmountField(
                    blocCtx,
                    state,
                    localization,
                    locale,
                    context,
                  ),
                  const SizedBox(height: DuploSpacing.spacing_md_8),
                ],
                _buildAccountAmountField(
                  blocCtx,
                  state,
                  localization,
                  locale,
                  context,
                ),
              ],
            ),
            state.doesNeedConversion
                ? Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      DuploRadius.radius_full_9999,
                    ),
                    color: context.duploTheme.background.bgTertiary,
                  ),
                  child: Center(
                    child: payments.Assets.images.refreshCcw.svg(
                      height: 20,
                      width: 20,
                      colorFilter: ColorFilter.mode(
                        context.duploTheme.icon.iconFeaturedLightFgGray,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                )
                : Container(),
          ],
        );
      },
    );
  }

  String _returnSelectedCurrencyFieldLabel({
    required PaymentType paymentType,
    required EquitiLocalization localization,
  }) {
    switch (paymentType) {
      case PaymentType.deposit:
        return localization.payments_toBeDeposited;
      case PaymentType.withdrawal:
        return localization.payments_withdrawalAmount;
      case PaymentType.transfer:
        return localization.payments_convertedAmount;
    }
  }

  String _returnAccountCurrencyFieldLabel({
    required PaymentType paymentType,
    required EquitiLocalization localization,
    required bool doesNeedConversion,
  }) {
    if (doesNeedConversion) {
      switch (paymentType) {
        case PaymentType.deposit:
          return localization.payments_amount;
        case PaymentType.withdrawal:
          return localization.payments_convertedAmount;
        case PaymentType.transfer:
          return localization.payments_transferAmount;
      }
    } else {
      switch (paymentType) {
        case PaymentType.transfer:
          return localization.payments_transferAmount;
        case PaymentType.deposit:
        case PaymentType.withdrawal:
          return localization.payments_addAmount;
      }
    }
  }
}
