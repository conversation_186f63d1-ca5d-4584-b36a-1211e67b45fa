// ignore_for_file: dispose-class-fields

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

/// A helper class that provides debounced rounding functionality for amount input fields.
///
/// This class handles automatic rounding of decimal values to 2 decimal places
/// after a 500ms debounce period when the user stops typing.
///
/// Example usage:
/// ```dart
/// final rounder = DebouncedAmountRounder(
///   controller: myController,
///   onRoundingComplete: (roundedValue) {
///     // This callback is called after rounding is complete
///     // Use this to update bloc or perform other actions with the rounded value
///   },
/// );
/// rounder.onTextChanged(); // Call this in the TextField's onChanged callback
/// rounder.dispose(); // Call this in the widget's dispose method
/// ```
class DebouncedAmountRounder {
  final TextEditingController controller;
  final Duration debounceDuration;

  /// Callback that is called after rounding is complete with the rounded value.
  /// This can be set dynamically to trigger bloc events or other actions.
  void Function(double roundedValue)? onRoundingComplete;

  Timer? _debounceTimer;

  DebouncedAmountRounder({
    required this.controller,
    this.debounceDuration = const Duration(milliseconds: 500),
    this.onRoundingComplete,
  });

  /// Call this method whenever the text field value changes.
  /// It will start/restart the debounce timer.
  void onTextChanged() {
    // Cancel any existing timer
    _debounceTimer?.cancel();

    // Start a new timer
    _debounceTimer = Timer(debounceDuration, () {
      _roundAmount();
    });
  }

  /// Rounds the current text field value to 2 decimal places.
  void _roundAmount() {
    final text = controller.text;

    // If the text is empty, do nothing
    if (text.isEmpty) {
      return onRoundingComplete?.call(0.0);
    }

    // Remove commas before parsing
    final numericValue = text.replaceAll(',', '');

    // Try to parse the value
    final value = double.tryParse(numericValue);

    // If parsing fails or value is null, do nothing
    if (value == null) {
      return;
    }

    // Check if the value has more than 2 decimal places
    final decimalPart = numericValue.split('.').elementAtOrNull(1);
    if (decimalPart == null || decimalPart.length <= 2) {
      // No rounding needed, but still call the callback with the current value
      onRoundingComplete?.call(value);
      return;
    }

    // Round to 2 decimal places
    final rounded = _roundToTwoDecimals(value);

    // Format the rounded value
    final formattedValue = _formatAmount(rounded);

    // Update the text field only if the value changed
    if (formattedValue != text) {
      // Update the text
      controller.text = formattedValue;

      // Try to maintain cursor position at the end
      // This is a simple approach - cursor goes to the end after rounding
      controller.selection = TextSelection.collapsed(
        offset: formattedValue.length,
      );
    }

    // Call the callback with the rounded value
    onRoundingComplete?.call(rounded);
  }

  /// Rounds a double value to 2 decimal places using standard rounding rules.
  double _roundToTwoDecimals(double value) {
    return (value * 100).roundToDouble() / 100;
  }

  /// Public static method to round any value to 2 decimal places.
  /// This can be used in callbacks to ensure consistent rounding.
  static double roundToTwoDecimals(double value) {
    return (value * 100).roundToDouble() / 100;
  }

  /// Formats an amount with thousand separators.
  String _formatAmount(double value) {
    // Split into integer and decimal parts
    final parts = value.toStringAsFixed(2).split('.');
    final integerPart = parts.elementAtOrNull(0) ?? '0';
    final decimalPart = parts.elementAtOrNull(1) ?? '00';

    // Add thousand separators to integer part
    final formattedInteger = _addThousandSeparators(integerPart);

    // Return formatted value
    return '$formattedInteger.$decimalPart';
  }

  /// Adds thousand separators (commas) to a numeric string.
  String _addThousandSeparators(String value) {
    // Remove any existing separators
    value = value.replaceAll(',', '');

    // Add commas
    final regex = RegExp(r'\B(?=(\d{3})+(?!\d))');
    return value.replaceAllMapped(regex, (match) => ',');
  }

  /// Disposes the debounce timer. Call this in the widget's dispose method.
  void dispose() {
    _debounceTimer?.cancel();
  }
}
