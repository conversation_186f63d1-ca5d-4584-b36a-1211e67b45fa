import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';

/// Service responsible for amount conversion calculations
/// Follows Single Responsibility Principle by handling only conversion logic
abstract class AmountConversionService {
  /// Converts amount from target currency to account currency (reversed)
  double getAccountToSelectedCurrencyConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  });

  /// Converts amount from target currency to account currency (reversed)
  double getSelectedCurrencyToAccountConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  });

  /// Converts amount from target currency to account currency (reversed)
  double getSourceToDestinationCurrencyConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  });

  /// Converts amount from target currency to account currency (reversed)
  double getDestinationToSourceConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  });
}

/// Result of amount conversion calculation
class ConversionResult {
  const ConversionResult({required this.convertedAmount, required this.rate});

  final double convertedAmount;
  final double rate;
}

/// Implementation of AmountConversionService
class AmountConversionServiceImpl implements AmountConversionService {
  const AmountConversionServiceImpl();

  @override
  double getAccountToSelectedCurrencyConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  }) {
    if (fromCurrency == null || toCurrency == null) {
      return amount;
    }
    final rateModel = AmountConversionUtils.getSelectedCurrencyRate(
      conversionRateData: conversionRateData,
      selectedCurrency: fromCurrency,
    );
    final rate = rateModel?.rate ?? 1.0;
    return _roundToTwoDecimals(amount / rate);
  }

  @override
  double getSelectedCurrencyToAccountConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  }) {
    if (fromCurrency == null || toCurrency == null) {
      return amount;
    }
    final rateModel = AmountConversionUtils.getSelectedCurrencyRate(
      conversionRateData: conversionRateData,
      selectedCurrency: fromCurrency,
    );
    final rate = rateModel?.rate ?? 1.0;
    return _roundToTwoDecimals(amount * rate);
  }

  @override
  double getDestinationToSourceConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  }) {
    if (fromCurrency == null || toCurrency == null) {
      return amount;
    }
    final rateModel = AmountConversionUtils.getSelectedCurrencyRate(
      conversionRateData: conversionRateData,
      selectedCurrency: fromCurrency,
    );
    final rate = rateModel?.rate ?? 1.0;
    return _roundToTwoDecimals(amount / rate);
  }

  @override
  double getSourceToDestinationCurrencyConvertedAmount({
    required double amount,
    String? fromCurrency,
    String? toCurrency,
    required ConversionRateModel? conversionRateData,
  }) {
    if (fromCurrency == null || toCurrency == null) {
      return amount;
    }
    final rateModel = AmountConversionUtils.getSelectedCurrencyRate(
      conversionRateData: conversionRateData,
      selectedCurrency: fromCurrency,
    );
    final rate = rateModel?.rate ?? 1.0;
    return _roundToTwoDecimals(amount * rate);
  }

  /// Rounds a double value to 2 decimal places using standard rounding rules.
  double _roundToTwoDecimals(double value) {
    // return (value * 100).roundToDouble() / 100;
    return value;
  }
}
