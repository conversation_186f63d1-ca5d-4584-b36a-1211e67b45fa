import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/assets/assets.gen.dart' as payments;
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/thousands_separator_input_formatter.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/debounced_amount_rounder.dart';

import 'package:payment/src/presentation/widgets/amount_conversion_widget/transfer_amount_conversion_widget/bloc/transfer_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/transfer_amount_conversion_widget/transfer_amount_conversion_widget.dart';
import 'package:prelude/prelude.dart';

class TransferAmountFields extends StatefulWidget {
  const TransferAmountFields({
    super.key,
    required this.args,
    required this.destinationCurrencyController,
    required this.sourceCurrencyController,
  });
  final TransferAmountConversionArgs args;
  final TextEditingController destinationCurrencyController;
  final TextEditingController sourceCurrencyController;

  @override
  State<TransferAmountFields> createState() => _TransferAmountFieldsState();
}

class _TransferAmountFieldsState extends State<TransferAmountFields> {
  late DebouncedAmountRounder _sourceRounder;
  late DebouncedAmountRounder _destinationRounder;

  @override
  void initState() {
    super.initState();
    _sourceRounder = DebouncedAmountRounder(
      controller: widget.sourceCurrencyController,
    );
    _destinationRounder = DebouncedAmountRounder(
      controller: widget.destinationCurrencyController,
    );
  }

  @override
  void dispose() {
    _sourceRounder.dispose();
    _destinationRounder.dispose();
    super.dispose();
  }

  Widget _buildSourceAmountField(
    BuildContext builderContext,
    TransferAmountConversionState state,
    EquitiLocalization localization,
    String locale,
    BuildContext context,
  ) {
    final bloc = builderContext.read<TransferAmountConversionBloc>();

    // Set up the rounding callback with access to bloc and locale
    _sourceRounder.onRoundingComplete = (roundedValue) {
      final isEmpty = roundedValue == 0.0;
      bloc.add(
        TransferAmountConversionEvent.onSourceCurrencyAmountChanged(
          amount: roundedValue,
          isEmptyField: isEmpty,
          updatedDestinationCurrencyAmount: (amountInSelectedCurrency) {
            // Apply rounding to callback value
            final rounded = DebouncedAmountRounder.roundToTwoDecimals(
              amountInSelectedCurrency,
            );
            widget
                .destinationCurrencyController
                .text = EquitiFormatter.formatNumber(
              value: rounded,
              locale: locale,
            ).replaceAll(',', "");
          },
        ),
      );
    };

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        DuploTextField(
          key: const Key('transfer_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: localization.payments_transferAmount,
          hint: localization.payments_transferAmount,
          controller: widget.sourceCurrencyController,
          readOnly: widget.args.isInputDisabled,
          scrollPadding: const EdgeInsets.only(bottom: 50),
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          onChanged: (amount) {
            // Only trigger debounced rounding - bloc event will be called from callback
            _sourceRounder.onTextChanged();
          },
        ),
        state.doesNeedConversion
            ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: Align(
                alignment:
                    Directionality.of(context) == TextDirection.ltr
                        ? Alignment.bottomRight
                        : Alignment.bottomLeft,
                child: DuploTagContainer.md(
                  leading: FlagProvider.getFlagFromCurrencyCode(
                    widget.args.sourceCurrency,
                  ),
                  text: widget.args.sourceCurrency,
                ),
              ),
            )
            : Padding(
              padding: const EdgeInsets.all(8.0),
              child: Align(
                alignment:
                    Directionality.of(context) == TextDirection.ltr
                        ? Alignment.bottomRight
                        : Alignment.bottomLeft,
                child: DuploTap(
                  key: const Key('transfer_currency_Inkwell'),
                  child: DuploTagContainer.md(
                    leading: FlagProvider.getFlagFromCurrencyCode(
                      widget.args.sourceCurrency,
                    ),
                    text: widget.args.sourceCurrency,
                    trailing:
                        widget.args.paymentType == PaymentType.transfer
                            ? null
                            : payments.Assets.images.chevronDown.svg(),
                  ),
                ),
              ),
            ),
      ],
    );
  }

  /// Builds the converted amount field
  Widget _buildDestinationAmountField(
    BuildContext builderContext,
    TransferAmountConversionState state,
    EquitiLocalization localization,
    String locale,
    BuildContext context,
  ) {
    final bloc = builderContext.read<TransferAmountConversionBloc>();

    // Set up the rounding callback with access to bloc and locale
    _destinationRounder.onRoundingComplete = (roundedValue) {
      final isEmpty = roundedValue == 0.0;
      bloc.add(
        TransferAmountConversionEvent.onDestinationCurrencyAmountChanged(
          amount: roundedValue,
          isEmptyField: isEmpty,
          updatedSourceCurrencyAmount: (amountInAccountCurrency) {
            // Apply rounding to callback value
            final rounded = DebouncedAmountRounder.roundToTwoDecimals(
              amountInAccountCurrency,
            );
            widget.sourceCurrencyController.text = EquitiFormatter.formatNumber(
              value: rounded,
              locale: locale,
            ).replaceAll(',', "");
          },
        ),
      );
    };

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        DuploTextField(
          key: const Key('converted_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: localization.payments_convertedAmount,
          hint: localization.payments_convertedAmount,
          readOnly: widget.args.isInputDisabled,
          controller: widget.destinationCurrencyController,
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          scrollPadding: const EdgeInsets.only(bottom: 50),
          onChanged: (amount) {
            // Only trigger debounced rounding - bloc event will be called from callback
            _destinationRounder.onTextChanged();
          },
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Align(
            alignment:
                Directionality.of(context) == TextDirection.ltr
                    ? Alignment.bottomRight
                    : Alignment.bottomLeft,
            child: InkWell(
              key: const Key('converted_currency_selector'),
              child: DuploTagContainer.md(
                leading: FlagProvider.getFlagFromCurrencyCode(
                  state.destinationCurrency ?? '',
                ),
                text: state.destinationCurrency ?? '',
                trailing:
                    widget.args.paymentType == PaymentType.transfer
                        ? null
                        : payments.Assets.images.chevronDown.svg(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);

    return BlocBuilder<
      TransferAmountConversionBloc,
      TransferAmountConversionState
    >(
      buildWhen: (previous, current) => previous != current,
      builder: (blocCtx, state) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: [
                _buildSourceAmountField(
                  blocCtx,
                  state,
                  localization,
                  locale,
                  context,
                ),
                if (state.doesNeedConversion) ...[
                  const SizedBox(height: DuploSpacing.spacing_md_8),
                  _buildDestinationAmountField(
                    blocCtx,
                    state,
                    localization,
                    locale,
                    context,
                  ),
                ],
              ],
            ),
            state.doesNeedConversion
                ? Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      DuploRadius.radius_full_9999,
                    ),
                    color: context.duploTheme.background.bgTertiary,
                  ),
                  child: Center(
                    child: payments.Assets.images.refreshCcw.svg(
                      height: 20,
                      width: 20,
                      colorFilter: ColorFilter.mode(
                        context.duploTheme.icon.iconFeaturedLightFgGray,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                )
                : Container(),
          ],
        );
      },
    );
  }
}
