import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

/// Custom tab style widget for account list tabs
class TapBarStyle extends StatelessWidget {
  const TapBarStyle({
    required this.count,
    required this.title,
    required this.isSelected,
    super.key,
  });

  final int count;
  final String title;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);

    return Tab(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: DuploText(
                text: title,
                style: duploTextStyles.textSm,
                fontWeight: DuploFontWeight.semiBold,
                color:
                    isSelected
                        ? theme.utility.utilityGray50
                        : theme.text.textTertiary,
              ),
            ),
          ),
          if (count > 0) ...[
            const SizedBox(width: DuploSpacing.spacing_md_8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                border:
                    isSelected
                        ? Border.all(color: theme.utility.utilityGray700)
                        : Border.all(color: theme.utility.utilityGray200),
                color:
                    isSelected
                        ? theme.utility.utilityGray800
                        : theme.utility.utilityGray50,
                shape: BoxShape.circle,
              ),
              child: DuploText(
                text: EquitiFormatter.formatNumber(
                  value: count,
                  locale: Localizations.localeOf(context).toString(),
                ),
                style: duploTextStyles.textXs,
                fontWeight: DuploFontWeight.medium,
                color:
                    isSelected
                        ? theme.utility.utilityGray50
                        : theme.text.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
