import 'dart:async';
import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment;
import 'package:payment/src/domain/data/lean_bank_status.dart';
import 'package:payment/src/presentation/widgets/lean/models/lean_bank_connection_ui_model.dart';
import 'package:payment/src/presentation/widgets/lean/saved_bank_account_base_chip_parent.dart';
import 'package:prelude/prelude.dart';

class LeanAccountListItem extends StatefulWidget {
  const LeanAccountListItem({
    super.key,
    required this.account,
    required this.selectedAccount,
    required this.onAccountSelected,
    required this.isEditingAccountsMode,
    required this.isBankConnectionDisabled,
    this.onAccountDeleted,
    this.showDeleteOnChild = false,
    this.onCoolOffExpired,
  });
  final LeanAccountUiModel account;
  final LeanAccountUiModel? selectedAccount;
  final VoidCallback onAccountSelected;
  final bool isEditingAccountsMode;
  final bool isBankConnectionDisabled;
  final VoidCallback? onAccountDeleted;
  final bool showDeleteOnChild;

  /// Callback triggered when the cool-off period expires (remaining time reaches 0)
  final VoidCallback? onCoolOffExpired;

  @override
  State<LeanAccountListItem> createState() => _LeanAccountListItemState();
}

class _LeanAccountListItemState extends State<LeanAccountListItem> {
  Timer? _timer;
  String? _remainingTime;
  bool _hasExpired = false;

  @override
  void initState() {
    super.initState();
    // Initialize remaining time without setState in initState
    if (widget.account.coolOffExpiry != null) {
      _remainingTime = _calculateRemainingTime(widget.account.coolOffExpiry!);
      _checkIfExpired(widget.account.coolOffExpiry!);
    }
    _startTimer();
  }

  @override
  void didUpdateWidget(LeanAccountListItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the coolOffExpiry changed, update the remaining time
    if (oldWidget.account.coolOffExpiry != widget.account.coolOffExpiry) {
      if (widget.account.coolOffExpiry != null) {
        _remainingTime = _calculateRemainingTime(widget.account.coolOffExpiry!);
        _hasExpired = false; // Reset expiration flag when coolOffExpiry changes
        _checkIfExpired(widget.account.coolOffExpiry!);
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    // Only start timer if there's a coolOffExpiry
    if (widget.account.coolOffExpiry != null) {
      _timer = Timer.periodic(const Duration(minutes: 1), (_) {
        if (mounted && widget.account.coolOffExpiry != null) {
          setState(() {
            _remainingTime = _calculateRemainingTime(
              widget.account.coolOffExpiry!,
            );
          });
          _checkIfExpired(widget.account.coolOffExpiry!);
        }
      });
    }
  }

  /// Checks if the cool-off period has expired and triggers the callback
  void _checkIfExpired(DateTime coolOffExpiry) {
    // Skip if already expired or if callback is not provided
    if (_hasExpired || widget.onCoolOffExpired == null) {
      return;
    }

    // Only check for accounts with AWAITING_BENEFICIARY_COOL_OFF status
    if (widget.account.status !=
        LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF) {
      return;
    }

    final now = DateTime.now();
    final difference = coolOffExpiry.difference(now);

    // Trigger when the time has expired (difference <= 0) but within the last minute
    // This ensures we only trigger once after the timer reaches 0
    // and not repeatedly for older negative values
    if (difference.inSeconds <= 0) {
      _hasExpired = true;
      // Trigger the callback to refresh accounts
      if (difference.inSeconds > -2) widget.onCoolOffExpired!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final loc = EquitiLocalization.of(context);
    final leanAccountStatus = widget.account.status;
    final isSelected =
        widget.selectedAccount?.accountId == widget.account.accountId;

    return DuploTap(
      onTap: _getTapHandler(),
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 12,
          vertical:
              leanAccountStatus != LeanAccountStatus.ACTIVE &&
                      !widget.isBankConnectionDisabled
                  ? 12
                  : 0,
        ),
        decoration:
            leanAccountStatus != LeanAccountStatus.ACTIVE &&
                    !widget.isBankConnectionDisabled
                ? BoxDecoration(
                  borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
                  color: theme.background.bgSecondary,
                )
                : null,
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_lg_12),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Wrap(
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      runSpacing: 4,
                      spacing: DuploSpacing.spacing_sm_6,
                      children: [
                        if (!widget.isBankConnectionDisabled &&
                            leanAccountStatus == LeanAccountStatus.REAUTHORIZE)
                          CircleAvatar(
                            radius: 12,
                            backgroundColor:
                                theme.background.bgWarningSecondary,
                            child: Assets.images.duploAlertInfo.svg(
                              height: 12,
                              width: 12,
                              colorFilter: ColorFilter.mode(
                                theme.utility.utilityWarning600,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        DuploText(
                          text: "${widget.account.accountName} - ",
                          style: textStyle.textSm,
                          fontWeight: DuploFontWeight.medium,
                          color: theme.text.textSecondary,
                        ),
                        _buildChip(
                          text: widget.account.accountNumber,
                          textStyle: textStyle.textXs,
                          color: theme.utility.utilityGray700,
                        ),
                        _buildChip(
                          text: widget.account.currency,
                          textStyle: textStyle.textXs,
                          color: theme.utility.utilityGray700,
                        ),
                      ],
                    ),
                    if (!widget.isBankConnectionDisabled)
                      switch (leanAccountStatus) {
                        LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF => Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: DuploSpacing.spacing_xs_4),
                            DuploText(
                              text: loc.payments_lean_account_ready_soon(
                                widget.account.currency,
                              ),
                              style: textStyle.textXs,
                              color: theme.text.textSecondary,
                            ),
                            const SizedBox(height: DuploSpacing.spacing_xs_4),
                            if (_remainingTime != null && !_hasExpired)
                              DuploText(
                                text:
                                    '$_remainingTime ${loc.payments_lean_hours_suffix}',
                                style: textStyle.textXs,
                                color: theme.text.textSecondary,
                              ),
                          ],
                        ),
                        LeanAccountStatus.REAUTHORIZE => Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: DuploSpacing.spacing_xs_4),
                            DuploText(
                              text: loc.payments_lean_authorize_account(
                                widget.account.currency,
                              ),
                              style: textStyle.textXs,
                              color: theme.text.textSecondary,
                            ),
                          ],
                        ),
                        (_) => SizedBox.shrink(),
                      },
                  ],
                ),
              ),
              _buildTrailingWidget(
                theme: theme,
                isEditingMode: widget.isEditingAccountsMode,
                leanAccountStatus: leanAccountStatus,
                isSelected: isSelected,
                context: context,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Determines the appropriate tap handler based on current state
  VoidCallback? _getTapHandler() {
    // Disabled bank connections are not tappable
    if (widget.isBankConnectionDisabled ||
        widget.account.status ==
            LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF) {
      return null;
    }

    // In edit mode with delete on children: tap to delete
    if (widget.isEditingAccountsMode &&
        widget.showDeleteOnChild &&
        widget.onAccountDeleted != null) {
      return widget.onAccountDeleted;
    }

    // In edit mode without delete on children: not tappable
    if (widget.isEditingAccountsMode) {
      return null;
    }

    // Normal mode: tap to select account
    return widget.onAccountSelected;
  }

  Widget _buildChip({
    required String text,
    required DuploTextStyle textStyle,
    required Color color,
  }) {
    return text.isEmpty
        ? SizedBox.shrink()
        : SavedBankAccountBaseChipParent(
          child: DuploText(
            text: text,
            style: textStyle,
            fontWeight: DuploFontWeight.medium,
            color: color,
          ),
        );
  }

  Widget _buildTrailingWidget({
    required DuploThemeData theme,
    required bool isEditingMode,
    required LeanAccountStatus leanAccountStatus,
    required bool isSelected,
    required BuildContext context,
  }) {
    // Show delete icon in editing mode if showDeleteOnChild is true
    if (isEditingMode && widget.showDeleteOnChild) {
      return payment.Assets.images.deleteIc.svg(
        height: 20,
        width: 20,
        colorFilter: ColorFilter.mode(
          theme.button.buttonTertiaryErrorFg,
          BlendMode.srcIn,
        ),
      );
    }

    // Don't show any trailing widget in editing mode (for deposit flow)
    if (isEditingMode &&
        leanAccountStatus != LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF) {
      return const SizedBox.shrink();
    }

    if (widget.isBankConnectionDisabled) {
      return payment.Assets.images.ban.svg(height: 16, width: 16);
    }

    switch (leanAccountStatus) {
      case LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF:
        if (_hasExpired) {
          return SizedBox.shrink();
        }
        final isRunningTest = Platform.environment.containsKey('FLUTTER_TEST');

        return SizedBox(
          height: 24,
          width: 24,
          child:
              isRunningTest
                  ? CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation(
                      theme.foreground.fgBrandPrimary,
                    ),
                    value: .5,
                  )
                  : CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation(
                      theme.foreground.fgBrandPrimary,
                    ),
                  ),
        );
      case LeanAccountStatus.REAUTHORIZE:
        return Assets.images
            .chevronRightDirectional(context)
            .svg(height: 24, width: 24);
      case LeanAccountStatus.ACTIVE:
        return (isSelected)
            ? Assets.images.radioBase.svg(height: 24, width: 24)
            : Assets.images.emptyRadioBase.svg(height: 24, width: 24);
      case LeanAccountStatus.UNKNOWN:
        return payment.Assets.images.ban.svg(height: 16, width: 16);
    }
  }

  String _calculateRemainingTime(DateTime coolOffIn) {
    final isRunningTest = Platform.environment.containsKey('FLUTTER_TEST');
    if (isRunningTest) {
      return "00:15";
    }
    final now = DateTime.now();
    final difference = coolOffIn.difference(now);
    final inHours = difference.inHours;
    final inMinutes = difference.inMinutes % 60;
    StringBuffer buffer = StringBuffer();
    // convert 2:2 to 02:02
    if (inHours < 10) {
      buffer.write(
        '0${EquitiFormatter.formatNumber(value: inHours, locale: 'en_US')}',
      );
    } else {
      buffer.write(inHours);
    }
    buffer.write(':');
    if (inMinutes < 10) {
      buffer.write(
        '0${EquitiFormatter.formatNumber(value: inMinutes, locale: 'en_US')}',
      );
    } else {
      buffer.write(inMinutes);
    }
    return buffer.toString();
  }
}
