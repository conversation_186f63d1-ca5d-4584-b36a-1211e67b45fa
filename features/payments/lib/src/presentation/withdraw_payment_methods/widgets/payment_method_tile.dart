import 'dart:io';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:payment/src/presentation/deposit_payment_methods/widgets/currency_container.dart';
import 'package:prelude/prelude.dart';

class PaymentMethodTile extends StatelessWidget {
  const PaymentMethodTile({
    super.key,
    required this.imageUrl,
    required this.currencies,
    required this.name,
    required this.tags,
    required this.fee,
  });
  final String imageUrl;
  final List<String> currencies;
  final String name;
  final List<String> tags;
  final String? fee;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.border.borderSecondary),
      ),
      child: Row(
        children: [
          Platform.environment.containsKey('FLUTTER_TEST')
              ? CircleAvatar(backgroundColor: Colors.red)
              : SvgPicture.network(
                imageUrl,
                width: 48,
                height: 48,
                placeholderBuilder:
                    (_) => Container(
                      width: 48,
                      height: 48,
                      color: theme.border.borderSecondary,
                    ),
              ),
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  DuploText(
                    text: name,
                    style: textStyles.textMd,
                    color: theme.text.textPrimary,
                    fontWeight: DuploFontWeight.semiBold,
                  ),
                  SizedBox(width: 7),
                  for (final tag in tags)
                    Container(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: DuploText(
                          text: tag,
                          color: theme.foreground.fgSuccessPrimary,
                          style: DuploTextStyles.of(context).textXs,
                          fontWeight: DuploFontWeight.medium,
                        ),
                      ),
                      decoration: BoxDecoration(
                        color: theme.utility.utilitySuccess50,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 5),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  fee != null
                      ? CurrencyContainer(
                        text: "${fee} ${localization.payments_fee}",
                      )
                      : Container(),
                  if (currencies.length <= 3)
                    for (String currency in currencies)
                      CurrencyContainer(text: currency),
                  if (currencies.length > 3)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        for (int i = 0; i < 2; i++)
                          CurrencyContainer(
                            text: currencies.elementAtOrNull(i) ?? "",
                          ),
                        CurrencyContainer(
                          text:
                              "+${EquitiFormatter.formatNumber(value: currencies.length - 2, locale: "en")} ${localization.payments_more}",
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
          const Spacer(),
          const Icon(Icons.arrow_forward_ios, size: 16),
          SizedBox(width: 10),
        ],
      ),
    );
  }
}
