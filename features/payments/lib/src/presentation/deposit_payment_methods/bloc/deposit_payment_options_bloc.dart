import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/flags/payment_flags.dart';
import 'package:payment/src/domain/usecase/payment_options_deposit_usecase.dart';
import 'package:payment/src/navigation/arguments/deposit_select_account_args/deposit_select_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:user_account/user_account.dart';

part 'deposit_payment_options_bloc.freezed.dart';
part 'deposit_payment_options_event.dart';
part 'deposit_payment_options_state.dart';

class DepositPaymentOptionsBloc
    extends Bloc<DepositPaymentOptionsEvent, DepositPaymentOptionsState> {
  final PaymentOptionsDepositUsecase _paymentOptionsUsecase;
  final PaymentNavigation _paymentNavigation;
  final ClientProfileUseCase _clientProfileUseCase;
  final DepositAnalyticsEvent _depositAnalyticsEvent;
  final PaymentFlags _paymentFlags;
  DepositPaymentOptionsBloc({
    required PaymentOptionsDepositUsecase paymentOptionsUsecase,
    required PaymentNavigation paymentNavigation,
    required ClientProfileUseCase clientProfileUseCase,
    required DepositAnalyticsEvent depositAnalyticsEvent,
    required PaymentFlags paymentFlags,
  }) : _paymentOptionsUsecase = paymentOptionsUsecase,
       _paymentNavigation = paymentNavigation,
       _clientProfileUseCase = clientProfileUseCase,
       _depositAnalyticsEvent = depositAnalyticsEvent,
       _paymentFlags = paymentFlags,
       super(_DepositPaymentOptionsState()) {
    on<_GetPaymentOptions>(_getPaymentOptions);
    on<_SetDepositFlowConfig>(_setDepositFlowConfig);
    on<_NavigateToSelectAccountAndAmount>(_navigateToSelectAccountAndAmount);
  }

  FutureOr<void> _getPaymentOptions(
    _GetPaymentOptions event,
    Emitter<DepositPaymentOptionsState> emit,
  ) async {
    final clientProfileResult = await _clientProfileUseCase().run();
    ClientProfileData? clientProfileData;
    clientProfileResult.fold(
      (l) {
        print("Error getting client profile $l");
      },
      (r) {
        clientProfileData = r;
      },
    );
    if (clientProfileData == null || clientProfileData?.countryCode == null) {
      emit(
        state.copyWith(
          paymentMethodsProccessState:
              const PaymentMethodsProccessState.error(),
        ),
      );
      addError(Exception('Client profile or Country not found'));
      return;
    }

    final paymentOption =
        await _paymentOptionsUsecase(
          countryCode: clientProfileData!.countryCode!,
        ).run();
    paymentOption.fold(
      (l) {
        emit(
          state.copyWith(
            paymentMethodsProccessState:
                const PaymentMethodsProccessState.error(),
          ),
        );
        addError(l);
      },
      (r) {
        emit(
          state.copyWith(
            paymentMethodsData: r,
            paymentMethodsProccessState:
                const PaymentMethodsProccessState.success(),
          ),
        );
      },
    );
  }

  Future<void> _navigateToSelectAccountAndAmount(
    _NavigateToSelectAccountAndAmount event,
    Emitter<DepositPaymentOptionsState> emit,
  ) async {
    // Extract polling configuration from the payment methods data
    switch (event.paymentMethod.mop) {
      case DepositMop.card:
      case DepositMop.bridgerpay:
      case DepositMop.apple_pay:
      case DepositMop.google_pay:
        final pollingData = state.paymentMethodsData?.data;
        final maxPollingAttempts = pollingData?.maxPollingTime?.toInt();
        final pollingFrequencySeconds = pollingData?.pollingFrequency?.toInt();
        _paymentNavigation.goToDepositSelectAccountAndAmountScreen(
          event.paymentMethod,
          maxPollingAttempts: maxPollingAttempts,
          pollingFrequencySeconds: pollingFrequencySeconds,
          depositFlowConfig: state.depositFlowConfig!,
        );
        break;
      case DepositMop.bank:
        _paymentNavigation.goToDepositSelectBankScreen(
          event.paymentMethodGroup,
        );
        break;
      case DepositMop.lean:

        // Use the new async method to ensure we get the value from Firebase Remote Config
        final isLeanEnabled = await _paymentFlags
            .isLeanPaymentEnabledWhenRemote(
              timeout: const Duration(seconds: 5),
            );
        if (isLeanEnabled) {
          final pollingData = state.paymentMethodsData?.data;
          final maxPollingAttempts = pollingData?.maxPollingTime?.toInt();
          final pollingFrequencySeconds =
              pollingData?.pollingFrequency?.toInt();
          _paymentNavigation.goToDepositSelectLeanAccountScreen(
            DepositSelectAccountArgs(
              paymentMethod: event.paymentMethod,
              depositFlowConfig: state.depositFlowConfig!,
              maxPollingAttempts: maxPollingAttempts,
              pollingFrequencySeconds: pollingFrequencySeconds,
            ),
          );
        } else {
          _paymentNavigation.goToPaymentNotAvailableYetPage();
        }
        break;
      default:
        _depositAnalyticsEvent.depositNotAvailable();
        _paymentNavigation.goToPaymentNotAvailableYetPage();
        break;
    }
  }

  FutureOr<void> _setDepositFlowConfig(
    _SetDepositFlowConfig event,
    Emitter<DepositPaymentOptionsState> emit,
  ) {
    emit(state.copyWith(depositFlowConfig: event.depositFlowConfig));
  }
}
