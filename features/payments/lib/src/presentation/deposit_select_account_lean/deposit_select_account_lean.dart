import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/navigation/arguments/deposit_select_account_args/deposit_select_account_args.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/bloc/deposit_select_account_lean_bloc.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/widgets/lean_account_selection_view.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/widgets/lean_empty_account_view.dart';

class DepositSelectAccountLean extends StatelessWidget {
  const DepositSelectAccountLean({super.key, required this.args});

  final DepositSelectAccountArgs args;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final styles = context.duploTextStyles;
    final theme = context.duploTheme;

    return BlocProvider<DepositSelectAccountLeanBloc>(
      create:
          (_) =>
              diContainer<DepositSelectAccountLeanBloc>()
                ..add(
                  DepositSelectAccountLeanEvent.initArguments(
                    paymentMethod: args.paymentMethod,
                    depositFlowConfig: args.depositFlowConfig,
                    maxPollingAttempts: args.maxPollingAttempts,
                    pollingFrequencySeconds: args.pollingFrequencySeconds,
                  ),
                )
                ..add(const DepositSelectAccountLeanEvent.getLeanAccounts()),
      child: BlocListener<
        DepositSelectAccountLeanBloc,
        DepositSelectAccountLeanState
      >(
        listenWhen:
            (previous, current) =>
                current.currentState != previous.currentState &&
                current.currentState is AccountDeletedSuccessfullyState,
        listener: (listenerContext, state) {
          if (state.currentState is AccountDeletedSuccessfullyState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: DuploText(
                  text: localization.payments_account_deleted_successfully,
                  style: styles.textSm,
                  fontWeight: DuploFontWeight.medium,
                  color: theme.foreground.fgWhite,
                  textAlign: TextAlign.start,
                ),
                showCloseIcon: false,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        child: BlocBuilder<
          DepositSelectAccountLeanBloc,
          DepositSelectAccountLeanState
        >(
          buildWhen:
              (previous, current) =>
                  current.currentState != previous.currentState,
          builder: (blocContext, state) {
            return switch (state.currentState) {
              LoadingState() ||
              AccountDeletedSuccessfullyState() => const LoadingView(),
              LoadedState() => LeanAccountSelectionView(
                paymentMethod: state.depositPaymentMethod!,
              ),
              EmptyState() => LeanEmptyAccountView(
                paymentMethod: state.depositPaymentMethod!,
              ),
              ErrorState() => Scaffold(
                body: Center(
                  child: DuploText(
                    text: 'error while fetching accounts',
                    style: DuploTextStyles.of(context).textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: DuploTheme.of(context).text.textPrimary,
                  ),
                ),
              ),
            };
          },
        ),
      ),
    );
  }
}
