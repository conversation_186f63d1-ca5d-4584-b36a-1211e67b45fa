import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment_assets;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/domain/data/lean_bank_status.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/bloc/deposit_select_account_lean_bloc.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/widgets/lean_payment_source_waiting_view.dart';
import 'package:payment/src/presentation/widgets/lean/lean_payment_source_destination_item.dart';
import 'package:payment/src/presentation/widgets/lean/mappers/lean_payment_source_mappers.dart';
import 'package:payment/src/utils/payment/lean_payment.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:prelude/prelude.dart';

class LeanAccountSelectionView extends StatefulWidget {
  const LeanAccountSelectionView({super.key, required this.paymentMethod});
  final DepositPaymentMethod paymentMethod;

  @override
  State<LeanAccountSelectionView> createState() =>
      _LeanAccountSelectionViewState();
}

class _LeanAccountSelectionViewState extends State<LeanAccountSelectionView> {
  Timer? _refreshDebounceTimer;

  @override
  void dispose() {
    _refreshDebounceTimer?.cancel();
    super.dispose();
  }

  /// Handles cool-off expiration with debouncing to prevent multiple simultaneous refreshes
  void _onCoolOffExpired(BuildContext blocContext) {
    // Cancel any existing timer
    _refreshDebounceTimer?.cancel();

    // Set a new timer with a short delay (e.g., 2 seconds)
    // This ensures that if multiple accounts expire at the same time,
    // only one refresh will be triggered
    _refreshDebounceTimer = Timer(const Duration(seconds: 2), () {
      if (mounted) {
        blocContext.read<DepositSelectAccountLeanBloc>().add(
          const DepositSelectAccountLeanEvent.getLeanAccounts(
            isRefreshing: true,
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final textStyle = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: localization.payments_lean_deposit_with_payment_method(
          widget.paymentMethod.name,
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DuploSpacing.spacing_md_8,
            ),
            child: DuploTap(
              key: const Key('editIcon'),
              onTap: () {
                context.read<DepositSelectAccountLeanBloc>().add(
                  DepositSelectAccountLeanEvent.onEditAccountsModeToggled(),
                );
              },
              child: Container(
                height: 40,
                width: 40,
                child: Center(
                  child: BlocBuilder<
                    DepositSelectAccountLeanBloc,
                    DepositSelectAccountLeanState
                  >(
                    buildWhen:
                        (previous, current) =>
                            current.isEditingAccountsMode !=
                            previous.isEditingAccountsMode,
                    builder: (blocContext, state) {
                      return _buildEditModeIcon(
                        theme,
                        state.isEditingAccountsMode,
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploText(
                        text: localization.payments_saved_bank_accounts,
                        style: textStyle.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      SizedBox(height: DuploSpacing.spacing_3xl_24),
                      BlocBuilder<
                        DepositSelectAccountLeanBloc,
                        DepositSelectAccountLeanState
                      >(
                        buildWhen:
                            (previous, current) =>
                                current.leanCustomerResponse !=
                                    previous.leanCustomerResponse ||
                                current.selectedAccount !=
                                    previous.selectedAccount ||
                                current.isEditingAccountsMode !=
                                    previous.isEditingAccountsMode,
                        builder: (blocCtx, state) {
                          final paymentSourceList =
                              state.leanCustomerResponse?.paymentSourceList ??
                              [];
                          return ListView.separated(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: paymentSourceList.length,
                            separatorBuilder:
                                (_, __) => SizedBox(
                                  height: DuploSpacing.spacing_lg_12,
                                ),
                            itemBuilder: (ctx, index) {
                              final paymentSource = paymentSourceList[index];
                              final bankConnectionUiModel =
                                  paymentSource.toUiModel();
                              final selectedAccountUiModel =
                                  state.selectedAccount?.toUiModel();
                              return LeanPaymentSourceDestinationItem(
                                bankConnection: bankConnectionUiModel,
                                selectedAccount: selectedAccountUiModel,
                                isEditingAccountsMode:
                                    state.isEditingAccountsMode,
                                onAccountSelected: (selectedAccountUi) {
                                  // Find the original LeanAccount by accountId
                                  final originalAccount = paymentSource
                                      .accountList
                                      .firstOrNullWhere(
                                        (account) =>
                                            account.accountId ==
                                            selectedAccountUi.accountId,
                                      );
                                  if (originalAccount != null) {
                                    _onAccountSelected(
                                      originalAccount,
                                      blocCtx,
                                    );
                                  }
                                },
                                onBankConnectionDeleted: () {
                                  _showDeleteAccountBottomSheet(
                                    context,
                                    blocCtx,
                                    paymentSource,
                                  );
                                },
                                onCoolOffExpired:
                                    () => _onCoolOffExpired(blocCtx),
                              );
                            },
                          );
                        },
                      ),
                      BlocBuilder<
                        DepositSelectAccountLeanBloc,
                        DepositSelectAccountLeanState
                      >(
                        buildWhen:
                            (previous, current) =>
                                current.showEmptyPaymentSource !=
                                previous.showEmptyPaymentSource,
                        builder: (blocCtx, state) {
                          if (state.showEmptyPaymentSource) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: DuploSpacing.spacing_3xl_24,
                              ),
                              child: LeanPaymentSourceWaitingView(),
                            );
                          }
                          return SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                ),
              ),

              BlocBuilder<
                DepositSelectAccountLeanBloc,
                DepositSelectAccountLeanState
              >(
                buildWhen:
                    (previous, current) =>
                        current.selectedAccount != previous.selectedAccount ||
                        current.isEditingAccountsMode !=
                            previous.isEditingAccountsMode ||
                        current.showRefreshIcon != previous.showRefreshIcon ||
                        current.leanCustomerResponse !=
                            previous.leanCustomerResponse ||
                        current.isAddBankAccountDisabled !=
                            previous.isAddBankAccountDisabled,
                builder: (blocContext, state) {
                  final bloc = blocContext.read<DepositSelectAccountLeanBloc>();
                  return Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: DuploSpacing.spacing_xl_16,
                        ),
                        child: DuploButton.tertiary(
                          useFullWidth: true,
                          title: localization.payments_add_new_account,
                          leadingIcon: Assets.images.plus.keyName,
                          isDisabled: state.isAddBankAccountDisabled,
                          onTap: () {
                            blocContext.read<DepositSelectAccountLeanBloc>().add(
                              const DepositSelectAccountLeanEvent.onAddAccountPressed(),
                            );
                          },
                        ),
                      ),
                      if (!state.isEditingAccountsMode)
                        DuploButton.defaultPrimary(
                          useFullWidth: true,
                          title: _getPrimaryButtonTitle(localization, bloc),
                          isDisabled: _isContinueButtonDisabled(state),
                          trailingIcon: getPrimaryButtonIcon(bloc),
                          onTap: () => _onPrimaryButtonPressed(bloc),
                        ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteAccountBottomSheet(
    BuildContext context,
    BuildContext blocContext,
    PaymentSource paymentSource,
  ) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;

    DuploErrorSheet.show<void>(
      context: context,
      bodyTitle: localization.payments_delete_lean_account_title,
      bodySubTitle: localization.payments_delete_lean_account_description,
      hideCloseButton: true,
      textAlign: TextAlign.center,
      // crossAxisAlignment: CrossAxisAlignment.start,
      customIcon: payment_assets.Assets.images.deleteAccountIc.svg(
        width: 160,
        height: 160,
      ),
      actionWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: DuploSpacing.spacing_3xl_24),
          DuploText(
            text: localization.payments_lean_account_nickname,
            style: textStyle.textMd,
            color: theme.text.textPrimary,
            fontWeight: DuploFontWeight.semiBold,
          ),
          SizedBox(height: DuploSpacing.spacing_xxs_2),
          DuploText(
            text: paymentSource.bankIdentifier,
            style: textStyle.textXs,
            color: theme.text.textTertiary,
            fontWeight: DuploFontWeight.regular,
          ),
          SizedBox(height: DuploSpacing.spacing_4xl_32),
          DuploButton.sellPrimary(
            title: localization.payments_delete_lean_account_button,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
              blocContext.read<DepositSelectAccountLeanBloc>().add(
                DepositSelectAccountLeanEvent.onLeanPaymentSourceDeleted(
                  paymentSource: paymentSource,
                ),
              );
            },
          ),
          SizedBox(height: DuploSpacing.spacing_md_8),
          DuploButton.secondary(
            title: localization.payments_cancel,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _onAccountSelected(LeanAccount selectedAccount, BuildContext blocCtx) {
    final isAuthorizationRequiredOnAccount =
        selectedAccount.status == LeanAccountStatus.REAUTHORIZE;

    if (!isAuthorizationRequiredOnAccount) {
      // if auth is not required select/unselect that account
      blocCtx.read<DepositSelectAccountLeanBloc>().add(
        DepositSelectAccountLeanEvent.onLeanBankAccountSelected(
          selectedAccount: selectedAccount,
        ),
      );
    } else {
      // Authentication is required, show bottom sheet with Lean connect flow
      _showLeanAuthenticationBottomSheet(blocCtx, selectedAccount);
    }
  }

  void _showLeanAuthenticationBottomSheet(
    BuildContext blocCtx,
    LeanAccount selectedAccount,
  ) {
    final analytics = diContainer<DepositAnalyticsEvent>();
    final theme = blocCtx.duploTheme;
    final bloc = blocCtx.read<DepositSelectAccountLeanBloc>();

    // Find the parent payment source to get the bank identifier
    final paymentSource = bloc.state.leanCustomerResponse?.paymentSourceList
        .firstOrNullWhere(
          (source) => source.accountList.any(
            (account) => account.id == selectedAccount.id,
          ),
        );

    // Show bottom sheet immediately
    DuploSheet.showNonScrollableModalSheet<bool>(
          context: blocCtx,
          hasTopBarLayer: false,
          useSafeArea: true,
          backgroundColor: blocCtx.duploTheme.background.bgPrimary,
          hasTrailingIc: false,
          showDragHandle: false,
          content: (sheetContext) {
            return SizedBox(
              height: MediaQuery.sizeOf(blocCtx).height * .7,
              child: BlocBuilder<
                DepositSelectAccountLeanBloc,
                DepositSelectAccountLeanState
              >(
                bloc: bloc,
                buildWhen:
                    (previous, current) =>
                        previous.isLoadingLeanConfig !=
                            current.isLoadingLeanConfig ||
                        previous.leanConfigResponse !=
                            current.leanConfigResponse ||
                        previous.leanConfigError != current.leanConfigError,
                builder: (builderContext, state) {
                  if (state.isLoadingLeanConfig) {
                    return LoadingView();
                  }

                  // Show error if config fetch failed
                  if (state.leanConfigError != null || paymentSource == null) {
                    final localization = EquitiLocalization.of(blocCtx);
                    return Container(
                      height: MediaQuery.sizeOf(blocCtx).height * .7,
                      color: theme.background.bgPrimary,
                      child: Center(
                        child: DuploText(
                          text:
                              localization.payments_lean_error_fetching_config,
                          style: blocCtx.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    );
                  }

                  // Show Lean connect widget when config is ready
                  final leanConfigResponse = state.leanConfigResponse;
                  if (leanConfigResponse == null) {
                    return LoadingView();
                  }

                  // Find the matching deposit config by currency
                  final depositConfig = leanConfigResponse
                      .data
                      .depositConfigList
                      .firstOrNullWhere(
                        (config) => config.currency == selectedAccount.currency,
                      );

                  // If no matching config found, show error
                  if (depositConfig == null) {
                    final localization = EquitiLocalization.of(blocCtx);
                    return Container(
                      height: MediaQuery.sizeOf(blocCtx).height * .7,
                      color: theme.background.bgPrimary,
                      child: Center(
                        child: DuploText(
                          text: localization
                              .payments_lean_no_payment_destination(
                                selectedAccount.currency,
                              ),
                          style: blocCtx.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    );
                  }

                  return LeanPayment.createBeneficiary(
                    context: blocCtx,
                    appToken: leanConfigResponse.data.appToken,
                    customerId: leanConfigResponse.data.customerId,
                    paymentDestinationId: depositConfig.destinationId,
                    paymentSourceId: paymentSource.id,
                    accessToken: leanConfigResponse.data.customerToken,
                    customization: LeanPayment.returnLeanCustomization(
                      context: blocCtx,
                    ),
                  );
                },
              ),
            );
          },
        )
        .then((value) {
          if (value == true) {
            bloc.add(const DepositSelectAccountLeanEvent.getLeanAccounts());
          }
        })
        .catchError((Object e) {
          analytics.depositLeanConnectError(errorMessage: e.toString());
        });

    // Dispatch event to fetch Lean config
    bloc.add(DepositSelectAccountLeanEvent.getLeanConfig());
  }

  /// Builds the appropriate icon for edit mode toggle button
  /// Shows close icon when in edit mode, edit icon otherwise
  Widget _buildEditModeIcon(DuploThemeData theme, bool isEditingMode) {
    if (isEditingMode) {
      // Show close icon in edit mode
      return Assets.images.closeIc.svg(
        width: 16,
        height: 16,
        colorFilter: ColorFilter.mode(
          theme.foreground.fgSecondary,
          BlendMode.srcIn,
        ),
      );
    }

    // Show edit icon in normal mode
    return payment_assets.Assets.images.editIcon.svg(
      width: 22,
      height: 22,
      colorFilter: ColorFilter.mode(
        theme.foreground.fgSecondary,
        BlendMode.srcIn,
      ),
    );
  }

  /// Determines if the continue button should be disabled
  /// Button is disabled when no account is selected or in edit mode
  bool _isContinueButtonDisabled(DepositSelectAccountLeanState state) {
    // If refresh button is visible, enable the continue button
    if (_isRefreshButtonVisible(state)) {
      return false;
    }

    // Disable if no account selected
    if (state.selectedAccount == null) {
      return true;
    }

    // Disable if in edit mode
    if (state.isEditingAccountsMode) {
      return true;
    }

    return false;
  }

  bool _isRefreshButtonVisible(DepositSelectAccountLeanState state) {
    final isAnyAccountInProgress =
        state.leanCustomerResponse?.paymentSourceList.any(
          (ps) => ps.status == LeanBankStatus.IN_PROGRESS,
        ) ??
        false;
    final isAnySubAccountInAwaitingBeneficiary =
        state.leanCustomerResponse?.paymentSourceList.any(
          (ps) => ps.accountList.any(
            (account) =>
                account.status ==
                LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF,
          ),
        ) ??
        false;
    return (isAnyAccountInProgress || isAnySubAccountInAwaitingBeneficiary) &&
        state.selectedAccount == null;
  }

  String _getPrimaryButtonTitle(
    EquitiLocalization localization,
    DepositSelectAccountLeanBloc bloc,
  ) {
    if (_isRefreshButtonVisible(bloc.state)) {
      return localization.payments_refresh;
    }
    return localization.payments_continue;
  }

  String? getPrimaryButtonIcon(DepositSelectAccountLeanBloc bloc) {
    if (_isRefreshButtonVisible(bloc.state)) {
      return null;
    }
    return Assets.images.chevronRight.keyName;
  }

  void _onPrimaryButtonPressed(DepositSelectAccountLeanBloc bloc) {
    if (_isRefreshButtonVisible(bloc.state)) {
      bloc.add(const DepositSelectAccountLeanEvent.onRefreshIconPressed());
    } else {
      bloc.add(const DepositSelectAccountLeanEvent.onConfirmButtonPressed());
    }
  }
}
