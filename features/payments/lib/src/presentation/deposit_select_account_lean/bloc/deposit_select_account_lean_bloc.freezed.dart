// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_select_account_lean_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositSelectAccountLeanEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositSelectAccountLeanEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositSelectAccountLeanEvent()';
}


}

/// @nodoc
class $DepositSelectAccountLeanEventCopyWith<$Res>  {
$DepositSelectAccountLeanEventCopyWith(DepositSelectAccountLeanEvent _, $Res Function(DepositSelectAccountLeanEvent) __);
}


/// @nodoc


class _InitArgumentsEvent implements DepositSelectAccountLeanEvent {
  const _InitArgumentsEvent({required this.paymentMethod, required this.depositFlowConfig, this.maxPollingAttempts, this.pollingFrequencySeconds});
  

 final  DepositPaymentMethod paymentMethod;
 final  DepositFlowConfig depositFlowConfig;
 final  num? maxPollingAttempts;
 final  num? pollingFrequencySeconds;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InitArgumentsEventCopyWith<_InitArgumentsEvent> get copyWith => __$InitArgumentsEventCopyWithImpl<_InitArgumentsEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InitArgumentsEvent&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethod,depositFlowConfig,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.initArguments(paymentMethod: $paymentMethod, depositFlowConfig: $depositFlowConfig, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class _$InitArgumentsEventCopyWith<$Res> implements $DepositSelectAccountLeanEventCopyWith<$Res> {
  factory _$InitArgumentsEventCopyWith(_InitArgumentsEvent value, $Res Function(_InitArgumentsEvent) _then) = __$InitArgumentsEventCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethod paymentMethod, DepositFlowConfig depositFlowConfig, num? maxPollingAttempts, num? pollingFrequencySeconds
});


$DepositPaymentMethodCopyWith<$Res> get paymentMethod;$DepositFlowConfigCopyWith<$Res> get depositFlowConfig;

}
/// @nodoc
class __$InitArgumentsEventCopyWithImpl<$Res>
    implements _$InitArgumentsEventCopyWith<$Res> {
  __$InitArgumentsEventCopyWithImpl(this._self, this._then);

  final _InitArgumentsEvent _self;
  final $Res Function(_InitArgumentsEvent) _then;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentMethod = null,Object? depositFlowConfig = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_InitArgumentsEvent(
paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod,depositFlowConfig: null == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res> get paymentMethod {
  
  return $DepositPaymentMethodCopyWith<$Res>(_self.paymentMethod, (value) {
    return _then(_self.copyWith(paymentMethod: value));
  });
}/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res> get depositFlowConfig {
  
  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}
}

/// @nodoc


class _OnConfirmButtonPressedEvent implements DepositSelectAccountLeanEvent {
  const _OnConfirmButtonPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnConfirmButtonPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.onConfirmButtonPressed()';
}


}




/// @nodoc


class _OnAddAccountPressedEvent implements DepositSelectAccountLeanEvent {
  const _OnAddAccountPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAddAccountPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.onAddAccountPressed()';
}


}




/// @nodoc


class _GetLeanAccountsEvent implements DepositSelectAccountLeanEvent {
  const _GetLeanAccountsEvent({this.isRefreshing, this.isPolling, this.pollingAttempt});
  

 final  bool? isRefreshing;
 final  bool? isPolling;
 final  int? pollingAttempt;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetLeanAccountsEventCopyWith<_GetLeanAccountsEvent> get copyWith => __$GetLeanAccountsEventCopyWithImpl<_GetLeanAccountsEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetLeanAccountsEvent&&(identical(other.isRefreshing, isRefreshing) || other.isRefreshing == isRefreshing)&&(identical(other.isPolling, isPolling) || other.isPolling == isPolling)&&(identical(other.pollingAttempt, pollingAttempt) || other.pollingAttempt == pollingAttempt));
}


@override
int get hashCode => Object.hash(runtimeType,isRefreshing,isPolling,pollingAttempt);

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.getLeanAccounts(isRefreshing: $isRefreshing, isPolling: $isPolling, pollingAttempt: $pollingAttempt)';
}


}

/// @nodoc
abstract mixin class _$GetLeanAccountsEventCopyWith<$Res> implements $DepositSelectAccountLeanEventCopyWith<$Res> {
  factory _$GetLeanAccountsEventCopyWith(_GetLeanAccountsEvent value, $Res Function(_GetLeanAccountsEvent) _then) = __$GetLeanAccountsEventCopyWithImpl;
@useResult
$Res call({
 bool? isRefreshing, bool? isPolling, int? pollingAttempt
});




}
/// @nodoc
class __$GetLeanAccountsEventCopyWithImpl<$Res>
    implements _$GetLeanAccountsEventCopyWith<$Res> {
  __$GetLeanAccountsEventCopyWithImpl(this._self, this._then);

  final _GetLeanAccountsEvent _self;
  final $Res Function(_GetLeanAccountsEvent) _then;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isRefreshing = freezed,Object? isPolling = freezed,Object? pollingAttempt = freezed,}) {
  return _then(_GetLeanAccountsEvent(
isRefreshing: freezed == isRefreshing ? _self.isRefreshing : isRefreshing // ignore: cast_nullable_to_non_nullable
as bool?,isPolling: freezed == isPolling ? _self.isPolling : isPolling // ignore: cast_nullable_to_non_nullable
as bool?,pollingAttempt: freezed == pollingAttempt ? _self.pollingAttempt : pollingAttempt // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class _OnLeanBankAccountSelectedEvent implements DepositSelectAccountLeanEvent {
  const _OnLeanBankAccountSelectedEvent({required this.selectedAccount});
  

 final  LeanAccount selectedAccount;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLeanBankAccountSelectedEventCopyWith<_OnLeanBankAccountSelectedEvent> get copyWith => __$OnLeanBankAccountSelectedEventCopyWithImpl<_OnLeanBankAccountSelectedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLeanBankAccountSelectedEvent&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount));
}


@override
int get hashCode => Object.hash(runtimeType,selectedAccount);

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.onLeanBankAccountSelected(selectedAccount: $selectedAccount)';
}


}

/// @nodoc
abstract mixin class _$OnLeanBankAccountSelectedEventCopyWith<$Res> implements $DepositSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnLeanBankAccountSelectedEventCopyWith(_OnLeanBankAccountSelectedEvent value, $Res Function(_OnLeanBankAccountSelectedEvent) _then) = __$OnLeanBankAccountSelectedEventCopyWithImpl;
@useResult
$Res call({
 LeanAccount selectedAccount
});


$LeanAccountCopyWith<$Res> get selectedAccount;

}
/// @nodoc
class __$OnLeanBankAccountSelectedEventCopyWithImpl<$Res>
    implements _$OnLeanBankAccountSelectedEventCopyWith<$Res> {
  __$OnLeanBankAccountSelectedEventCopyWithImpl(this._self, this._then);

  final _OnLeanBankAccountSelectedEvent _self;
  final $Res Function(_OnLeanBankAccountSelectedEvent) _then;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? selectedAccount = null,}) {
  return _then(_OnLeanBankAccountSelectedEvent(
selectedAccount: null == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as LeanAccount,
  ));
}

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountCopyWith<$Res> get selectedAccount {
  
  return $LeanAccountCopyWith<$Res>(_self.selectedAccount, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}
}

/// @nodoc


class _OnEditAccountsModeToggledEvent implements DepositSelectAccountLeanEvent {
  const _OnEditAccountsModeToggledEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnEditAccountsModeToggledEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.onEditAccountsModeToggled()';
}


}




/// @nodoc


class _OnLeanPaymentSourceDeletedEvent implements DepositSelectAccountLeanEvent {
  const _OnLeanPaymentSourceDeletedEvent({required this.paymentSource});
  

 final  PaymentSource paymentSource;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLeanPaymentSourceDeletedEventCopyWith<_OnLeanPaymentSourceDeletedEvent> get copyWith => __$OnLeanPaymentSourceDeletedEventCopyWithImpl<_OnLeanPaymentSourceDeletedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLeanPaymentSourceDeletedEvent&&(identical(other.paymentSource, paymentSource) || other.paymentSource == paymentSource));
}


@override
int get hashCode => Object.hash(runtimeType,paymentSource);

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.onLeanPaymentSourceDeleted(paymentSource: $paymentSource)';
}


}

/// @nodoc
abstract mixin class _$OnLeanPaymentSourceDeletedEventCopyWith<$Res> implements $DepositSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnLeanPaymentSourceDeletedEventCopyWith(_OnLeanPaymentSourceDeletedEvent value, $Res Function(_OnLeanPaymentSourceDeletedEvent) _then) = __$OnLeanPaymentSourceDeletedEventCopyWithImpl;
@useResult
$Res call({
 PaymentSource paymentSource
});


$PaymentSourceCopyWith<$Res> get paymentSource;

}
/// @nodoc
class __$OnLeanPaymentSourceDeletedEventCopyWithImpl<$Res>
    implements _$OnLeanPaymentSourceDeletedEventCopyWith<$Res> {
  __$OnLeanPaymentSourceDeletedEventCopyWithImpl(this._self, this._then);

  final _OnLeanPaymentSourceDeletedEvent _self;
  final $Res Function(_OnLeanPaymentSourceDeletedEvent) _then;

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentSource = null,}) {
  return _then(_OnLeanPaymentSourceDeletedEvent(
paymentSource: null == paymentSource ? _self.paymentSource : paymentSource // ignore: cast_nullable_to_non_nullable
as PaymentSource,
  ));
}

/// Create a copy of DepositSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PaymentSourceCopyWith<$Res> get paymentSource {
  
  return $PaymentSourceCopyWith<$Res>(_self.paymentSource, (value) {
    return _then(_self.copyWith(paymentSource: value));
  });
}
}

/// @nodoc


class _GetLeanConfigEvent implements DepositSelectAccountLeanEvent {
  const _GetLeanConfigEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetLeanConfigEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.getLeanConfig()';
}


}




/// @nodoc


class _OnRefreshIconPressedEvent implements DepositSelectAccountLeanEvent {
  const _OnRefreshIconPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnRefreshIconPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositSelectAccountLeanEvent.onRefreshIconPressed()';
}


}




/// @nodoc
mixin _$DepositSelectAccountLeanState {

 LeanAccountProcessState get currentState; bool get isButtonLoading; LeanCustomerResponse? get leanCustomerResponse; DepositFlowConfig? get depositFlowConfig; DepositPaymentMethod? get depositPaymentMethod; LeanAccount? get selectedAccount; bool get isEditingAccountsMode; LeanConfigResponse? get leanConfigResponse; Exception? get leanConfigError; bool get isLoadingLeanConfig; bool get showRefreshIcon; bool get showEmptyPaymentSource; bool get isAddBankAccountDisabled; num? get maxPollingAttempts; num? get pollingFrequencySeconds;
/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositSelectAccountLeanStateCopyWith<DepositSelectAccountLeanState> get copyWith => _$DepositSelectAccountLeanStateCopyWithImpl<DepositSelectAccountLeanState>(this as DepositSelectAccountLeanState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositSelectAccountLeanState&&(identical(other.currentState, currentState) || other.currentState == currentState)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.leanCustomerResponse, leanCustomerResponse) || other.leanCustomerResponse == leanCustomerResponse)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.depositPaymentMethod, depositPaymentMethod) || other.depositPaymentMethod == depositPaymentMethod)&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.isEditingAccountsMode, isEditingAccountsMode) || other.isEditingAccountsMode == isEditingAccountsMode)&&(identical(other.leanConfigResponse, leanConfigResponse) || other.leanConfigResponse == leanConfigResponse)&&(identical(other.leanConfigError, leanConfigError) || other.leanConfigError == leanConfigError)&&(identical(other.isLoadingLeanConfig, isLoadingLeanConfig) || other.isLoadingLeanConfig == isLoadingLeanConfig)&&(identical(other.showRefreshIcon, showRefreshIcon) || other.showRefreshIcon == showRefreshIcon)&&(identical(other.showEmptyPaymentSource, showEmptyPaymentSource) || other.showEmptyPaymentSource == showEmptyPaymentSource)&&(identical(other.isAddBankAccountDisabled, isAddBankAccountDisabled) || other.isAddBankAccountDisabled == isAddBankAccountDisabled)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,currentState,isButtonLoading,leanCustomerResponse,depositFlowConfig,depositPaymentMethod,selectedAccount,isEditingAccountsMode,leanConfigResponse,leanConfigError,isLoadingLeanConfig,showRefreshIcon,showEmptyPaymentSource,isAddBankAccountDisabled,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'DepositSelectAccountLeanState(currentState: $currentState, isButtonLoading: $isButtonLoading, leanCustomerResponse: $leanCustomerResponse, depositFlowConfig: $depositFlowConfig, depositPaymentMethod: $depositPaymentMethod, selectedAccount: $selectedAccount, isEditingAccountsMode: $isEditingAccountsMode, leanConfigResponse: $leanConfigResponse, leanConfigError: $leanConfigError, isLoadingLeanConfig: $isLoadingLeanConfig, showRefreshIcon: $showRefreshIcon, showEmptyPaymentSource: $showEmptyPaymentSource, isAddBankAccountDisabled: $isAddBankAccountDisabled, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class $DepositSelectAccountLeanStateCopyWith<$Res>  {
  factory $DepositSelectAccountLeanStateCopyWith(DepositSelectAccountLeanState value, $Res Function(DepositSelectAccountLeanState) _then) = _$DepositSelectAccountLeanStateCopyWithImpl;
@useResult
$Res call({
 LeanAccountProcessState currentState, bool isButtonLoading, LeanCustomerResponse? leanCustomerResponse, DepositFlowConfig? depositFlowConfig, DepositPaymentMethod? depositPaymentMethod, LeanAccount? selectedAccount, bool isEditingAccountsMode, LeanConfigResponse? leanConfigResponse, Exception? leanConfigError, bool isLoadingLeanConfig, bool showRefreshIcon, bool showEmptyPaymentSource, bool isAddBankAccountDisabled, num? maxPollingAttempts, num? pollingFrequencySeconds
});


$LeanAccountProcessStateCopyWith<$Res> get currentState;$LeanCustomerResponseCopyWith<$Res>? get leanCustomerResponse;$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;$DepositPaymentMethodCopyWith<$Res>? get depositPaymentMethod;$LeanAccountCopyWith<$Res>? get selectedAccount;$LeanConfigResponseCopyWith<$Res>? get leanConfigResponse;

}
/// @nodoc
class _$DepositSelectAccountLeanStateCopyWithImpl<$Res>
    implements $DepositSelectAccountLeanStateCopyWith<$Res> {
  _$DepositSelectAccountLeanStateCopyWithImpl(this._self, this._then);

  final DepositSelectAccountLeanState _self;
  final $Res Function(DepositSelectAccountLeanState) _then;

/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? currentState = null,Object? isButtonLoading = null,Object? leanCustomerResponse = freezed,Object? depositFlowConfig = freezed,Object? depositPaymentMethod = freezed,Object? selectedAccount = freezed,Object? isEditingAccountsMode = null,Object? leanConfigResponse = freezed,Object? leanConfigError = freezed,Object? isLoadingLeanConfig = null,Object? showRefreshIcon = null,Object? showEmptyPaymentSource = null,Object? isAddBankAccountDisabled = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_self.copyWith(
currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as LeanAccountProcessState,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,leanCustomerResponse: freezed == leanCustomerResponse ? _self.leanCustomerResponse : leanCustomerResponse // ignore: cast_nullable_to_non_nullable
as LeanCustomerResponse?,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,depositPaymentMethod: freezed == depositPaymentMethod ? _self.depositPaymentMethod : depositPaymentMethod // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod?,selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as LeanAccount?,isEditingAccountsMode: null == isEditingAccountsMode ? _self.isEditingAccountsMode : isEditingAccountsMode // ignore: cast_nullable_to_non_nullable
as bool,leanConfigResponse: freezed == leanConfigResponse ? _self.leanConfigResponse : leanConfigResponse // ignore: cast_nullable_to_non_nullable
as LeanConfigResponse?,leanConfigError: freezed == leanConfigError ? _self.leanConfigError : leanConfigError // ignore: cast_nullable_to_non_nullable
as Exception?,isLoadingLeanConfig: null == isLoadingLeanConfig ? _self.isLoadingLeanConfig : isLoadingLeanConfig // ignore: cast_nullable_to_non_nullable
as bool,showRefreshIcon: null == showRefreshIcon ? _self.showRefreshIcon : showRefreshIcon // ignore: cast_nullable_to_non_nullable
as bool,showEmptyPaymentSource: null == showEmptyPaymentSource ? _self.showEmptyPaymentSource : showEmptyPaymentSource // ignore: cast_nullable_to_non_nullable
as bool,isAddBankAccountDisabled: null == isAddBankAccountDisabled ? _self.isAddBankAccountDisabled : isAddBankAccountDisabled // ignore: cast_nullable_to_non_nullable
as bool,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}
/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountProcessStateCopyWith<$Res> get currentState {
  
  return $LeanAccountProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanCustomerResponseCopyWith<$Res>? get leanCustomerResponse {
    if (_self.leanCustomerResponse == null) {
    return null;
  }

  return $LeanCustomerResponseCopyWith<$Res>(_self.leanCustomerResponse!, (value) {
    return _then(_self.copyWith(leanCustomerResponse: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res>? get depositPaymentMethod {
    if (_self.depositPaymentMethod == null) {
    return null;
  }

  return $DepositPaymentMethodCopyWith<$Res>(_self.depositPaymentMethod!, (value) {
    return _then(_self.copyWith(depositPaymentMethod: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $LeanAccountCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanConfigResponseCopyWith<$Res>? get leanConfigResponse {
    if (_self.leanConfigResponse == null) {
    return null;
  }

  return $LeanConfigResponseCopyWith<$Res>(_self.leanConfigResponse!, (value) {
    return _then(_self.copyWith(leanConfigResponse: value));
  });
}
}


/// @nodoc


class _DepositSelectAccountLeanState implements DepositSelectAccountLeanState {
  const _DepositSelectAccountLeanState({this.currentState = const LeanAccountProcessState.loading(), this.isButtonLoading = false, this.leanCustomerResponse, this.depositFlowConfig, this.depositPaymentMethod, this.selectedAccount, this.isEditingAccountsMode = false, this.leanConfigResponse, this.leanConfigError, this.isLoadingLeanConfig = false, this.showRefreshIcon = false, this.showEmptyPaymentSource = false, this.isAddBankAccountDisabled = false, this.maxPollingAttempts, this.pollingFrequencySeconds});
  

@override@JsonKey() final  LeanAccountProcessState currentState;
@override@JsonKey() final  bool isButtonLoading;
@override final  LeanCustomerResponse? leanCustomerResponse;
@override final  DepositFlowConfig? depositFlowConfig;
@override final  DepositPaymentMethod? depositPaymentMethod;
@override final  LeanAccount? selectedAccount;
@override@JsonKey() final  bool isEditingAccountsMode;
@override final  LeanConfigResponse? leanConfigResponse;
@override final  Exception? leanConfigError;
@override@JsonKey() final  bool isLoadingLeanConfig;
@override@JsonKey() final  bool showRefreshIcon;
@override@JsonKey() final  bool showEmptyPaymentSource;
@override@JsonKey() final  bool isAddBankAccountDisabled;
@override final  num? maxPollingAttempts;
@override final  num? pollingFrequencySeconds;

/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositSelectAccountLeanStateCopyWith<_DepositSelectAccountLeanState> get copyWith => __$DepositSelectAccountLeanStateCopyWithImpl<_DepositSelectAccountLeanState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositSelectAccountLeanState&&(identical(other.currentState, currentState) || other.currentState == currentState)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.leanCustomerResponse, leanCustomerResponse) || other.leanCustomerResponse == leanCustomerResponse)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.depositPaymentMethod, depositPaymentMethod) || other.depositPaymentMethod == depositPaymentMethod)&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.isEditingAccountsMode, isEditingAccountsMode) || other.isEditingAccountsMode == isEditingAccountsMode)&&(identical(other.leanConfigResponse, leanConfigResponse) || other.leanConfigResponse == leanConfigResponse)&&(identical(other.leanConfigError, leanConfigError) || other.leanConfigError == leanConfigError)&&(identical(other.isLoadingLeanConfig, isLoadingLeanConfig) || other.isLoadingLeanConfig == isLoadingLeanConfig)&&(identical(other.showRefreshIcon, showRefreshIcon) || other.showRefreshIcon == showRefreshIcon)&&(identical(other.showEmptyPaymentSource, showEmptyPaymentSource) || other.showEmptyPaymentSource == showEmptyPaymentSource)&&(identical(other.isAddBankAccountDisabled, isAddBankAccountDisabled) || other.isAddBankAccountDisabled == isAddBankAccountDisabled)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds));
}


@override
int get hashCode => Object.hash(runtimeType,currentState,isButtonLoading,leanCustomerResponse,depositFlowConfig,depositPaymentMethod,selectedAccount,isEditingAccountsMode,leanConfigResponse,leanConfigError,isLoadingLeanConfig,showRefreshIcon,showEmptyPaymentSource,isAddBankAccountDisabled,maxPollingAttempts,pollingFrequencySeconds);

@override
String toString() {
  return 'DepositSelectAccountLeanState(currentState: $currentState, isButtonLoading: $isButtonLoading, leanCustomerResponse: $leanCustomerResponse, depositFlowConfig: $depositFlowConfig, depositPaymentMethod: $depositPaymentMethod, selectedAccount: $selectedAccount, isEditingAccountsMode: $isEditingAccountsMode, leanConfigResponse: $leanConfigResponse, leanConfigError: $leanConfigError, isLoadingLeanConfig: $isLoadingLeanConfig, showRefreshIcon: $showRefreshIcon, showEmptyPaymentSource: $showEmptyPaymentSource, isAddBankAccountDisabled: $isAddBankAccountDisabled, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds)';
}


}

/// @nodoc
abstract mixin class _$DepositSelectAccountLeanStateCopyWith<$Res> implements $DepositSelectAccountLeanStateCopyWith<$Res> {
  factory _$DepositSelectAccountLeanStateCopyWith(_DepositSelectAccountLeanState value, $Res Function(_DepositSelectAccountLeanState) _then) = __$DepositSelectAccountLeanStateCopyWithImpl;
@override @useResult
$Res call({
 LeanAccountProcessState currentState, bool isButtonLoading, LeanCustomerResponse? leanCustomerResponse, DepositFlowConfig? depositFlowConfig, DepositPaymentMethod? depositPaymentMethod, LeanAccount? selectedAccount, bool isEditingAccountsMode, LeanConfigResponse? leanConfigResponse, Exception? leanConfigError, bool isLoadingLeanConfig, bool showRefreshIcon, bool showEmptyPaymentSource, bool isAddBankAccountDisabled, num? maxPollingAttempts, num? pollingFrequencySeconds
});


@override $LeanAccountProcessStateCopyWith<$Res> get currentState;@override $LeanCustomerResponseCopyWith<$Res>? get leanCustomerResponse;@override $DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;@override $DepositPaymentMethodCopyWith<$Res>? get depositPaymentMethod;@override $LeanAccountCopyWith<$Res>? get selectedAccount;@override $LeanConfigResponseCopyWith<$Res>? get leanConfigResponse;

}
/// @nodoc
class __$DepositSelectAccountLeanStateCopyWithImpl<$Res>
    implements _$DepositSelectAccountLeanStateCopyWith<$Res> {
  __$DepositSelectAccountLeanStateCopyWithImpl(this._self, this._then);

  final _DepositSelectAccountLeanState _self;
  final $Res Function(_DepositSelectAccountLeanState) _then;

/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currentState = null,Object? isButtonLoading = null,Object? leanCustomerResponse = freezed,Object? depositFlowConfig = freezed,Object? depositPaymentMethod = freezed,Object? selectedAccount = freezed,Object? isEditingAccountsMode = null,Object? leanConfigResponse = freezed,Object? leanConfigError = freezed,Object? isLoadingLeanConfig = null,Object? showRefreshIcon = null,Object? showEmptyPaymentSource = null,Object? isAddBankAccountDisabled = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,}) {
  return _then(_DepositSelectAccountLeanState(
currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as LeanAccountProcessState,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,leanCustomerResponse: freezed == leanCustomerResponse ? _self.leanCustomerResponse : leanCustomerResponse // ignore: cast_nullable_to_non_nullable
as LeanCustomerResponse?,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,depositPaymentMethod: freezed == depositPaymentMethod ? _self.depositPaymentMethod : depositPaymentMethod // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod?,selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as LeanAccount?,isEditingAccountsMode: null == isEditingAccountsMode ? _self.isEditingAccountsMode : isEditingAccountsMode // ignore: cast_nullable_to_non_nullable
as bool,leanConfigResponse: freezed == leanConfigResponse ? _self.leanConfigResponse : leanConfigResponse // ignore: cast_nullable_to_non_nullable
as LeanConfigResponse?,leanConfigError: freezed == leanConfigError ? _self.leanConfigError : leanConfigError // ignore: cast_nullable_to_non_nullable
as Exception?,isLoadingLeanConfig: null == isLoadingLeanConfig ? _self.isLoadingLeanConfig : isLoadingLeanConfig // ignore: cast_nullable_to_non_nullable
as bool,showRefreshIcon: null == showRefreshIcon ? _self.showRefreshIcon : showRefreshIcon // ignore: cast_nullable_to_non_nullable
as bool,showEmptyPaymentSource: null == showEmptyPaymentSource ? _self.showEmptyPaymentSource : showEmptyPaymentSource // ignore: cast_nullable_to_non_nullable
as bool,isAddBankAccountDisabled: null == isAddBankAccountDisabled ? _self.isAddBankAccountDisabled : isAddBankAccountDisabled // ignore: cast_nullable_to_non_nullable
as bool,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}

/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountProcessStateCopyWith<$Res> get currentState {
  
  return $LeanAccountProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanCustomerResponseCopyWith<$Res>? get leanCustomerResponse {
    if (_self.leanCustomerResponse == null) {
    return null;
  }

  return $LeanCustomerResponseCopyWith<$Res>(_self.leanCustomerResponse!, (value) {
    return _then(_self.copyWith(leanCustomerResponse: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res>? get depositPaymentMethod {
    if (_self.depositPaymentMethod == null) {
    return null;
  }

  return $DepositPaymentMethodCopyWith<$Res>(_self.depositPaymentMethod!, (value) {
    return _then(_self.copyWith(depositPaymentMethod: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $LeanAccountCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of DepositSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanConfigResponseCopyWith<$Res>? get leanConfigResponse {
    if (_self.leanConfigResponse == null) {
    return null;
  }

  return $LeanConfigResponseCopyWith<$Res>(_self.leanConfigResponse!, (value) {
    return _then(_self.copyWith(leanConfigResponse: value));
  });
}
}

/// @nodoc
mixin _$LeanAccountProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeanAccountProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState()';
}


}

/// @nodoc
class $LeanAccountProcessStateCopyWith<$Res>  {
$LeanAccountProcessStateCopyWith(LeanAccountProcessState _, $Res Function(LeanAccountProcessState) __);
}


/// @nodoc


class LoadingState implements LeanAccountProcessState {
  const LoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.loading()';
}


}




/// @nodoc


class LoadedState implements LeanAccountProcessState {
  const LoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.loaded()';
}


}




/// @nodoc


class ErrorState implements LeanAccountProcessState {
  const ErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.error()';
}


}




/// @nodoc


class EmptyState implements LeanAccountProcessState {
  const EmptyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmptyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.empty()';
}


}




/// @nodoc


class AccountDeletedSuccessfullyState implements LeanAccountProcessState {
  const AccountDeletedSuccessfullyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDeletedSuccessfullyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.accountDeletedSuccessfully()';
}


}




// dart format on
