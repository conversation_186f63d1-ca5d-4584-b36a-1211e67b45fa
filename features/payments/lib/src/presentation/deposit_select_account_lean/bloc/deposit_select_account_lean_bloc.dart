import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/data/lean_config_response/lean_config_response.dart';
import 'package:payment/src/data/lean_customer_response/lean_customer_response.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/usecase/delete_payment_source_usecase.dart';
import 'package:payment/src/domain/usecase/get_lean_account_usecase.dart';
import 'package:payment/src/domain/usecase/get_lean_config_usecase.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/data/lean_customer_response/payment_source_list_extensions.dart';

part 'deposit_select_account_lean_bloc.freezed.dart';
part 'deposit_select_account_lean_event.dart';
part 'deposit_select_account_lean_state.dart';

class DepositSelectAccountLeanBloc
    extends Bloc<DepositSelectAccountLeanEvent, DepositSelectAccountLeanState> {
  final GetLeanAccountUsecase _getLeanAccountUsecase;
  final DeletePaymentSourceUseCase _deletePaymentSourceUseCase;
  final GetLeanConfigUsecase _getLeanConfigUsecase;
  final PaymentNavigation _paymentNavigation;
  final DepositAnalyticsEvent _depositAnalyticsEvent;

  // ============================================================================
  // POLLING CONFIGURATION
  // ============================================================================
  // When a user adds a new bank account via Lean Connect, the account
  // verification happens asynchronously on the backend. The account initially
  // appears with IN_PROGRESS status and transitions to ACTIVE once verified.
  // We poll the API to detect when this transition completes.

  /// Maximum number of times to poll for account verification status.
  /// After this limit, we show a refresh icon for manual retry.
  static const int _maxPollingAttempts = 3;

  /// Time interval between polling attempts.
  static const Duration _pollingInterval = Duration(seconds: 2);

  /// Tracks the bank identifier of the most recently added bank account.
  ///
  /// This is set when the user returns from the Lean Connect flow after adding
  /// a new bank. It's used during polling to verify if the newly added bank
  /// appears in the payment sources list.
  ///
  /// Reset to null after polling completes or reaches maximum attempts.
  String? _addedBankIdentifier;

  DepositSelectAccountLeanBloc({
    required GetLeanAccountUsecase getLeanAccountUsecase,
    required DeletePaymentSourceUseCase deletePaymentSourceUseCase,
    required GetLeanConfigUsecase getLeanConfigUsecase,
    required PaymentNavigation paymentNavigation,
    required DepositAnalyticsEvent depositAnalyticsEvent,
  }) : _getLeanAccountUsecase = getLeanAccountUsecase,
       _deletePaymentSourceUseCase = deletePaymentSourceUseCase,
       _getLeanConfigUsecase = getLeanConfigUsecase,
       _paymentNavigation = paymentNavigation,
       _depositAnalyticsEvent = depositAnalyticsEvent,
       super(const DepositSelectAccountLeanState()) {
    on<_InitArgumentsEvent>(_initArguments);
    on<_OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
    on<_OnAddAccountPressedEvent>(_onAddAccountPressed);
    on<_GetLeanAccountsEvent>(_onGetLeanAccounts);
    on<_OnLeanBankAccountSelectedEvent>(_onLeanBankAccountSelected);
    on<_OnEditAccountsModeToggledEvent>(_onEditAccountsModeToggled);
    on<_OnLeanPaymentSourceDeletedEvent>(_onLeanPaymentSourceDeleted);
    on<_GetLeanConfigEvent>(_onGetLeanConfig);
    on<_OnRefreshIconPressedEvent>(_onRefreshIconPressed);
  }

  /// Initializes the bloc with required arguments from the navigation flow.
  ///
  /// This should be called when the screen is first opened to set up the
  /// payment method and deposit flow configuration.
  FutureOr<void> _initArguments(
    _InitArgumentsEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    emit(
      state.copyWith(
        depositPaymentMethod: event.paymentMethod,
        depositFlowConfig: event.depositFlowConfig,
        maxPollingAttempts: event.maxPollingAttempts,
        pollingFrequencySeconds: event.pollingFrequencySeconds,
      ),
    );
  }

  /// Fetches lean accounts from the API and handles the response.
  ///
  /// This method is the main entry point for loading payment sources. It can be
  /// called in three scenarios:
  /// 1. Initial load when the screen opens
  /// 2. Manual refresh by the user (via refresh icon)
  /// 3. Automatic polling after adding a new bank account
  ///
  /// The polling mechanism is used to check if newly added bank accounts have
  /// completed verification (moved from IN_PROGRESS to ACTIVE status).
  Future<void> _onGetLeanAccounts(
    _GetLeanAccountsEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) async {
    // Only show loading state if not polling (i.e., initial load or manual refresh)
    final isPolling = event.isPolling ?? false;
    final pollingAttempt = event.pollingAttempt;
    final refreshingAfterAddingAccount = event.isRefreshing ?? false;

    if (!isPolling) {
      emit(
        state.copyWith(currentState: const LeanAccountProcessState.loading()),
      );
    }

    // Analytics: mark start of loading payment sources request
    _depositAnalyticsEvent.depositLeanPaymentSourcesLoaded(sourcesCount: 0);

    // Execute API call
    final result = await _getLeanAccountUsecase().run();
    if (isClosed) return;

    // Handle result using helper methods
    result.fold(
      (error) =>
          _handleAccountsFetchError(error, emit, isPolling, pollingAttempt),
      (response) => _handleSuccessfulAccountsFetch(
        response: response,
        emit: emit,
        isPolling: isPolling,
        pollingAttempt: pollingAttempt,
        refreshingAfterAddingAccount: refreshingAfterAddingAccount,
      ),
    );
  }

  /// Handles the "Add Account" button press.
  ///
  /// Navigates to the Lean Connect Bank screen where users can link a new bank
  /// account. When the user returns, if a bank was successfully added, stores
  /// the bank identifier and triggers a refresh with polling enabled.
  FutureOr<void> _onAddAccountPressed(
    _OnAddAccountPressedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    log('Add account button pressed');
    if (state.leanCustomerResponse != null) {
      assert(
        state.depositPaymentMethod != null,
        "Deposit payment method is null",
      );
      _paymentNavigation.navigateToLeanConnectBank(
        leanAccountArgs: LeanAccountArgs(
          selectedLeanAccount: state.selectedAccount,
          mop: state.depositPaymentMethod!.name,
          paymentType: PaymentType.deposit,
        ),
        then: (bankIdentifier) {
          if (bankIdentifier != null) {
            log(
              'Returned from Lean Connect Bank screen with bank identifier $bankIdentifier, refreshing accounts ',
            );
            _addedBankIdentifier = bankIdentifier;
            add(
              DepositSelectAccountLeanEvent.getLeanAccounts(isRefreshing: true),
            );
          }
        },
      );
    } else {
      log('Lean customer data is null, cannot navigate to add account');
    }
  }

  /// Handles bank account selection/deselection.
  ///
  /// If the tapped account is already selected, deselects it (sets to null).
  /// Otherwise, selects the tapped account.
  FutureOr<void> _onLeanBankAccountSelected(
    _OnLeanBankAccountSelectedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    if (event.selectedAccount == state.selectedAccount) {
      emit(state.copyWith(selectedAccount: null));
    } else {
      // Note: Analytics tracking happens in the widget layer
      emit(state.copyWith(selectedAccount: event.selectedAccount));
    }
  }

  /// Handles the confirm button press.
  ///
  /// Navigates to the next screen (deposit amount selection) with the selected
  /// account information.
  FutureOr<void> _onConfirmButtonPressed(
    _OnConfirmButtonPressedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    log('Confirm button pressed');
    _paymentNavigation.goToDepositSelectAccountAndAmountScreen(
      state.depositPaymentMethod!,
      depositFlowConfig: state.depositFlowConfig!,
      maxPollingAttempts: state.maxPollingAttempts,
      pollingFrequencySeconds: state.pollingFrequencySeconds,
      leanAccountArgs: LeanAccountArgs(
        selectedLeanAccount: state.selectedAccount,
        mop: state.depositPaymentMethod!.name,
        paymentType: PaymentType.deposit,
      ),
    );
  }

  /// Toggles edit mode for managing bank accounts.
  ///
  /// In edit mode, users can delete saved bank accounts.
  FutureOr<void> _onEditAccountsModeToggled(
    _OnEditAccountsModeToggledEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    emit(state.copyWith(isEditingAccountsMode: !state.isEditingAccountsMode));
  }

  /// Handles deletion of a payment source (bank account).
  ///
  /// Shows loading state, calls the delete API, and updates the state based on
  /// the result. If successful, removes the payment source from the local state
  /// and determines if the empty state should be shown.
  Future<void> _onLeanPaymentSourceDeleted(
    _OnLeanPaymentSourceDeletedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) async {
    emit(state.copyWith(currentState: const LeanAccountProcessState.loading()));
    _depositAnalyticsEvent.depositLeanPaymentSourceDeleteStart(
      paymentSourceId: event.paymentSource.id,
    );
    final result =
        await _deletePaymentSourceUseCase(
          paymentSourceId: event.paymentSource.id,
        ).run();
    if (isClosed) return;
    result.fold(
      (error) {
        log('Error deleting payment source: $error');
        _depositAnalyticsEvent.depositLeanPaymentSourceDeleteError(
          paymentSourceId: event.paymentSource.id,
          errorMessage: error.toString(),
        );
        emit(state.copyWith(currentState: LeanAccountProcessState.error()));
      },
      (response) async {
        log('Payment source deleted successfully: ${response.toJson()}');
        _depositAnalyticsEvent.depositLeanPaymentSourceDeleteSuccess(
          paymentSourceId: event.paymentSource.id,
        );

        // Emit success state to trigger UI feedback
        emit(
          state.copyWith(
            currentState:
                const LeanAccountProcessState.accountDeletedSuccessfully(),
          ),
        );

        // Refresh the data from API to get the updated list
        add(const DepositSelectAccountLeanEvent.getLeanAccounts());
      },
    );
  }

  /// Fetches Lean configuration required for the Lean Connect SDK.
  ///
  /// This configuration includes API keys and settings needed to initialize
  /// the Lean Connect flow for adding bank accounts.
  Future<void> _onGetLeanConfig(
    _GetLeanConfigEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) async {
    // Set loading flag and clear previous config/error
    emit(
      state.copyWith(
        leanConfigResponse: null,
        leanConfigError: null,
        isLoadingLeanConfig: true,
      ),
    );

    final result = await _getLeanConfigUsecase().run();
    if (isClosed) return;
    result.fold(
      (error) {
        log('Error fetching lean config: $error');
        _depositAnalyticsEvent.depositLeanConnectError(
          errorMessage: error.toString(),
        );
        // Set error and stop loading
        emit(
          state.copyWith(leanConfigError: error, isLoadingLeanConfig: false),
        );
      },
      (response) {
        log('Lean config fetched: ${response.toJson()}');
        emit(
          state.copyWith(
            leanConfigResponse: response,
            isLoadingLeanConfig: false,
          ),
        );
      },
    );
  }

  /// Handles manual refresh icon press.
  ///
  /// This is shown when polling reaches maximum attempts but accounts are still
  /// in IN_PROGRESS status. Allows users to manually retry fetching accounts.
  FutureOr<void> _onRefreshIconPressed(
    _OnRefreshIconPressedEvent event,
    Emitter<DepositSelectAccountLeanState> emit,
  ) {
    log(
      'Refresh icon pressed, manually refreshing accounts and restarting polling',
    );
    // Hide the refresh icon
    emit(state.copyWith(showRefreshIcon: false));
    add(const DepositSelectAccountLeanEvent.getLeanAccounts());
  }

  // ============================================================================
  // HELPER METHODS - State Determination and Polling Logic
  // ============================================================================

  /// Checks if the recently added bank is present in the payment sources.
  ///
  /// Returns `true` if:
  /// - No bank was recently added (_addedBankIdentifier is null), OR
  /// - The added bank identifier exists in the current payment sources list
  ///
  /// Returns `false` if a bank was added but is not yet in the payment sources.
  bool _isAddedBankInSources() {
    if (_addedBankIdentifier == null) return true;
    return state.leanCustomerResponse?.paymentSourceList.containsBankIdentifier(
          _addedBankIdentifier,
        ) ??
        false;
  }

  /// Checks if any payment source has IN_PROGRESS status.
  ///
  /// This indicates that account verification is still ongoing and polling
  /// may be needed to check for completion.
  bool _hasAccountsInProgress() {
    return state.leanCustomerResponse?.paymentSourceList
            .hasAccountsInProgress() ??
        false;
  }

  /// Determines the appropriate state based on account availability and bank presence.
  ///
  /// Returns [LeanAccountProcessState.empty] if the added bank is present but
  /// there are no accounts (user needs to add an account).
  ///
  /// Returns [LeanAccountProcessState.loaded] in all other cases (accounts exist
  /// or bank is still being processed).
  LeanAccountProcessState _determineAccountState({
    required bool hasAddedBank,
    required bool hasNoAccounts,
  }) {
    return hasAddedBank && hasNoAccounts
        ? LeanAccountProcessState.empty()
        : LeanAccountProcessState.loaded();
  }

  /// Determines if polling should continue based on current conditions.
  ///
  /// Polling continues if:
  /// 1. The added bank is not yet in sources OR accounts are still in progress, AND
  /// 2. We haven't reached the maximum number of polling attempts
  ///
  /// Parameters:
  ///   - [hasAddedBank]: Whether the recently added bank is in payment sources
  ///   - [hasAccountsInProgress]: Whether any account has IN_PROGRESS status
  ///   - [currentAttempt]: The current polling attempt number
  bool _shouldContinuePolling({
    required bool hasAddedBank,
    required bool hasAccountsInProgress,
    required int currentAttempt,
  }) {
    final needsPolling = !hasAddedBank || hasAccountsInProgress;
    final hasAttemptsRemaining = currentAttempt < _maxPollingAttempts;
    return needsPolling && hasAttemptsRemaining;
  }

  /// Handles state updates when polling completes or stops.
  ///
  /// If all accounts are verified (no IN_PROGRESS status) and the added bank
  /// is present, hides the refresh icon and clears the empty state.
  ///
  /// If polling stopped due to max attempts being reached, shows the refresh
  /// icon so users can manually retry.
  ///
  /// Always clears the _addedBankIdentifier after completion.
  void _handlePollingCompletion({
    required Emitter<DepositSelectAccountLeanState> emit,
    required bool hasAccountsInProgress,
    required bool hasAddedBank,
  }) {
    if (!hasAccountsInProgress && hasAddedBank) {
      log('Polling stopped: No accounts in IN_PROGRESS status');
      // Hide refresh icon since all accounts are no longer IN_PROGRESS
      emit(
        state.copyWith(showRefreshIcon: false, showEmptyPaymentSource: false),
      );
    } else {
      log('Polling stopped: Maximum attempts ($_maxPollingAttempts) reached');
      // Show refresh icon since accounts are still IN_PROGRESS after max attempts
      emit(state.copyWith(showRefreshIcon: true));
    }
    _addedBankIdentifier = null;
  }

  /// Handles the error response when fetching lean accounts fails.
  ///
  /// Logs the error, tracks analytics, and updates state to error.
  /// If this was a polling attempt, continues polling despite the error.
  void _handleAccountsFetchError(
    Exception error,
    Emitter<DepositSelectAccountLeanState> emit,
    bool isPolling,
    int? pollingAttempt,
  ) {
    log('Error fetching lean accounts: $error');
    _depositAnalyticsEvent.depositLeanPaymentSourcesLoadError(
      errorMessage: error.toString(),
    );
    emit(state.copyWith(currentState: LeanAccountProcessState.error()));

    // If this was a polling attempt, continue polling despite error
    if (isPolling && pollingAttempt != null) {
      log('Error during polling attempt $pollingAttempt: $error');
      _schedulePoll(currentAttempt: pollingAttempt);
    }
  }

  /// Handles the successful response from fetching lean accounts.
  ///
  /// This method:
  /// 1. Logs the response and tracks analytics
  /// 2. Determines the appropriate state (empty/loaded)
  /// 3. Updates the state with the new data
  /// 4. Handles polling logic if this was a polling request
  /// 5. Starts polling if this was triggered after adding an account
  void _handleSuccessfulAccountsFetch({
    required LeanCustomerResponse response,
    required Emitter<DepositSelectAccountLeanState> emit,
    required bool isPolling,
    required int? pollingAttempt,
    required bool refreshingAfterAddingAccount,
  }) {
    log('Lean accounts fetched: ${response.toJson()}');
    final sources = response.paymentSourceList;
    _depositAnalyticsEvent.depositLeanPaymentSourcesLoaded(
      sourcesCount: sources.length,
    );

    final hasNoAccounts = sources.isEmpty;
    final hasAddedBankInSources = _isAddedBankInSources();
    final currentState = _determineAccountState(
      hasAddedBank: hasAddedBankInSources,
      hasNoAccounts: hasNoAccounts,
    );

    emit(
      state.copyWith(
        currentState: currentState,
        leanCustomerResponse: response,
        isEditingAccountsMode: false,
        selectedAccount: null,
        isAddBankAccountDisabled: response.paymentSourceList.length >= 3,
      ),
    );

    // Handle polling logic if this was a polling attempt
    if (isPolling && pollingAttempt != null) {
      _handlePollingAttempt(
        emit: emit,
        sources: sources,
        hasAddedBankInSources: hasAddedBankInSources,
        pollingAttempt: pollingAttempt,
      );
    }
    // Start polling if we're refreshing after adding an account
    else if (refreshingAfterAddingAccount) {
      _startPolling();
    }
  }

  /// Handles the logic for a single polling attempt.
  ///
  /// Checks if the added bank is present and if any accounts are still in progress.
  /// Decides whether to continue polling or finalize the polling state.
  void _handlePollingAttempt({
    required Emitter<DepositSelectAccountLeanState> emit,
    required List<PaymentSource> sources,
    required bool hasAddedBankInSources,
    required int pollingAttempt,
  }) {
    log(
      'Polling attempt $pollingAttempt successful. Checking payment source statuses...',
    );

    // Show empty state if added bank is not in payment sources
    if (!hasAddedBankInSources) {
      emit(state.copyWith(showEmptyPaymentSource: true));
    }

    // Check if any account is still in progress
    final hasAccountsInProgress = sources.hasAccountsInProgress();
    log('Any account still in progress: $hasAccountsInProgress');

    // Determine if polling should continue
    if (_shouldContinuePolling(
      hasAddedBank: hasAddedBankInSources,
      hasAccountsInProgress: hasAccountsInProgress,
      currentAttempt: pollingAttempt,
    )) {
      // Continue polling if we haven't reached max attempts
      _schedulePoll(currentAttempt: pollingAttempt);
    } else {
      // Polling complete - finalize state
      _handlePollingCompletion(
        emit: emit,
        hasAccountsInProgress: hasAccountsInProgress,
        hasAddedBank: hasAddedBankInSources,
      );
    }
  }

  /// Starts the polling mechanism after adding an account.
  ///
  /// This method checks if any payment source has IN_PROGRESS status or if the
  /// recently added bank is not yet in the payment sources, and initiates
  /// polling if needed.
  ///
  /// Polling is necessary because bank account verification is asynchronous and
  /// can take several seconds to complete on the backend.
  void _startPolling() {
    final hasAddedBankInSources = _isAddedBankInSources();
    final hasAccountsInProgress = _hasAccountsInProgress();

    log(
      'Starting polling check. Any account in progress: $hasAccountsInProgress, '
      'Added bank in sources: $hasAddedBankInSources',
    );

    // Start polling if any account is in progress or added bank is not yet present
    if (hasAccountsInProgress || !hasAddedBankInSources) {
      _schedulePoll(currentAttempt: 0);
    }
  }

  /// Schedules a polling attempt after the configured delay.
  ///
  /// This method checks if the maximum number of attempts has been reached and
  /// schedules the next poll if not. The polling mechanism is used to check if
  /// newly added bank accounts have completed verification.
  ///
  /// Parameters:
  ///   - [currentAttempt]: The current polling attempt number (0-indexed)
  ///
  /// The method stops polling if:
  /// - Maximum attempts (_maxPollingAttempts) have been reached
  /// - The bloc is closed
  void _schedulePoll({required int currentAttempt}) {
    // Check if we've reached the maximum polling attempts
    if (currentAttempt >= _maxPollingAttempts) {
      log('Polling stopped: Maximum attempts ($_maxPollingAttempts) reached');
      return;
    }

    final newAttempt = currentAttempt + 1;
    log(
      'Scheduling polling attempt $newAttempt of $_maxPollingAttempts in ${_pollingInterval.inSeconds} seconds...',
    );

    // Schedule the next poll after the configured interval
    Future<void>.delayed(_pollingInterval).then((_) {
      if (!isClosed) {
        log('Executing polling attempt $newAttempt of $_maxPollingAttempts');
        _executePoll(attemptNumber: newAttempt);
      }
    });
  }

  /// Executes a single polling attempt by dispatching the getLeanAccounts event.
  ///
  /// This method triggers a background fetch of lean accounts without showing
  /// a loading indicator to the user. The response is handled by
  /// [_onGetLeanAccounts] with polling-specific logic.
  ///
  /// Parameters:
  ///   - [attemptNumber]: The current polling attempt number (1-indexed)
  void _executePoll({required int attemptNumber}) {
    log('Executing polling attempt $attemptNumber of $_maxPollingAttempts');

    // Dispatch the event to fetch lean accounts in the background
    // This will trigger _onGetLeanAccounts with isPolling: true
    add(
      DepositSelectAccountLeanEvent.getLeanAccounts(
        isPolling: true,
        pollingAttempt: attemptNumber,
      ),
    );
  }
}
