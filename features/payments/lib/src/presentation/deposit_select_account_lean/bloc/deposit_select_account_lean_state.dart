part of 'deposit_select_account_lean_bloc.dart';

@freezed
sealed class DepositSelectAccountLeanState
    with _$DepositSelectAccountLeanState {
  const factory DepositSelectAccountLeanState({
    @Default(LeanAccountProcessState.loading())
    LeanAccountProcessState currentState,
    @Default(false) bool isButtonLoading,
    LeanCustomerResponse? leanCustomerResponse,
    DepositFlowConfig? depositFlowConfig,
    DepositPaymentMethod? depositPaymentMethod,
    LeanAccount? selectedAccount,
    @Default(false) bool isEditingAccountsMode,
    LeanConfigResponse? leanConfigResponse,
    Exception? leanConfigError,
    @Default(false) bool isLoadingLeanConfig,
    @Default(false) bool showRefreshIcon,
    @Default(false) bool showEmptyPaymentSource,
    @Default(false) bool isAddBankAccountDisabled,
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
  }) = _DepositSelectAccountLeanState;
}

@freezed
sealed class LeanAccountProcessState with _$LeanAccountProcessState {
  const factory LeanAccountProcessState.loading() = LoadingState;
  const factory LeanAccountProcessState.loaded() = LoadedState;
  const factory LeanAccountProcessState.error() = ErrorState;
  const factory LeanAccountProcessState.empty() = EmptyState;
  const factory LeanAccountProcessState.accountDeletedSuccessfully() =
      AccountDeletedSuccessfullyState;
}
