part of 'withdraw_add_iban_lean_bloc.dart';

@freezed
sealed class WithdrawAddIbanLeanState with _$WithdrawAddIbanLeanState {
  const factory WithdrawAddIbanLeanState({
    @Default(AddIbanProcessState.initial()) AddIbanProcessState currentState,
    @Default(false) bool isButtonLoading,
    @Default(false) bool isFormValid,
    WithdrawalPaymentMethod? withdrawalPaymentMethod,
    String? originRoute,
    @Default('') String iban,
    @Default('AED') String currencyIsoCode,
    String? ibanError,
  }) = _WithdrawAddIbanLeanState;
}

@freezed
sealed class AddIbanProcessState with _$AddIbanProcessState {
  const factory AddIbanProcessState.initial() = InitialState;
  const factory AddIbanProcessState.loading() = LoadingState;
  const factory AddIbanProcessState.success() = SuccessState;
  const factory AddIbanProcessState.error(String message) = ErrorState;
}
