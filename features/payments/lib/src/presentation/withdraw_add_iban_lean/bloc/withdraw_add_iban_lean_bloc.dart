import 'dart:async';
import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/domain/exceptions/create_lean_payout_destination_exception/create_lean_payout_destination_exception.dart';
import 'package:payment/src/domain/usecase/create_lean_payout_destination_usecase.dart';
import 'package:payment/src/domain/validators/iban_validator.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

part 'withdraw_add_iban_lean_bloc.freezed.dart';
part 'withdraw_add_iban_lean_event.dart';
part 'withdraw_add_iban_lean_state.dart';

class WithdrawAddIbanLeanBloc
    extends Bloc<WithdrawAddIbanLeanEvent, WithdrawAddIbanLeanState> {
  final PaymentNavigation _paymentNavigation;
  final CreateLeanPayoutDestinationUsecase _createLeanPayoutDestinationUsecase;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  WithdrawAddIbanLeanBloc({
    required PaymentNavigation paymentNavigation,
    required CreateLeanPayoutDestinationUsecase
    createLeanPayoutDestinationUsecase,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _paymentNavigation = paymentNavigation,
       _createLeanPayoutDestinationUsecase = createLeanPayoutDestinationUsecase,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(const WithdrawAddIbanLeanState()) {
    on<_InitArgumentsEvent>(_initArguments);
    on<_OnIbanChangedEvent>(_onIbanChanged);
    on<_OnCurrencyChangedEvent>(_onCurrencyChanged);
    on<_OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
    on<_ValidateFormEvent>(_validateForm);
    on<_ResetProcessStateEvent>(_resetProcessState);
  }

  FutureOr<void> _initArguments(
    _InitArgumentsEvent event,
    Emitter<WithdrawAddIbanLeanState> emit,
  ) {
    emit(
      state.copyWith(
        withdrawalPaymentMethod: event.withdrawalPaymentMethod,
        originRoute: event.originRoute,
      ),
    );
  }

  FutureOr<void> _onIbanChanged(
    _OnIbanChangedEvent event,
    Emitter<WithdrawAddIbanLeanState> emit,
  ) {
    // Validate IBAN in real-time
    final validationError = IbanValidator.validate(event.iban);
    // Store the localization key instead of the error enum
    final ibanError = validationError?.toLocalizationKey();

    emit(state.copyWith(iban: event.iban, ibanError: ibanError));
    add(const WithdrawAddIbanLeanEvent.validateForm());
  }

  FutureOr<void> _onCurrencyChanged(
    _OnCurrencyChangedEvent event,
    Emitter<WithdrawAddIbanLeanState> emit,
  ) {
    emit(state.copyWith(currencyIsoCode: event.currencyIsoCode));
    add(const WithdrawAddIbanLeanEvent.validateForm());
  }

  FutureOr<void> _validateForm(
    _ValidateFormEvent event,
    Emitter<WithdrawAddIbanLeanState> emit,
  ) {
    // IBAN is valid if it's complete (23 characters) and has no validation errors
    final isIbanValid =
        IbanValidator.isComplete(state.iban) &&
        IbanValidator.validate(state.iban) == null;
    final isCurrencyValid = state.currencyIsoCode.isNotEmpty;

    final isFormValid = isIbanValid && isCurrencyValid;

    emit(state.copyWith(isFormValid: isFormValid));
  }

  FutureOr<void> _resetProcessState(
    _ResetProcessStateEvent event,
    Emitter<WithdrawAddIbanLeanState> emit,
  ) {
    emit(state.copyWith(currentState: const AddIbanProcessState.initial()));
  }

  Future<void> _onConfirmButtonPressed(
    _OnConfirmButtonPressedEvent event,
    Emitter<WithdrawAddIbanLeanState> emit,
  ) async {
    // Validate all fields before submission
    String? ibanError;

    if (state.iban.isEmpty) {
      ibanError = 'IBAN is required';
    } else {
      // Use the IBAN validator for comprehensive validation
      final validationError = IbanValidator.validate(state.iban);
      ibanError = validationError?.toLocalizationKey();
    }

    if (ibanError != null) {
      emit(state.copyWith(ibanError: ibanError));
      return;
    }

    emit(state.copyWith(isButtonLoading: true));
    _withdrawAnalyticsEvent.withdrawLeanAddIbanStart();

    try {
      final result =
          await _createLeanPayoutDestinationUsecase(
            iban: state.iban,
            currencyIsoCode: state.currencyIsoCode,
          ).run();

      if (isClosed) return;

      result.fold(
        (error) {
          log('Error adding IBAN: $error');
          _withdrawAnalyticsEvent.withdrawLeanAddIbanError(
            errorMessage: error.toString(),
          );
          if (error is CreateLeanPayoutDestinationException) {
            switch (error) {
              case CreateLeanPayoutDestinationUnknownError():
                emit(
                  state.copyWith(
                    currentState: AddIbanProcessState.error(error.message),
                    isButtonLoading: false,
                    ibanError: error.message,
                  ),
                );
            }
          } else {
            emit(
              state.copyWith(
                currentState: AddIbanProcessState.error(error.toString()),
                isButtonLoading: false,
                ibanError: error.toString(),
              ),
            );
          }
        },
        (response) {
          log('IBAN added successfully: ${response.toJson()}');
          _withdrawAnalyticsEvent.withdrawLeanAddIbanSuccess(
            currencyIsoCode: state.currencyIsoCode,
          );
          emit(
            state.copyWith(
              currentState: const AddIbanProcessState.success(),
              isButtonLoading: false,
              ibanError: null,
            ),
          );
        },
      );
    } catch (e) {
      log('Error adding IBAN: $e');
      if (isClosed) return;

      _withdrawAnalyticsEvent.withdrawLeanAddIbanError(
        errorMessage: e.toString(),
      );
      emit(
        state.copyWith(
          currentState: AddIbanProcessState.error(e.toString()),
          isButtonLoading: false,
        ),
      );
    }
  }
}
