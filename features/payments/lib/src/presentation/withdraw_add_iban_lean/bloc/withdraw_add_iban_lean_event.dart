part of 'withdraw_add_iban_lean_bloc.dart';

@freezed
sealed class WithdrawAddIbanLeanEvent with _$WithdrawAddIbanLeanEvent {
  const factory WithdrawAddIbanLeanEvent.initArguments({
    required WithdrawalPaymentMethod withdrawalPaymentMethod,
    required String originRoute,
  }) = _InitArgumentsEvent;

  const factory WithdrawAddIbanLeanEvent.onIbanChanged(String iban) =
      _OnIbanChangedEvent;

  const factory WithdrawAddIbanLeanEvent.onCurrencyChanged(
    String currencyIsoCode,
  ) = _OnCurrencyChangedEvent;

  const factory WithdrawAddIbanLeanEvent.onConfirmButtonPressed() =
      _OnConfirmButtonPressedEvent;

  const factory WithdrawAddIbanLeanEvent.validateForm() = _ValidateFormEvent;

  const factory WithdrawAddIbanLeanEvent.resetProcessState() =
      _ResetProcessStateEvent;
}
