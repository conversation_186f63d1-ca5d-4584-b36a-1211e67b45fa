import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/withdraw_add_iban_lean/bloc/withdraw_add_iban_lean_bloc.dart';
import 'package:payment/src/presentation/withdraw_add_iban_lean/widgets/withdraw_add_iban_lean_form.dart';

class WithdrawAddIbanLean extends StatelessWidget {
  const WithdrawAddIbanLean({
    super.key,
    required this.withdrawalPaymentMethod,
    required this.originRoute,
  });

  final WithdrawalPaymentMethod withdrawalPaymentMethod;
  final String originRoute;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<WithdrawAddIbanLeanBloc>(
      create:
          (_) =>
              diContainer<WithdrawAddIbanLeanBloc>()..add(
                WithdrawAddIbanLeanEvent.initArguments(
                  withdrawalPaymentMethod: withdrawalPaymentMethod,
                  originRoute: originRoute,
                ),
              ),
      child: BlocListener<WithdrawAddIbanLeanBloc, WithdrawAddIbanLeanState>(
        listenWhen:
            (previous, current) =>
                previous.currentState != current.currentState,
        listener: (listenerContext, state) {
          if (state.currentState is SuccessState) {
            Navigator.of(listenerContext).pop();
          }
        },
        child: BlocBuilder<WithdrawAddIbanLeanBloc, WithdrawAddIbanLeanState>(
          buildWhen:
              (previous, current) =>
                  current.currentState != previous.currentState,
          builder: (blocContext, state) {
            return switch (state.currentState) {
              InitialState() ||
              SuccessState() ||
              ErrorState() => WithdrawAddIbanLeanForm(
                withdrawalPaymentMethod: withdrawalPaymentMethod,
              ),
              LoadingState() => const Scaffold(
                body: Center(child: LoadingView()),
              ),
            };
          },
        ),
      ),
    );
  }
}
