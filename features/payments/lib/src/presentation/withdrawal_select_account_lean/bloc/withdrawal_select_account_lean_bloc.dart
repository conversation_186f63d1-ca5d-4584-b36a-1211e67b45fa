import 'dart:async';
import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/lean_destination_account_response/lean_destination_account_response.dart';
import 'package:payment/src/data/lean_destination_account_response/lean_destination_account_data_list_extensions.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/usecase/delete_iban_usecase.dart';
import 'package:payment/src/domain/usecase/delete_payment_destination_usecase.dart';
import 'package:payment/src/domain/usecase/get_lean_destination_account_usecase.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

part 'withdrawal_select_account_lean_bloc.freezed.dart';
part 'withdrawal_select_account_lean_event.dart';
part 'withdrawal_select_account_lean_state.dart';

class WithdrawalSelectAccountLeanBloc
    extends
        Bloc<
          WithdrawalSelectAccountLeanEvent,
          WithdrawalSelectAccountLeanState
        > {
  final PaymentNavigation _paymentNavigation;
  final GetLeanDestinationAccountUsecase _getLeanDestinationAccountUsecase;
  final DeleteIbanUseCase _deleteIbanUseCase;
  final DeletePaymentDestinationUseCase _deletePaymentDestinationUseCase;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  // ============================================================================
  // POLLING CONFIGURATION
  // ============================================================================
  // When a user adds a new bank account via Lean Connect, the account
  // verification happens asynchronously on the backend. The account initially
  // appears with IN_PROGRESS status and transitions to ACTIVE once verified.
  // We poll the API to detect when this transition completes.

  /// Maximum number of times to poll for account verification status.
  /// After this limit, we show a refresh icon for manual retry.
  static const int _maxPollingAttempts = 3;

  /// Time interval between polling attempts.
  static const Duration _pollingInterval = Duration(seconds: 2);

  /// Tracks the bank identifier of the most recently added bank account.
  ///
  /// This is set when the user returns from the Lean Connect flow after adding
  /// a new bank. It's used during polling to verify if the newly added bank
  /// appears in the bank accounts list.
  ///
  /// Reset to null after polling completes or reaches maximum attempts.
  String? _addedBankIdentifier;

  WithdrawalSelectAccountLeanBloc({
    required PaymentNavigation paymentNavigation,
    required GetLeanDestinationAccountUsecase getLeanDestinationAccountUsecase,
    required DeleteIbanUseCase deleteIbanUseCase,
    required DeletePaymentDestinationUseCase deletePaymentDestinationUseCase,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _paymentNavigation = paymentNavigation,
       _getLeanDestinationAccountUsecase = getLeanDestinationAccountUsecase,
       _deleteIbanUseCase = deleteIbanUseCase,
       _deletePaymentDestinationUseCase = deletePaymentDestinationUseCase,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(const WithdrawalSelectAccountLeanState()) {
    on<_InitArgumentsEvent>(_initArguments);
    on<_OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
    on<_OnAddNewAccountPressedEvent>(_onAddNewAccountPressed);
    on<_OnLeanBankAccountSelectedEvent>(_onLeanBankAccountSelected);
    on<_OnLeanIBanSelectedEvent>(_onLeanIBanSelected);
    on<_GetLeanDestinationAccountsEvent>(_onGetLeanDestinationAccounts);
    on<_OnEditAccountsModeToggledEvent>(_onEditAccountsModeToggled);
    on<_OnTabChangedEvent>(_onTabChanged);
    on<_OnBankConnectionDeletedEvent>(_onBankConnectionDeleted);
    on<_OnManualIbanDeletedEvent>(_onManualIbanDeleted);
    on<_OnRefreshIconPressedEvent>(_onRefreshIconPressed);
  }
  FutureOr<void> _initArguments(
    _InitArgumentsEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    emit(
      state.copyWith(
        withdrawalPaymentMethod: event.withdrawalPaymentMethod,
        originRoute: event.originRoute,
      ),
    );
  }

  /// Fetches lean destination accounts from the API and handles the response.
  ///
  /// This method is the main entry point for loading destination accounts. It can be
  /// called in three scenarios:
  /// 1. Initial load when the screen opens
  /// 2. Manual refresh by the user (via refresh icon)
  /// 3. Automatic polling after adding a new bank account
  ///
  /// The polling mechanism is used to check if newly added bank accounts have
  /// completed verification (moved from IN_PROGRESS to ACTIVE status).
  Future<void> _onGetLeanDestinationAccounts(
    _GetLeanDestinationAccountsEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) async {
    // Only show loading state if not polling (i.e., initial load or manual refresh)
    final isPolling = event.isPolling ?? false;
    final pollingAttempt = event.pollingAttempt;
    final refreshingAfterAddingAccount =
        event.refreshingAfterAddingAccount ?? false;

    if (!isPolling) {
      emit(
        state.copyWith(currentState: const LeanAccountProcessState.loading()),
      );
    }

    // Analytics: mark start of loading destination accounts request
    _withdrawAnalyticsEvent.withdrawLeanDestinationAccountsLoaded(
      accountsCount: 0,
    );

    // Execute API call
    final result = await _getLeanDestinationAccountUsecase().run();
    if (isClosed) return;

    // Handle result using helper methods
    result.fold(
      (error) =>
          _handleAccountsFetchError(error, emit, isPolling, pollingAttempt),
      (response) => _handleSuccessfulAccountsFetch(
        response: response,
        emit: emit,
        isPolling: isPolling,
        pollingAttempt: pollingAttempt,
        refreshingAfterAddingAccount: refreshingAfterAddingAccount,
      ),
    );
  }

  /// Handles the "Add Account" button press.
  ///
  /// Navigates to either:
  /// 1. Lean Connect Bank screen (for bank accounts) - triggers polling after return
  /// 2. Add IBAN screen (for manual IBANs) - does NOT trigger polling
  ///
  /// When the user returns from Lean Connect Bank, if a bank was successfully added,
  /// stores the bank identifier and triggers a refresh with polling enabled.
  FutureOr<void> _onAddNewAccountPressed(
    _OnAddNewAccountPressedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    if (state.selectedTabIndex == 0) {
      // Navigate to add bank account screen (Lean Connect flow)
      log("Add bank account pressed");
      _paymentNavigation.navigateToLeanConnectBank(
        leanAccountArgs: LeanAccountArgs(
          mop: state.withdrawalPaymentMethod!.name,
          paymentType: PaymentType.withdrawal,
        ),
        then: (bankIdentifier) {
          if (bankIdentifier != null) {
            log(
              'Returned from Lean Connect Bank screen with bank identifier $bankIdentifier, refreshing accounts',
            );
            // Store the bank identifier for polling
            _addedBankIdentifier = bankIdentifier;
            add(
              const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(
                refreshingAfterAddingAccount: true,
              ),
            );
          }
        },
      );
    } else {
      // Navigate to add IBAN screen (manual flow - no polling needed)
      log("Add IBAN pressed");
      _paymentNavigation.goToWithdrawAddIbanLeanScreen(
        withdrawalPaymentMethod: state.withdrawalPaymentMethod!,
        originRoute: state.originRoute ?? '',
        then: () {
          // Do NOT set _addedBankIdentifier for manual IBANs
          // Just refresh the list without polling
          add(
            const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(),
          );
        },
      );
    }
  }

  FutureOr<void> _onConfirmButtonPressed(
    _OnConfirmButtonPressedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    final selectedAccount =
        state.selectedTabIndex == 0
            ? state.selectedBankAccount
            : state.selectedIBanAccount;
    if (selectedAccount != null) {
      _paymentNavigation.goToWithdrawSelectAccountAndAmountScreen(
        method: state.withdrawalPaymentMethod!,
        selectedLeanAccount: selectedAccount,
        popUntilRoute: state.originRoute ?? '',
      );
    }
  }

  FutureOr<void> _onEditAccountsModeToggled(
    _OnEditAccountsModeToggledEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    emit(state.copyWith(isEditingAccountsMode: !state.isEditingAccountsMode));
  }

  FutureOr<void> _onLeanBankAccountSelected(
    _OnLeanBankAccountSelectedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    if (event.selectedBankAccount == state.selectedBankAccount) {
      emit(state.copyWith(selectedBankAccount: null));
    } else {
      emit(state.copyWith(selectedBankAccount: event.selectedBankAccount));
    }
  }

  FutureOr<void> _onLeanIBanSelected(
    _OnLeanIBanSelectedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    if (event.selectedIBanAccount == state.selectedIBanAccount) {
      emit(state.copyWith(selectedIBanAccount: null));
    } else {
      emit(state.copyWith(selectedIBanAccount: event.selectedIBanAccount));
    }
  }

  FutureOr<void> _onTabChanged(
    _OnTabChangedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    emit(state.copyWith(selectedTabIndex: event.index));
  }

  Future<void> _onBankConnectionDeleted(
    _OnBankConnectionDeletedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) async {
    emit(state.copyWith(currentState: const LeanAccountProcessState.loading()));

    final bankIdentifier = event.bankAccountData.bankIdentifier;
    _withdrawAnalyticsEvent.withdrawLeanDestinationAccountDeleteStart(
      destinationId: bankIdentifier,
    );

    final result =
        await _deletePaymentDestinationUseCase(
          bankIdentifier: bankIdentifier,
        ).run();

    if (isClosed) return;
    result.fold(
      (error) {
        log('Error deleting bank connection: $error');
        _withdrawAnalyticsEvent.withdrawLeanDestinationAccountDeleteError(
          destinationId: bankIdentifier,
          errorMessage: error.toString(),
        );
        emit(state.copyWith(currentState: LeanAccountProcessState.error()));
      },
      (response) async {
        log('Bank connection deleted successfully: ${response.toJson()}');
        _withdrawAnalyticsEvent.withdrawLeanDestinationAccountDeleteSuccess(
          destinationId: bankIdentifier,
        );
        emit(
          state.copyWith(
            currentState:
                const LeanAccountProcessState.accountDeletedSuccessfully(),
          ),
        );
        // Refresh the data from API to get the updated list
        add(
          const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(),
        );
      },
    );
  }

  Future<void> _onManualIbanDeleted(
    _OnManualIbanDeletedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) async {
    emit(state.copyWith(currentState: const LeanAccountProcessState.loading()));

    _withdrawAnalyticsEvent.withdrawLeanDestinationAccountDeleteStart(
      destinationId: event.ibanAccount.id,
    );

    final result =
        await _deleteIbanUseCase(
          paymentDestinationId: event.ibanAccount.id,
        ).run();

    if (isClosed) return;
    result.fold(
      (error) {
        log('Error deleting IBAN: $error');
        _withdrawAnalyticsEvent.withdrawLeanDestinationAccountDeleteError(
          destinationId: event.ibanAccount.id,
          errorMessage: error.toString(),
        );
        emit(state.copyWith(currentState: LeanAccountProcessState.error()));
      },
      (response) async {
        log('IBAN deleted successfully: ${response.toJson()}');
        _withdrawAnalyticsEvent.withdrawLeanDestinationAccountDeleteSuccess(
          destinationId: event.ibanAccount.id,
        );

        emit(
          state.copyWith(
            currentState:
                const LeanAccountProcessState.accountDeletedSuccessfully(),
          ),
        );

        // Refresh the data from API to get the updated list

        add(
          const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(),
        );
      },
    );
  }

  /// Handles manual refresh icon press.
  ///
  /// This is shown when polling reaches maximum attempts but accounts are still
  /// in IN_PROGRESS status. Allows users to manually retry fetching accounts.
  FutureOr<void> _onRefreshIconPressed(
    _OnRefreshIconPressedEvent event,
    Emitter<WithdrawalSelectAccountLeanState> emit,
  ) {
    log(
      'Refresh icon pressed, manually refreshing accounts and restarting polling',
    );
    // Hide the refresh icon
    emit(state.copyWith(showRefreshIcon: false));
    add(const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts());
  }

  // ============================================================================
  // HELPER METHODS - State Determination and Polling Logic
  // ============================================================================

  /// Checks if the recently added bank is present in the bank accounts list.
  ///
  /// Returns `true` if:
  /// - No bank was recently added (_addedBankIdentifier is null), OR
  /// - The added bank identifier exists in the current bank accounts list
  ///
  /// Returns `false` if a bank was added but is not yet in the bank accounts.
  bool _isAddedBankInAccounts() {
    if (_addedBankIdentifier == null) return true;
    return state.bankAccountList.containsBankIdentifier(_addedBankIdentifier);
  }

  /// Checks if any bank connection has IN_PROGRESS status.
  ///
  /// This indicates that bank connection verification is still ongoing and polling
  /// may be needed to check for completion.
  bool _hasBankConnectionsInProgress() {
    return state.bankAccountList.hasBankConnectionsInProgress();
  }

  /// Determines if polling should continue based on current conditions.
  ///
  /// Polling continues if:
  /// 1. The added bank is not yet in accounts OR bank connections are still in progress, AND
  /// 2. We haven't reached the maximum number of polling attempts
  ///
  /// Parameters:
  ///   - [hasAddedBank]: Whether the recently added bank is in bank accounts
  ///   - [hasBankConnectionsInProgress]: Whether any bank connection has IN_PROGRESS status
  ///   - [currentAttempt]: The current polling attempt number
  bool _shouldContinuePolling({
    required bool hasAddedBank,
    required bool hasBankConnectionsInProgress,
    required int currentAttempt,
  }) {
    final needsPolling = !hasAddedBank || hasBankConnectionsInProgress;
    final hasAttemptsRemaining = currentAttempt < _maxPollingAttempts;
    return needsPolling && hasAttemptsRemaining;
  }

  /// Handles state updates when polling completes or stops.
  ///
  /// If all bank connections are verified (no IN_PROGRESS status) and the added bank
  /// is present, hides the refresh icon and clears the empty state.
  ///
  /// If polling stopped due to max attempts being reached, shows the refresh
  /// icon so users can manually retry.
  ///
  /// Always clears the _addedBankIdentifier after completion.
  void _handlePollingCompletion({
    required Emitter<WithdrawalSelectAccountLeanState> emit,
    required bool hasBankConnectionsInProgress,
    required bool hasAddedBank,
  }) {
    if (!hasBankConnectionsInProgress && hasAddedBank) {
      log('Polling stopped: No bank connections in IN_PROGRESS status');
      // Hide refresh icon since all bank connections are no longer IN_PROGRESS
      emit(
        state.copyWith(
          showRefreshIcon: false,
          showEmptyPaymentDestination: false,
        ),
      );
    } else {
      log('Polling stopped: Maximum attempts ($_maxPollingAttempts) reached');
      // Show refresh icon since bank connections are still IN_PROGRESS after max attempts
      emit(state.copyWith(showRefreshIcon: true));
    }
    _addedBankIdentifier = null;
  }

  /// Handles the error response when fetching lean destination accounts fails.
  ///
  /// Logs the error, tracks analytics, and updates state to error.
  /// If this was a polling attempt, continues polling despite the error.
  void _handleAccountsFetchError(
    Exception error,
    Emitter<WithdrawalSelectAccountLeanState> emit,
    bool isPolling,
    int? pollingAttempt,
  ) {
    log('Error fetching lean destination accounts: $error');
    _withdrawAnalyticsEvent.withdrawLeanDestinationAccountsLoadError(
      errorMessage: error.toString(),
    );
    emit(state.copyWith(currentState: LeanAccountProcessState.error()));

    // If this was a polling attempt, continue polling despite error
    if (isPolling && pollingAttempt != null) {
      log('Error during polling attempt $pollingAttempt: $error');
      _schedulePoll(currentAttempt: pollingAttempt);
    }
  }

  /// Handles the successful response from fetching lean destination accounts.
  ///
  /// This method:
  /// 1. Logs the response and tracks analytics
  /// 2. Processes the data into bank accounts and IBANs lists
  /// 3. Updates the state with the new data
  /// 4. Handles polling logic if this was a polling request
  /// 5. Starts polling if this was triggered after adding an account
  void _handleSuccessfulAccountsFetch({
    required LeanDestinationAccountResponse response,
    required Emitter<WithdrawalSelectAccountLeanState> emit,
    required bool isPolling,
    required int? pollingAttempt,
    required bool refreshingAfterAddingAccount,
  }) {
    log('Lean destination accounts fetched: ${response.toJson()}');
    final autoAccounts = response.data.auto;
    final manualAccounts = response.data.manual;

    // Flatten all destinations from both auto and manual account data groups
    final allDestinations = [
      ...autoAccounts.expand((accountData) => accountData.destinations),
      ...manualAccounts.expand((accountData) => accountData.destinations),
    ];

    _withdrawAnalyticsEvent.withdrawLeanDestinationAccountsLoaded(
      accountsCount: allDestinations.length,
    );

    // Manual accounts are already filtered - flatten destinations for IBANs list
    final iBansList =
        manualAccounts
            .expand((accountData) => accountData.destinations)
            .toList();

    // Auto accounts are already filtered - use directly for bank accounts list
    final bankAccountList = autoAccounts;

    emit(
      state.copyWith(
        currentState: LeanAccountProcessState.loaded(),
        isEditingAccountsMode: false,
        leanDestinationAccounts: allDestinations,
        //note: auto accounts are bank accounts, manual accounts are IBANs
        bankAccountList: bankAccountList,
        iBansList: iBansList,
        isAddIbanDisabled: iBansList.length >= 3,
        isAddBankAccountDisabled: bankAccountList.length >= 3,
        selectedTabIndex:
            bankAccountList.isEmpty && iBansList.isNotEmpty ? 1 : 0,
      ),
    );

    // Handle polling logic if this was a polling attempt
    if (isPolling && pollingAttempt != null) {
      _handlePollingAttempt(
        emit: emit,
        bankAccountList: bankAccountList,
        hasAddedBankInAccounts: _isAddedBankInAccounts(),
        pollingAttempt: pollingAttempt,
      );
    }
    // Start polling if we're refreshing after adding an account
    else if (refreshingAfterAddingAccount) {
      _startPolling();
    }
  }

  /// Handles the logic for a single polling attempt.
  ///
  /// Checks if the added bank is present and if any bank connections are still in progress.
  /// Decides whether to continue polling or finalize the polling state.
  void _handlePollingAttempt({
    required Emitter<WithdrawalSelectAccountLeanState> emit,
    required List<LeanDestinationAccountData> bankAccountList,
    required bool hasAddedBankInAccounts,
    required int pollingAttempt,
  }) {
    log(
      'Polling attempt $pollingAttempt successful. Checking bank connection statuses...',
    );

    // Show empty state if added bank is not in bank accounts
    if (!hasAddedBankInAccounts) {
      emit(state.copyWith(showEmptyPaymentDestination: true));
    }

    // Check if any bank connection is still in progress
    final hasBankConnectionsInProgress =
        bankAccountList.hasBankConnectionsInProgress();
    log('Any bank connection still in progress: $hasBankConnectionsInProgress');

    // Determine if polling should continue
    if (_shouldContinuePolling(
      hasAddedBank: hasAddedBankInAccounts,
      hasBankConnectionsInProgress: hasBankConnectionsInProgress,
      currentAttempt: pollingAttempt,
    )) {
      // Continue polling if we haven't reached max attempts
      _schedulePoll(currentAttempt: pollingAttempt);
    } else {
      // Polling complete - finalize state
      _handlePollingCompletion(
        emit: emit,
        hasBankConnectionsInProgress: hasBankConnectionsInProgress,
        hasAddedBank: hasAddedBankInAccounts,
      );
    }
  }

  /// Starts the polling mechanism after adding a bank account.
  ///
  /// This method checks if any bank connection has IN_PROGRESS status or if the
  /// recently added bank is not yet in the bank accounts, and initiates
  /// polling if needed.
  ///
  /// Polling is necessary because bank account verification is asynchronous and
  /// can take several seconds to complete on the backend.
  void _startPolling() {
    final hasAddedBankInAccounts = _isAddedBankInAccounts();
    final hasBankConnectionsInProgress = _hasBankConnectionsInProgress();

    log(
      'Starting polling check. Any bank connection in progress: $hasBankConnectionsInProgress, '
      'Added bank in accounts: $hasAddedBankInAccounts',
    );

    // Start polling if any bank connection is in progress or added bank is not yet present
    if (hasBankConnectionsInProgress || !hasAddedBankInAccounts) {
      _schedulePoll(currentAttempt: 0);
    }
  }

  /// Schedules a polling attempt after the configured delay.
  ///
  /// This method checks if the maximum number of attempts has been reached and
  /// schedules the next poll if not. The polling mechanism is used to check if
  /// newly added bank accounts have completed verification.
  ///
  /// Parameters:
  ///   - [currentAttempt]: The current polling attempt number (0-indexed)
  ///
  /// The method stops polling if:
  /// - Maximum attempts (_maxPollingAttempts) have been reached
  /// - The bloc is closed
  void _schedulePoll({required int currentAttempt}) {
    // Check if we've reached the maximum polling attempts
    if (currentAttempt >= _maxPollingAttempts) {
      log('Polling stopped: Maximum attempts ($_maxPollingAttempts) reached');
      return;
    }

    final newAttempt = currentAttempt + 1;
    log(
      'Scheduling polling attempt $newAttempt of $_maxPollingAttempts in ${_pollingInterval.inSeconds} seconds...',
    );

    // Schedule the next poll after the configured interval
    Future<void>.delayed(_pollingInterval).then((_) {
      if (!isClosed) {
        log('Executing polling attempt $newAttempt of $_maxPollingAttempts');
        _executePoll(attemptNumber: newAttempt);
      }
    });
  }

  /// Executes a single polling attempt by dispatching the getLeanDestinationAccounts event.
  ///
  /// This method triggers a background fetch of lean destination accounts without showing
  /// a loading indicator to the user. The response is handled by
  /// [_onGetLeanDestinationAccounts] with polling-specific logic.
  ///
  /// Parameters:
  ///   - [attemptNumber]: The current polling attempt number (1-indexed)
  void _executePoll({required int attemptNumber}) {
    log('Executing polling attempt $attemptNumber of $_maxPollingAttempts');

    // Dispatch the event to fetch lean destination accounts in the background
    // This will trigger _onGetLeanDestinationAccounts with isPolling: true
    add(
      WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(
        isPolling: true,
        pollingAttempt: attemptNumber,
      ),
    );
  }
}
