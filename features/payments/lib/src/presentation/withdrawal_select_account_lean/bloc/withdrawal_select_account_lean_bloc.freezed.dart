// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdrawal_select_account_lean_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WithdrawalSelectAccountLeanEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawalSelectAccountLeanEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent()';
}


}

/// @nodoc
class $WithdrawalSelectAccountLeanEventCopyWith<$Res>  {
$WithdrawalSelectAccountLeanEventCopyWith(WithdrawalSelectAccountLeanEvent _, $Res Function(WithdrawalSelectAccountLeanEvent) __);
}


/// @nodoc


class _InitArgumentsEvent implements WithdrawalSelectAccountLeanEvent {
  const _InitArgumentsEvent({required this.withdrawalPaymentMethod, required this.originRoute});
  

 final  WithdrawalPaymentMethod withdrawalPaymentMethod;
 final  String originRoute;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InitArgumentsEventCopyWith<_InitArgumentsEvent> get copyWith => __$InitArgumentsEventCopyWithImpl<_InitArgumentsEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InitArgumentsEvent&&(identical(other.withdrawalPaymentMethod, withdrawalPaymentMethod) || other.withdrawalPaymentMethod == withdrawalPaymentMethod)&&(identical(other.originRoute, originRoute) || other.originRoute == originRoute));
}


@override
int get hashCode => Object.hash(runtimeType,withdrawalPaymentMethod,originRoute);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.initArguments(withdrawalPaymentMethod: $withdrawalPaymentMethod, originRoute: $originRoute)';
}


}

/// @nodoc
abstract mixin class _$InitArgumentsEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$InitArgumentsEventCopyWith(_InitArgumentsEvent value, $Res Function(_InitArgumentsEvent) _then) = __$InitArgumentsEventCopyWithImpl;
@useResult
$Res call({
 WithdrawalPaymentMethod withdrawalPaymentMethod, String originRoute
});


$WithdrawalPaymentMethodCopyWith<$Res> get withdrawalPaymentMethod;

}
/// @nodoc
class __$InitArgumentsEventCopyWithImpl<$Res>
    implements _$InitArgumentsEventCopyWith<$Res> {
  __$InitArgumentsEventCopyWithImpl(this._self, this._then);

  final _InitArgumentsEvent _self;
  final $Res Function(_InitArgumentsEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? withdrawalPaymentMethod = null,Object? originRoute = null,}) {
  return _then(_InitArgumentsEvent(
withdrawalPaymentMethod: null == withdrawalPaymentMethod ? _self.withdrawalPaymentMethod : withdrawalPaymentMethod // ignore: cast_nullable_to_non_nullable
as WithdrawalPaymentMethod,originRoute: null == originRoute ? _self.originRoute : originRoute // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalPaymentMethodCopyWith<$Res> get withdrawalPaymentMethod {
  
  return $WithdrawalPaymentMethodCopyWith<$Res>(_self.withdrawalPaymentMethod, (value) {
    return _then(_self.copyWith(withdrawalPaymentMethod: value));
  });
}
}

/// @nodoc


class _OnConfirmButtonPressedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnConfirmButtonPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnConfirmButtonPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onConfirmButtonPressed()';
}


}




/// @nodoc


class _OnAddNewAccountPressedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnAddNewAccountPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAddNewAccountPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onAddNewAccountPressed()';
}


}




/// @nodoc


class _GetLeanDestinationAccountsEvent implements WithdrawalSelectAccountLeanEvent {
  const _GetLeanDestinationAccountsEvent({this.refreshingAfterAddingAccount, this.isPolling, this.pollingAttempt});
  

 final  bool? refreshingAfterAddingAccount;
 final  bool? isPolling;
 final  int? pollingAttempt;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetLeanDestinationAccountsEventCopyWith<_GetLeanDestinationAccountsEvent> get copyWith => __$GetLeanDestinationAccountsEventCopyWithImpl<_GetLeanDestinationAccountsEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetLeanDestinationAccountsEvent&&(identical(other.refreshingAfterAddingAccount, refreshingAfterAddingAccount) || other.refreshingAfterAddingAccount == refreshingAfterAddingAccount)&&(identical(other.isPolling, isPolling) || other.isPolling == isPolling)&&(identical(other.pollingAttempt, pollingAttempt) || other.pollingAttempt == pollingAttempt));
}


@override
int get hashCode => Object.hash(runtimeType,refreshingAfterAddingAccount,isPolling,pollingAttempt);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(refreshingAfterAddingAccount: $refreshingAfterAddingAccount, isPolling: $isPolling, pollingAttempt: $pollingAttempt)';
}


}

/// @nodoc
abstract mixin class _$GetLeanDestinationAccountsEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$GetLeanDestinationAccountsEventCopyWith(_GetLeanDestinationAccountsEvent value, $Res Function(_GetLeanDestinationAccountsEvent) _then) = __$GetLeanDestinationAccountsEventCopyWithImpl;
@useResult
$Res call({
 bool? refreshingAfterAddingAccount, bool? isPolling, int? pollingAttempt
});




}
/// @nodoc
class __$GetLeanDestinationAccountsEventCopyWithImpl<$Res>
    implements _$GetLeanDestinationAccountsEventCopyWith<$Res> {
  __$GetLeanDestinationAccountsEventCopyWithImpl(this._self, this._then);

  final _GetLeanDestinationAccountsEvent _self;
  final $Res Function(_GetLeanDestinationAccountsEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? refreshingAfterAddingAccount = freezed,Object? isPolling = freezed,Object? pollingAttempt = freezed,}) {
  return _then(_GetLeanDestinationAccountsEvent(
refreshingAfterAddingAccount: freezed == refreshingAfterAddingAccount ? _self.refreshingAfterAddingAccount : refreshingAfterAddingAccount // ignore: cast_nullable_to_non_nullable
as bool?,isPolling: freezed == isPolling ? _self.isPolling : isPolling // ignore: cast_nullable_to_non_nullable
as bool?,pollingAttempt: freezed == pollingAttempt ? _self.pollingAttempt : pollingAttempt // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class _OnLeanBankAccountSelectedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnLeanBankAccountSelectedEvent(this.selectedBankAccount);
  

 final  LeanDestinationAccount selectedBankAccount;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLeanBankAccountSelectedEventCopyWith<_OnLeanBankAccountSelectedEvent> get copyWith => __$OnLeanBankAccountSelectedEventCopyWithImpl<_OnLeanBankAccountSelectedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLeanBankAccountSelectedEvent&&(identical(other.selectedBankAccount, selectedBankAccount) || other.selectedBankAccount == selectedBankAccount));
}


@override
int get hashCode => Object.hash(runtimeType,selectedBankAccount);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onLeanBankAccountSelected(selectedBankAccount: $selectedBankAccount)';
}


}

/// @nodoc
abstract mixin class _$OnLeanBankAccountSelectedEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnLeanBankAccountSelectedEventCopyWith(_OnLeanBankAccountSelectedEvent value, $Res Function(_OnLeanBankAccountSelectedEvent) _then) = __$OnLeanBankAccountSelectedEventCopyWithImpl;
@useResult
$Res call({
 LeanDestinationAccount selectedBankAccount
});


$LeanDestinationAccountCopyWith<$Res> get selectedBankAccount;

}
/// @nodoc
class __$OnLeanBankAccountSelectedEventCopyWithImpl<$Res>
    implements _$OnLeanBankAccountSelectedEventCopyWith<$Res> {
  __$OnLeanBankAccountSelectedEventCopyWithImpl(this._self, this._then);

  final _OnLeanBankAccountSelectedEvent _self;
  final $Res Function(_OnLeanBankAccountSelectedEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? selectedBankAccount = null,}) {
  return _then(_OnLeanBankAccountSelectedEvent(
null == selectedBankAccount ? _self.selectedBankAccount : selectedBankAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount,
  ));
}

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res> get selectedBankAccount {
  
  return $LeanDestinationAccountCopyWith<$Res>(_self.selectedBankAccount, (value) {
    return _then(_self.copyWith(selectedBankAccount: value));
  });
}
}

/// @nodoc


class _OnLeanIBanSelectedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnLeanIBanSelectedEvent(this.selectedIBanAccount);
  

 final  LeanDestinationAccount selectedIBanAccount;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLeanIBanSelectedEventCopyWith<_OnLeanIBanSelectedEvent> get copyWith => __$OnLeanIBanSelectedEventCopyWithImpl<_OnLeanIBanSelectedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLeanIBanSelectedEvent&&(identical(other.selectedIBanAccount, selectedIBanAccount) || other.selectedIBanAccount == selectedIBanAccount));
}


@override
int get hashCode => Object.hash(runtimeType,selectedIBanAccount);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onLeanIBanSelected(selectedIBanAccount: $selectedIBanAccount)';
}


}

/// @nodoc
abstract mixin class _$OnLeanIBanSelectedEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnLeanIBanSelectedEventCopyWith(_OnLeanIBanSelectedEvent value, $Res Function(_OnLeanIBanSelectedEvent) _then) = __$OnLeanIBanSelectedEventCopyWithImpl;
@useResult
$Res call({
 LeanDestinationAccount selectedIBanAccount
});


$LeanDestinationAccountCopyWith<$Res> get selectedIBanAccount;

}
/// @nodoc
class __$OnLeanIBanSelectedEventCopyWithImpl<$Res>
    implements _$OnLeanIBanSelectedEventCopyWith<$Res> {
  __$OnLeanIBanSelectedEventCopyWithImpl(this._self, this._then);

  final _OnLeanIBanSelectedEvent _self;
  final $Res Function(_OnLeanIBanSelectedEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? selectedIBanAccount = null,}) {
  return _then(_OnLeanIBanSelectedEvent(
null == selectedIBanAccount ? _self.selectedIBanAccount : selectedIBanAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount,
  ));
}

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res> get selectedIBanAccount {
  
  return $LeanDestinationAccountCopyWith<$Res>(_self.selectedIBanAccount, (value) {
    return _then(_self.copyWith(selectedIBanAccount: value));
  });
}
}

/// @nodoc


class _OnEditAccountsModeToggledEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnEditAccountsModeToggledEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnEditAccountsModeToggledEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onEditAccountsModeToggled()';
}


}




/// @nodoc


class _OnTabChangedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnTabChangedEvent(this.index);
  

 final  int index;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnTabChangedEventCopyWith<_OnTabChangedEvent> get copyWith => __$OnTabChangedEventCopyWithImpl<_OnTabChangedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnTabChangedEvent&&(identical(other.index, index) || other.index == index));
}


@override
int get hashCode => Object.hash(runtimeType,index);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onTabChanged(index: $index)';
}


}

/// @nodoc
abstract mixin class _$OnTabChangedEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnTabChangedEventCopyWith(_OnTabChangedEvent value, $Res Function(_OnTabChangedEvent) _then) = __$OnTabChangedEventCopyWithImpl;
@useResult
$Res call({
 int index
});




}
/// @nodoc
class __$OnTabChangedEventCopyWithImpl<$Res>
    implements _$OnTabChangedEventCopyWith<$Res> {
  __$OnTabChangedEventCopyWithImpl(this._self, this._then);

  final _OnTabChangedEvent _self;
  final $Res Function(_OnTabChangedEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? index = null,}) {
  return _then(_OnTabChangedEvent(
null == index ? _self.index : index // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _OnBankConnectionDeletedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnBankConnectionDeletedEvent({required this.bankAccountData});
  

 final  LeanDestinationAccountData bankAccountData;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnBankConnectionDeletedEventCopyWith<_OnBankConnectionDeletedEvent> get copyWith => __$OnBankConnectionDeletedEventCopyWithImpl<_OnBankConnectionDeletedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnBankConnectionDeletedEvent&&(identical(other.bankAccountData, bankAccountData) || other.bankAccountData == bankAccountData));
}


@override
int get hashCode => Object.hash(runtimeType,bankAccountData);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onBankConnectionDeleted(bankAccountData: $bankAccountData)';
}


}

/// @nodoc
abstract mixin class _$OnBankConnectionDeletedEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnBankConnectionDeletedEventCopyWith(_OnBankConnectionDeletedEvent value, $Res Function(_OnBankConnectionDeletedEvent) _then) = __$OnBankConnectionDeletedEventCopyWithImpl;
@useResult
$Res call({
 LeanDestinationAccountData bankAccountData
});


$LeanDestinationAccountDataCopyWith<$Res> get bankAccountData;

}
/// @nodoc
class __$OnBankConnectionDeletedEventCopyWithImpl<$Res>
    implements _$OnBankConnectionDeletedEventCopyWith<$Res> {
  __$OnBankConnectionDeletedEventCopyWithImpl(this._self, this._then);

  final _OnBankConnectionDeletedEvent _self;
  final $Res Function(_OnBankConnectionDeletedEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? bankAccountData = null,}) {
  return _then(_OnBankConnectionDeletedEvent(
bankAccountData: null == bankAccountData ? _self.bankAccountData : bankAccountData // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccountData,
  ));
}

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountDataCopyWith<$Res> get bankAccountData {
  
  return $LeanDestinationAccountDataCopyWith<$Res>(_self.bankAccountData, (value) {
    return _then(_self.copyWith(bankAccountData: value));
  });
}
}

/// @nodoc


class _OnManualIbanDeletedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnManualIbanDeletedEvent({required this.ibanAccount});
  

 final  LeanDestinationAccount ibanAccount;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnManualIbanDeletedEventCopyWith<_OnManualIbanDeletedEvent> get copyWith => __$OnManualIbanDeletedEventCopyWithImpl<_OnManualIbanDeletedEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnManualIbanDeletedEvent&&(identical(other.ibanAccount, ibanAccount) || other.ibanAccount == ibanAccount));
}


@override
int get hashCode => Object.hash(runtimeType,ibanAccount);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onManualIbanDeleted(ibanAccount: $ibanAccount)';
}


}

/// @nodoc
abstract mixin class _$OnManualIbanDeletedEventCopyWith<$Res> implements $WithdrawalSelectAccountLeanEventCopyWith<$Res> {
  factory _$OnManualIbanDeletedEventCopyWith(_OnManualIbanDeletedEvent value, $Res Function(_OnManualIbanDeletedEvent) _then) = __$OnManualIbanDeletedEventCopyWithImpl;
@useResult
$Res call({
 LeanDestinationAccount ibanAccount
});


$LeanDestinationAccountCopyWith<$Res> get ibanAccount;

}
/// @nodoc
class __$OnManualIbanDeletedEventCopyWithImpl<$Res>
    implements _$OnManualIbanDeletedEventCopyWith<$Res> {
  __$OnManualIbanDeletedEventCopyWithImpl(this._self, this._then);

  final _OnManualIbanDeletedEvent _self;
  final $Res Function(_OnManualIbanDeletedEvent) _then;

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? ibanAccount = null,}) {
  return _then(_OnManualIbanDeletedEvent(
ibanAccount: null == ibanAccount ? _self.ibanAccount : ibanAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount,
  ));
}

/// Create a copy of WithdrawalSelectAccountLeanEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res> get ibanAccount {
  
  return $LeanDestinationAccountCopyWith<$Res>(_self.ibanAccount, (value) {
    return _then(_self.copyWith(ibanAccount: value));
  });
}
}

/// @nodoc


class _OnRefreshIconPressedEvent implements WithdrawalSelectAccountLeanEvent {
  const _OnRefreshIconPressedEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnRefreshIconPressedEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawalSelectAccountLeanEvent.onRefreshIconPressed()';
}


}




/// @nodoc
mixin _$WithdrawalSelectAccountLeanState {

 LeanAccountProcessState get currentState; bool get isButtonLoading; bool get isEditingAccountsMode; WithdrawalPaymentMethod? get withdrawalPaymentMethod; String? get originRoute; List<LeanDestinationAccount> get leanDestinationAccounts; List<LeanDestinationAccountData> get bankAccountList; List<LeanDestinationAccount> get iBansList; int get selectedTabIndex; LeanDestinationAccount? get selectedIBanAccount; LeanDestinationAccount? get selectedBankAccount; bool get isAddIbanDisabled; bool get isAddBankAccountDisabled; bool get showRefreshIcon; bool get showEmptyPaymentDestination;
/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawalSelectAccountLeanStateCopyWith<WithdrawalSelectAccountLeanState> get copyWith => _$WithdrawalSelectAccountLeanStateCopyWithImpl<WithdrawalSelectAccountLeanState>(this as WithdrawalSelectAccountLeanState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawalSelectAccountLeanState&&(identical(other.currentState, currentState) || other.currentState == currentState)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.isEditingAccountsMode, isEditingAccountsMode) || other.isEditingAccountsMode == isEditingAccountsMode)&&(identical(other.withdrawalPaymentMethod, withdrawalPaymentMethod) || other.withdrawalPaymentMethod == withdrawalPaymentMethod)&&(identical(other.originRoute, originRoute) || other.originRoute == originRoute)&&const DeepCollectionEquality().equals(other.leanDestinationAccounts, leanDestinationAccounts)&&const DeepCollectionEquality().equals(other.bankAccountList, bankAccountList)&&const DeepCollectionEquality().equals(other.iBansList, iBansList)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex)&&(identical(other.selectedIBanAccount, selectedIBanAccount) || other.selectedIBanAccount == selectedIBanAccount)&&(identical(other.selectedBankAccount, selectedBankAccount) || other.selectedBankAccount == selectedBankAccount)&&(identical(other.isAddIbanDisabled, isAddIbanDisabled) || other.isAddIbanDisabled == isAddIbanDisabled)&&(identical(other.isAddBankAccountDisabled, isAddBankAccountDisabled) || other.isAddBankAccountDisabled == isAddBankAccountDisabled)&&(identical(other.showRefreshIcon, showRefreshIcon) || other.showRefreshIcon == showRefreshIcon)&&(identical(other.showEmptyPaymentDestination, showEmptyPaymentDestination) || other.showEmptyPaymentDestination == showEmptyPaymentDestination));
}


@override
int get hashCode => Object.hash(runtimeType,currentState,isButtonLoading,isEditingAccountsMode,withdrawalPaymentMethod,originRoute,const DeepCollectionEquality().hash(leanDestinationAccounts),const DeepCollectionEquality().hash(bankAccountList),const DeepCollectionEquality().hash(iBansList),selectedTabIndex,selectedIBanAccount,selectedBankAccount,isAddIbanDisabled,isAddBankAccountDisabled,showRefreshIcon,showEmptyPaymentDestination);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanState(currentState: $currentState, isButtonLoading: $isButtonLoading, isEditingAccountsMode: $isEditingAccountsMode, withdrawalPaymentMethod: $withdrawalPaymentMethod, originRoute: $originRoute, leanDestinationAccounts: $leanDestinationAccounts, bankAccountList: $bankAccountList, iBansList: $iBansList, selectedTabIndex: $selectedTabIndex, selectedIBanAccount: $selectedIBanAccount, selectedBankAccount: $selectedBankAccount, isAddIbanDisabled: $isAddIbanDisabled, isAddBankAccountDisabled: $isAddBankAccountDisabled, showRefreshIcon: $showRefreshIcon, showEmptyPaymentDestination: $showEmptyPaymentDestination)';
}


}

/// @nodoc
abstract mixin class $WithdrawalSelectAccountLeanStateCopyWith<$Res>  {
  factory $WithdrawalSelectAccountLeanStateCopyWith(WithdrawalSelectAccountLeanState value, $Res Function(WithdrawalSelectAccountLeanState) _then) = _$WithdrawalSelectAccountLeanStateCopyWithImpl;
@useResult
$Res call({
 LeanAccountProcessState currentState, bool isButtonLoading, bool isEditingAccountsMode, WithdrawalPaymentMethod? withdrawalPaymentMethod, String? originRoute, List<LeanDestinationAccount> leanDestinationAccounts, List<LeanDestinationAccountData> bankAccountList, List<LeanDestinationAccount> iBansList, int selectedTabIndex, LeanDestinationAccount? selectedIBanAccount, LeanDestinationAccount? selectedBankAccount, bool isAddIbanDisabled, bool isAddBankAccountDisabled, bool showRefreshIcon, bool showEmptyPaymentDestination
});


$LeanAccountProcessStateCopyWith<$Res> get currentState;$WithdrawalPaymentMethodCopyWith<$Res>? get withdrawalPaymentMethod;$LeanDestinationAccountCopyWith<$Res>? get selectedIBanAccount;$LeanDestinationAccountCopyWith<$Res>? get selectedBankAccount;

}
/// @nodoc
class _$WithdrawalSelectAccountLeanStateCopyWithImpl<$Res>
    implements $WithdrawalSelectAccountLeanStateCopyWith<$Res> {
  _$WithdrawalSelectAccountLeanStateCopyWithImpl(this._self, this._then);

  final WithdrawalSelectAccountLeanState _self;
  final $Res Function(WithdrawalSelectAccountLeanState) _then;

/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? currentState = null,Object? isButtonLoading = null,Object? isEditingAccountsMode = null,Object? withdrawalPaymentMethod = freezed,Object? originRoute = freezed,Object? leanDestinationAccounts = null,Object? bankAccountList = null,Object? iBansList = null,Object? selectedTabIndex = null,Object? selectedIBanAccount = freezed,Object? selectedBankAccount = freezed,Object? isAddIbanDisabled = null,Object? isAddBankAccountDisabled = null,Object? showRefreshIcon = null,Object? showEmptyPaymentDestination = null,}) {
  return _then(_self.copyWith(
currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as LeanAccountProcessState,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,isEditingAccountsMode: null == isEditingAccountsMode ? _self.isEditingAccountsMode : isEditingAccountsMode // ignore: cast_nullable_to_non_nullable
as bool,withdrawalPaymentMethod: freezed == withdrawalPaymentMethod ? _self.withdrawalPaymentMethod : withdrawalPaymentMethod // ignore: cast_nullable_to_non_nullable
as WithdrawalPaymentMethod?,originRoute: freezed == originRoute ? _self.originRoute : originRoute // ignore: cast_nullable_to_non_nullable
as String?,leanDestinationAccounts: null == leanDestinationAccounts ? _self.leanDestinationAccounts : leanDestinationAccounts // ignore: cast_nullable_to_non_nullable
as List<LeanDestinationAccount>,bankAccountList: null == bankAccountList ? _self.bankAccountList : bankAccountList // ignore: cast_nullable_to_non_nullable
as List<LeanDestinationAccountData>,iBansList: null == iBansList ? _self.iBansList : iBansList // ignore: cast_nullable_to_non_nullable
as List<LeanDestinationAccount>,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,selectedIBanAccount: freezed == selectedIBanAccount ? _self.selectedIBanAccount : selectedIBanAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount?,selectedBankAccount: freezed == selectedBankAccount ? _self.selectedBankAccount : selectedBankAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount?,isAddIbanDisabled: null == isAddIbanDisabled ? _self.isAddIbanDisabled : isAddIbanDisabled // ignore: cast_nullable_to_non_nullable
as bool,isAddBankAccountDisabled: null == isAddBankAccountDisabled ? _self.isAddBankAccountDisabled : isAddBankAccountDisabled // ignore: cast_nullable_to_non_nullable
as bool,showRefreshIcon: null == showRefreshIcon ? _self.showRefreshIcon : showRefreshIcon // ignore: cast_nullable_to_non_nullable
as bool,showEmptyPaymentDestination: null == showEmptyPaymentDestination ? _self.showEmptyPaymentDestination : showEmptyPaymentDestination // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountProcessStateCopyWith<$Res> get currentState {
  
  return $LeanAccountProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalPaymentMethodCopyWith<$Res>? get withdrawalPaymentMethod {
    if (_self.withdrawalPaymentMethod == null) {
    return null;
  }

  return $WithdrawalPaymentMethodCopyWith<$Res>(_self.withdrawalPaymentMethod!, (value) {
    return _then(_self.copyWith(withdrawalPaymentMethod: value));
  });
}/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res>? get selectedIBanAccount {
    if (_self.selectedIBanAccount == null) {
    return null;
  }

  return $LeanDestinationAccountCopyWith<$Res>(_self.selectedIBanAccount!, (value) {
    return _then(_self.copyWith(selectedIBanAccount: value));
  });
}/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res>? get selectedBankAccount {
    if (_self.selectedBankAccount == null) {
    return null;
  }

  return $LeanDestinationAccountCopyWith<$Res>(_self.selectedBankAccount!, (value) {
    return _then(_self.copyWith(selectedBankAccount: value));
  });
}
}


/// @nodoc


class _WithdrawalSelectAccountLeanState implements WithdrawalSelectAccountLeanState {
  const _WithdrawalSelectAccountLeanState({this.currentState = const LeanAccountProcessState.loading(), this.isButtonLoading = false, this.isEditingAccountsMode = false, this.withdrawalPaymentMethod, this.originRoute, final  List<LeanDestinationAccount> leanDestinationAccounts = const [], final  List<LeanDestinationAccountData> bankAccountList = const [], final  List<LeanDestinationAccount> iBansList = const [], this.selectedTabIndex = 0, this.selectedIBanAccount = null, this.selectedBankAccount = null, this.isAddIbanDisabled = false, this.isAddBankAccountDisabled = false, this.showRefreshIcon = false, this.showEmptyPaymentDestination = false}): _leanDestinationAccounts = leanDestinationAccounts,_bankAccountList = bankAccountList,_iBansList = iBansList;
  

@override@JsonKey() final  LeanAccountProcessState currentState;
@override@JsonKey() final  bool isButtonLoading;
@override@JsonKey() final  bool isEditingAccountsMode;
@override final  WithdrawalPaymentMethod? withdrawalPaymentMethod;
@override final  String? originRoute;
 final  List<LeanDestinationAccount> _leanDestinationAccounts;
@override@JsonKey() List<LeanDestinationAccount> get leanDestinationAccounts {
  if (_leanDestinationAccounts is EqualUnmodifiableListView) return _leanDestinationAccounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_leanDestinationAccounts);
}

 final  List<LeanDestinationAccountData> _bankAccountList;
@override@JsonKey() List<LeanDestinationAccountData> get bankAccountList {
  if (_bankAccountList is EqualUnmodifiableListView) return _bankAccountList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_bankAccountList);
}

 final  List<LeanDestinationAccount> _iBansList;
@override@JsonKey() List<LeanDestinationAccount> get iBansList {
  if (_iBansList is EqualUnmodifiableListView) return _iBansList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_iBansList);
}

@override@JsonKey() final  int selectedTabIndex;
@override@JsonKey() final  LeanDestinationAccount? selectedIBanAccount;
@override@JsonKey() final  LeanDestinationAccount? selectedBankAccount;
@override@JsonKey() final  bool isAddIbanDisabled;
@override@JsonKey() final  bool isAddBankAccountDisabled;
@override@JsonKey() final  bool showRefreshIcon;
@override@JsonKey() final  bool showEmptyPaymentDestination;

/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawalSelectAccountLeanStateCopyWith<_WithdrawalSelectAccountLeanState> get copyWith => __$WithdrawalSelectAccountLeanStateCopyWithImpl<_WithdrawalSelectAccountLeanState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawalSelectAccountLeanState&&(identical(other.currentState, currentState) || other.currentState == currentState)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.isEditingAccountsMode, isEditingAccountsMode) || other.isEditingAccountsMode == isEditingAccountsMode)&&(identical(other.withdrawalPaymentMethod, withdrawalPaymentMethod) || other.withdrawalPaymentMethod == withdrawalPaymentMethod)&&(identical(other.originRoute, originRoute) || other.originRoute == originRoute)&&const DeepCollectionEquality().equals(other._leanDestinationAccounts, _leanDestinationAccounts)&&const DeepCollectionEquality().equals(other._bankAccountList, _bankAccountList)&&const DeepCollectionEquality().equals(other._iBansList, _iBansList)&&(identical(other.selectedTabIndex, selectedTabIndex) || other.selectedTabIndex == selectedTabIndex)&&(identical(other.selectedIBanAccount, selectedIBanAccount) || other.selectedIBanAccount == selectedIBanAccount)&&(identical(other.selectedBankAccount, selectedBankAccount) || other.selectedBankAccount == selectedBankAccount)&&(identical(other.isAddIbanDisabled, isAddIbanDisabled) || other.isAddIbanDisabled == isAddIbanDisabled)&&(identical(other.isAddBankAccountDisabled, isAddBankAccountDisabled) || other.isAddBankAccountDisabled == isAddBankAccountDisabled)&&(identical(other.showRefreshIcon, showRefreshIcon) || other.showRefreshIcon == showRefreshIcon)&&(identical(other.showEmptyPaymentDestination, showEmptyPaymentDestination) || other.showEmptyPaymentDestination == showEmptyPaymentDestination));
}


@override
int get hashCode => Object.hash(runtimeType,currentState,isButtonLoading,isEditingAccountsMode,withdrawalPaymentMethod,originRoute,const DeepCollectionEquality().hash(_leanDestinationAccounts),const DeepCollectionEquality().hash(_bankAccountList),const DeepCollectionEquality().hash(_iBansList),selectedTabIndex,selectedIBanAccount,selectedBankAccount,isAddIbanDisabled,isAddBankAccountDisabled,showRefreshIcon,showEmptyPaymentDestination);

@override
String toString() {
  return 'WithdrawalSelectAccountLeanState(currentState: $currentState, isButtonLoading: $isButtonLoading, isEditingAccountsMode: $isEditingAccountsMode, withdrawalPaymentMethod: $withdrawalPaymentMethod, originRoute: $originRoute, leanDestinationAccounts: $leanDestinationAccounts, bankAccountList: $bankAccountList, iBansList: $iBansList, selectedTabIndex: $selectedTabIndex, selectedIBanAccount: $selectedIBanAccount, selectedBankAccount: $selectedBankAccount, isAddIbanDisabled: $isAddIbanDisabled, isAddBankAccountDisabled: $isAddBankAccountDisabled, showRefreshIcon: $showRefreshIcon, showEmptyPaymentDestination: $showEmptyPaymentDestination)';
}


}

/// @nodoc
abstract mixin class _$WithdrawalSelectAccountLeanStateCopyWith<$Res> implements $WithdrawalSelectAccountLeanStateCopyWith<$Res> {
  factory _$WithdrawalSelectAccountLeanStateCopyWith(_WithdrawalSelectAccountLeanState value, $Res Function(_WithdrawalSelectAccountLeanState) _then) = __$WithdrawalSelectAccountLeanStateCopyWithImpl;
@override @useResult
$Res call({
 LeanAccountProcessState currentState, bool isButtonLoading, bool isEditingAccountsMode, WithdrawalPaymentMethod? withdrawalPaymentMethod, String? originRoute, List<LeanDestinationAccount> leanDestinationAccounts, List<LeanDestinationAccountData> bankAccountList, List<LeanDestinationAccount> iBansList, int selectedTabIndex, LeanDestinationAccount? selectedIBanAccount, LeanDestinationAccount? selectedBankAccount, bool isAddIbanDisabled, bool isAddBankAccountDisabled, bool showRefreshIcon, bool showEmptyPaymentDestination
});


@override $LeanAccountProcessStateCopyWith<$Res> get currentState;@override $WithdrawalPaymentMethodCopyWith<$Res>? get withdrawalPaymentMethod;@override $LeanDestinationAccountCopyWith<$Res>? get selectedIBanAccount;@override $LeanDestinationAccountCopyWith<$Res>? get selectedBankAccount;

}
/// @nodoc
class __$WithdrawalSelectAccountLeanStateCopyWithImpl<$Res>
    implements _$WithdrawalSelectAccountLeanStateCopyWith<$Res> {
  __$WithdrawalSelectAccountLeanStateCopyWithImpl(this._self, this._then);

  final _WithdrawalSelectAccountLeanState _self;
  final $Res Function(_WithdrawalSelectAccountLeanState) _then;

/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? currentState = null,Object? isButtonLoading = null,Object? isEditingAccountsMode = null,Object? withdrawalPaymentMethod = freezed,Object? originRoute = freezed,Object? leanDestinationAccounts = null,Object? bankAccountList = null,Object? iBansList = null,Object? selectedTabIndex = null,Object? selectedIBanAccount = freezed,Object? selectedBankAccount = freezed,Object? isAddIbanDisabled = null,Object? isAddBankAccountDisabled = null,Object? showRefreshIcon = null,Object? showEmptyPaymentDestination = null,}) {
  return _then(_WithdrawalSelectAccountLeanState(
currentState: null == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as LeanAccountProcessState,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,isEditingAccountsMode: null == isEditingAccountsMode ? _self.isEditingAccountsMode : isEditingAccountsMode // ignore: cast_nullable_to_non_nullable
as bool,withdrawalPaymentMethod: freezed == withdrawalPaymentMethod ? _self.withdrawalPaymentMethod : withdrawalPaymentMethod // ignore: cast_nullable_to_non_nullable
as WithdrawalPaymentMethod?,originRoute: freezed == originRoute ? _self.originRoute : originRoute // ignore: cast_nullable_to_non_nullable
as String?,leanDestinationAccounts: null == leanDestinationAccounts ? _self._leanDestinationAccounts : leanDestinationAccounts // ignore: cast_nullable_to_non_nullable
as List<LeanDestinationAccount>,bankAccountList: null == bankAccountList ? _self._bankAccountList : bankAccountList // ignore: cast_nullable_to_non_nullable
as List<LeanDestinationAccountData>,iBansList: null == iBansList ? _self._iBansList : iBansList // ignore: cast_nullable_to_non_nullable
as List<LeanDestinationAccount>,selectedTabIndex: null == selectedTabIndex ? _self.selectedTabIndex : selectedTabIndex // ignore: cast_nullable_to_non_nullable
as int,selectedIBanAccount: freezed == selectedIBanAccount ? _self.selectedIBanAccount : selectedIBanAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount?,selectedBankAccount: freezed == selectedBankAccount ? _self.selectedBankAccount : selectedBankAccount // ignore: cast_nullable_to_non_nullable
as LeanDestinationAccount?,isAddIbanDisabled: null == isAddIbanDisabled ? _self.isAddIbanDisabled : isAddIbanDisabled // ignore: cast_nullable_to_non_nullable
as bool,isAddBankAccountDisabled: null == isAddBankAccountDisabled ? _self.isAddBankAccountDisabled : isAddBankAccountDisabled // ignore: cast_nullable_to_non_nullable
as bool,showRefreshIcon: null == showRefreshIcon ? _self.showRefreshIcon : showRefreshIcon // ignore: cast_nullable_to_non_nullable
as bool,showEmptyPaymentDestination: null == showEmptyPaymentDestination ? _self.showEmptyPaymentDestination : showEmptyPaymentDestination // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountProcessStateCopyWith<$Res> get currentState {
  
  return $LeanAccountProcessStateCopyWith<$Res>(_self.currentState, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalPaymentMethodCopyWith<$Res>? get withdrawalPaymentMethod {
    if (_self.withdrawalPaymentMethod == null) {
    return null;
  }

  return $WithdrawalPaymentMethodCopyWith<$Res>(_self.withdrawalPaymentMethod!, (value) {
    return _then(_self.copyWith(withdrawalPaymentMethod: value));
  });
}/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res>? get selectedIBanAccount {
    if (_self.selectedIBanAccount == null) {
    return null;
  }

  return $LeanDestinationAccountCopyWith<$Res>(_self.selectedIBanAccount!, (value) {
    return _then(_self.copyWith(selectedIBanAccount: value));
  });
}/// Create a copy of WithdrawalSelectAccountLeanState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanDestinationAccountCopyWith<$Res>? get selectedBankAccount {
    if (_self.selectedBankAccount == null) {
    return null;
  }

  return $LeanDestinationAccountCopyWith<$Res>(_self.selectedBankAccount!, (value) {
    return _then(_self.copyWith(selectedBankAccount: value));
  });
}
}

/// @nodoc
mixin _$LeanAccountProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeanAccountProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState()';
}


}

/// @nodoc
class $LeanAccountProcessStateCopyWith<$Res>  {
$LeanAccountProcessStateCopyWith(LeanAccountProcessState _, $Res Function(LeanAccountProcessState) __);
}


/// @nodoc


class LoadingState implements LeanAccountProcessState {
  const LoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.loading()';
}


}




/// @nodoc


class LoadedState implements LeanAccountProcessState {
  const LoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.loaded()';
}


}




/// @nodoc


class ErrorState implements LeanAccountProcessState {
  const ErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.error()';
}


}




/// @nodoc


class AccountDeletedSuccessfullyState implements LeanAccountProcessState {
  const AccountDeletedSuccessfullyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDeletedSuccessfullyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'LeanAccountProcessState.accountDeletedSuccessfully()';
}


}




// dart format on
