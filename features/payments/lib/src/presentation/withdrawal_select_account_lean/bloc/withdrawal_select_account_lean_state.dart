part of 'withdrawal_select_account_lean_bloc.dart';

@freezed
sealed class WithdrawalSelectAccountLeanState
    with _$WithdrawalSelectAccountLeanState {
  const factory WithdrawalSelectAccountLeanState({
    @Default(LeanAccountProcessState.loading())
    LeanAccountProcessState currentState,
    @Default(false) bool isButtonLoading,
    @Default(false) bool isEditingAccountsMode,
    WithdrawalPaymentMethod? withdrawalPaymentMethod,
    String? originRoute,
    @Default([]) List<LeanDestinationAccount> leanDestinationAccounts,
    @Default([]) List<LeanDestinationAccountData> bankAccountList,
    @Default([]) List<LeanDestinationAccount> iBansList,
    @Default(0) int selectedTabIndex,
    @Default(null) LeanDestinationAccount? selectedIBanAccount,
    @Default(null) LeanDestinationAccount? selectedBankAccount,
    @Default(false) bool isAddIbanDisabled,
    @Default(false) bool isAddBankAccountDisabled,
    @Default(false) bool showRefreshIcon,
    @Default(false) bool showEmptyPaymentDestination,
  }) = _WithdrawalSelectAccountLeanState;
}

@freezed
sealed class LeanAccountProcessState with _$LeanAccountProcessState {
  const factory LeanAccountProcessState.loading() = LoadingState;
  const factory LeanAccountProcessState.loaded() = LoadedState;
  const factory LeanAccountProcessState.error() = ErrorState;
  const factory LeanAccountProcessState.accountDeletedSuccessfully() =
      AccountDeletedSuccessfullyState;
}
