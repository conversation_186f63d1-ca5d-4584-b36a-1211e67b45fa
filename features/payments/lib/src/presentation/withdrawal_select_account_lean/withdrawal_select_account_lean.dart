import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/withdrawal_select_account_lean/bloc/withdrawal_select_account_lean_bloc.dart';
import 'package:payment/src/presentation/withdrawal_select_account_lean/widgets/withdraw_select_account_lean_loaded_state.dart';

class WithdrawalSelectAccountLean extends StatelessWidget {
  const WithdrawalSelectAccountLean({
    super.key,
    required this.withdrawalPaymentMethod,
    required this.originRoute,
  });

  final WithdrawalPaymentMethod withdrawalPaymentMethod;
  final String originRoute;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final styles = context.duploTextStyles;
    final theme = context.duploTheme;
    return BlocProvider<WithdrawalSelectAccountLeanBloc>(
      create:
          (_) =>
              diContainer<WithdrawalSelectAccountLeanBloc>()
                ..add(
                  WithdrawalSelectAccountLeanEvent.initArguments(
                    withdrawalPaymentMethod: withdrawalPaymentMethod,
                    originRoute: originRoute,
                  ),
                )
                ..add(
                  const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(),
                ),

      child: BlocListener<
        WithdrawalSelectAccountLeanBloc,
        WithdrawalSelectAccountLeanState
      >(
        listenWhen:
            (previous, current) =>
                current.currentState != previous.currentState &&
                current.currentState is AccountDeletedSuccessfullyState,
        listener: (listenerContext, state) {
          if (state.currentState is AccountDeletedSuccessfullyState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: DuploText(
                  text: localization.payments_account_deleted_successfully,
                  style: styles.textSm,
                  fontWeight: DuploFontWeight.medium,
                  color: theme.foreground.fgWhite,
                  textAlign: TextAlign.start,
                ),
                showCloseIcon: false,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 4),
              ),
            );
          }
        },
        child: BlocBuilder<
          WithdrawalSelectAccountLeanBloc,
          WithdrawalSelectAccountLeanState
        >(
          buildWhen:
              (previous, current) =>
                  current.currentState != previous.currentState,
          builder: (blocContext, state) {
            return switch (state.currentState) {
              LoadingState() ||
              AccountDeletedSuccessfullyState() => const LoadingView(),
              LoadedState() => WithdrawSelectAccountLeanLoadedState(
                paymentMethod: state.withdrawalPaymentMethod!,
              ),

              ErrorState() => Scaffold(
                body: Center(
                  child: DuploText(
                    text: 'error while fetching accounts',
                    style: DuploTextStyles.of(context).textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: DuploTheme.of(context).text.textPrimary,
                  ),
                ),
              ),
            };
          },
        ),
      ),
    );
  }
}
