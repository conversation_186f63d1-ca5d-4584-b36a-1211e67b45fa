import 'dart:async';
import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment_assets;
import 'package:payment/src/data/lean_destination_account_response/lean_destination_account_response.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/lean_bank_status.dart';
import 'package:payment/src/presentation/deposit_select_account_lean/widgets/lean_payment_source_waiting_view.dart';
import 'package:payment/src/presentation/widgets/lean/lean_no_accounts_found_view.dart';
import 'package:payment/src/presentation/widgets/lean/lean_no_ibans_found_view.dart';
import 'package:payment/src/presentation/widgets/lean/lean_payment_source_destination_item.dart';
import 'package:payment/src/presentation/widgets/lean/mappers/lean_payment_source_mappers.dart';
import 'package:payment/src/presentation/widgets/lean/models/lean_bank_connection_ui_model.dart';
import 'package:payment/src/presentation/withdrawal_select_account_lean/bloc/withdrawal_select_account_lean_bloc.dart';
import 'package:payment/src/presentation/withdrawal_select_account_lean/widgets/lean_iban_list_view.dart';
import 'package:payment/src/presentation/withdrawal_select_account_lean/widgets/tab_switcher_lean_dest_accounts.dart';
import 'package:prelude/prelude.dart';

class WithdrawSelectAccountLeanLoadedState extends StatefulWidget {
  const WithdrawSelectAccountLeanLoadedState({
    super.key,
    required this.paymentMethod,
  });

  final WithdrawalPaymentMethod paymentMethod;

  @override
  State<WithdrawSelectAccountLeanLoadedState> createState() =>
      _WithdrawSelectAccountLeanLoadedStateState();
}

class _WithdrawSelectAccountLeanLoadedStateState
    extends State<WithdrawSelectAccountLeanLoadedState> {
  Timer? _refreshDebounceTimer;

  @override
  void dispose() {
    _refreshDebounceTimer?.cancel();
    super.dispose();
  }

  /// Handles cool-off expiration with debouncing to prevent multiple simultaneous refreshes
  void _onCoolOffExpired(BuildContext blocContext) {
    // Cancel any existing timer
    _refreshDebounceTimer?.cancel();

    // Set a new timer with a short delay (e.g., 2 seconds)
    // This ensures that if multiple accounts expire at the same time,
    // only one refresh will be triggered
    _refreshDebounceTimer = Timer(const Duration(seconds: 2), () {
      if (mounted) {
        blocContext.read<WithdrawalSelectAccountLeanBloc>().add(
          const WithdrawalSelectAccountLeanEvent.getLeanDestinationAccounts(),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final textStyle = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title:
            "${localization.payments_withdraw_with} ${widget.paymentMethod.name}",
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DuploSpacing.spacing_md_8,
            ),
            child: DuploTap(
              key: const Key('editIcon'),
              onTap: () {
                context.read<WithdrawalSelectAccountLeanBloc>().add(
                  WithdrawalSelectAccountLeanEvent.onEditAccountsModeToggled(),
                );
              },
              child: Container(
                height: 40,
                width: 40,
                child: Center(
                  child: BlocBuilder<
                    WithdrawalSelectAccountLeanBloc,
                    WithdrawalSelectAccountLeanState
                  >(
                    buildWhen:
                        (previous, current) =>
                            current.isEditingAccountsMode !=
                            previous.isEditingAccountsMode,
                    builder: (blocContext, state) {
                      return _buildEditModeIcon(
                        theme,
                        state.isEditingAccountsMode,
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DuploText(
                text: localization.payments_saved_bank_accounts,
                style: textStyle.textXl,
                color: theme.text.textPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
              SizedBox(height: DuploSpacing.spacing_3xl_24),
              TabSwitcherLeanDestAccounts(),
              SizedBox(height: DuploSpacing.spacing_3xl_24),
              Expanded(
                child: BlocBuilder<
                  WithdrawalSelectAccountLeanBloc,
                  WithdrawalSelectAccountLeanState
                >(
                  buildWhen:
                      (previous, current) =>
                          previous.selectedTabIndex !=
                              current.selectedTabIndex ||
                          previous.isEditingAccountsMode !=
                              current.isEditingAccountsMode ||
                          previous.selectedIBanAccount !=
                              current.selectedIBanAccount ||
                          previous.selectedBankAccount !=
                              current.selectedBankAccount ||
                          previous.showEmptyPaymentDestination !=
                              current.showEmptyPaymentDestination,
                  builder: (blocContext, state) {
                    final isAccountTab = state.selectedTabIndex == 0;
                    final isAccountListEmpty = state.bankAccountList.isEmpty;
                    final isIBANListEmpty = state.iBansList.isEmpty;
                    return AnimatedSwitcher(
                      duration: const Duration(milliseconds: 100),
                      switchInCurve: Curves.easeIn,
                      switchOutCurve: Curves.easeOut,
                      transitionBuilder: (
                        Widget child,
                        Animation<double> animation,
                      ) {
                        final isGoingToTab1 = state.selectedTabIndex == 1;
                        return SlideTransition(
                          position: Tween<Offset>(
                            begin: Offset(isGoingToTab1 ? -1.0 : 1.0, 0.0),
                            end: Offset.zero,
                          ).animate(
                            CurvedAnimation(
                              parent: animation,
                              curve: Curves.easeOutCubic,
                            ),
                          ),
                          child: child,
                        );
                      },
                      layoutBuilder: (
                        Widget? currentChild,
                        List<Widget> previousChildren,
                      ) {
                        return Stack(
                          alignment: Alignment.topCenter,
                          children: <Widget>[
                            ...previousChildren,
                            if (currentChild != null) currentChild,
                          ],
                        );
                      },
                      child: _buildTabContent(
                        context,
                        blocContext,
                        state,
                        isAccountTab,
                        isAccountListEmpty,
                        isIBANListEmpty,
                      ),
                    );
                  },
                ),
              ),

              BlocBuilder<
                WithdrawalSelectAccountLeanBloc,
                WithdrawalSelectAccountLeanState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.selectedTabIndex != current.selectedTabIndex ||
                        previous.selectedBankAccount !=
                            current.selectedBankAccount ||
                        previous.selectedIBanAccount !=
                            current.selectedIBanAccount ||
                        previous.isAddIbanDisabled !=
                            current.isAddIbanDisabled ||
                        previous.isAddBankAccountDisabled !=
                            current.isAddBankAccountDisabled ||
                        previous.showRefreshIcon != current.showRefreshIcon ||
                        previous.isEditingAccountsMode !=
                            current.isEditingAccountsMode,

                builder: (blocContext, state) {
                  final bloc =
                      blocContext.read<WithdrawalSelectAccountLeanBloc>();
                  final isAccountTab = state.selectedTabIndex == 0;
                  final isEmpty = _isCurrentTabEmpty(state, isAccountTab);

                  // Check if refresh button should be shown
                  final shouldShowRefreshButton = _isRefreshButtonVisible(
                    state,
                    isAccountTab,
                  );

                  if (isEmpty) {
                    return DuploButton.defaultPrimary(
                      useFullWidth: true,
                      title:
                          isAccountTab
                              ? localization.payments_lean_connect_bank_account
                              : localization.payments_lean_connect_iban,
                      onTap:
                          () => bloc.add(
                            const WithdrawalSelectAccountLeanEvent.onAddNewAccountPressed(),
                          ),
                      trailingIcon: Assets.images.lock.keyName,
                    );
                  }

                  return Column(
                    children: [
                      DuploButton.tertiary(
                        useFullWidth: true,
                        isDisabled:
                            isAccountTab
                                ? state.isAddBankAccountDisabled
                                : state.isAddIbanDisabled,
                        title:
                            isAccountTab
                                ? localization.payments_add_new_account
                                : localization.payments_add_new_iban,
                        onTap:
                            () => bloc.add(
                              const WithdrawalSelectAccountLeanEvent.onAddNewAccountPressed(),
                            ),
                        leadingIcon: Assets.images.plus.keyName,
                        // iconColor: theme.button.buttonTertiaryFg,
                      ),
                      if (!state.isEditingAccountsMode) ...[
                        SizedBox(height: DuploSpacing.spacing_xl_16),
                        // Show refresh button or continue button
                        if (shouldShowRefreshButton)
                          DuploButton.defaultPrimary(
                            useFullWidth: true,
                            title: localization.payments_refresh,
                            onTap:
                                () => bloc.add(
                                  const WithdrawalSelectAccountLeanEvent.onRefreshIconPressed(),
                                ),
                            leadingIcon:
                                payment_assets.Assets.images.refreshCcw.keyName,
                          )
                        else
                          DuploButton.defaultPrimary(
                            useFullWidth: true,
                            isDisabled:
                                isAccountTab
                                    ? state.selectedBankAccount == null
                                    : state.selectedIBanAccount == null,

                            title: localization.payments_continue,
                            onTap:
                                () => bloc.add(
                                  const WithdrawalSelectAccountLeanEvent.onConfirmButtonPressed(),
                                ),
                            trailingIcon: Assets.images.chevronRight.keyName,
                          ),
                      ],
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteBankConnectionBottomSheet(
    BuildContext context,
    BuildContext blocContext,
    LeanDestinationAccountData bankAccountData,
  ) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;

    DuploErrorSheet.show<void>(
      context: context,
      bodyTitle: localization.payments_delete_lean_account_title,
      bodySubTitle: localization.payments_delete_lean_account_description,
      hideCloseButton: true,
      textAlign: TextAlign.center,
      customIcon: payment_assets.Assets.images.deleteAccountIc.svg(
        width: 160,
        height: 160,
      ),
      actionWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: DuploSpacing.spacing_3xl_24),
          DuploText(
            text: localization.payments_lean_account_nickname,
            style: textStyle.textMd,
            color: theme.text.textPrimary,
            fontWeight: DuploFontWeight.semiBold,
          ),
          SizedBox(height: DuploSpacing.spacing_xxs_2),
          DuploText(
            text: bankAccountData.bankIdentifier,
            style: textStyle.textXs,
            color: theme.text.textTertiary,
            fontWeight: DuploFontWeight.regular,
          ),
          SizedBox(height: DuploSpacing.spacing_4xl_32),
          DuploButton.sellPrimary(
            title: localization.payments_delete_lean_account_button,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
              blocContext.read<WithdrawalSelectAccountLeanBloc>().add(
                WithdrawalSelectAccountLeanEvent.onBankConnectionDeleted(
                  bankAccountData: bankAccountData,
                ),
              );
            },
          ),
          SizedBox(height: DuploSpacing.spacing_md_8),
          DuploButton.secondary(
            title: localization.payments_cancel,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showDeleteIbanBottomSheet(
    BuildContext context,
    BuildContext blocContext,
    LeanDestinationAccount iban,
  ) {
    final localization = EquitiLocalization.of(context);
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;

    DuploErrorSheet.show<void>(
      context: context,
      bodyTitle: localization.payments_delete_lean_account_title,
      bodySubTitle: localization.payments_delete_lean_account_description,
      hideCloseButton: true,
      textAlign: TextAlign.center,
      customIcon: payment_assets.Assets.images.deleteAccountIc.svg(
        width: 160,
        height: 160,
      ),
      actionWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: DuploSpacing.spacing_3xl_24),
          DuploText(
            text: localization.payments_lean_account_nickname,
            style: textStyle.textMd,
            color: theme.text.textPrimary,
            fontWeight: DuploFontWeight.semiBold,
          ),
          SizedBox(height: DuploSpacing.spacing_xxs_2),
          DuploText(
            text:
                iban.bankIdentifier ?? localization.payments_lean_iban_fallback,
            style: textStyle.textXs,
            color: theme.text.textTertiary,
            fontWeight: DuploFontWeight.regular,
          ),
          SizedBox(height: DuploSpacing.spacing_4xl_32),
          DuploButton.sellPrimary(
            title: localization.payments_delete_lean_account_button,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
              blocContext.read<WithdrawalSelectAccountLeanBloc>().add(
                WithdrawalSelectAccountLeanEvent.onManualIbanDeleted(
                  ibanAccount: iban,
                ),
              );
            },
          ),
          SizedBox(height: DuploSpacing.spacing_md_8),
          DuploButton.secondary(
            title: localization.payments_cancel,
            useFullWidth: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  /// Builds the appropriate icon for edit mode toggle button
  /// Shows close icon when in edit mode, edit icon otherwise
  Widget _buildEditModeIcon(DuploThemeData theme, bool isEditingMode) {
    if (isEditingMode) {
      // Show close icon in edit mode
      return Assets.images.closeIc.svg(
        width: 16,
        height: 16,
        colorFilter: ColorFilter.mode(
          theme.foreground.fgSecondary,
          BlendMode.srcIn,
        ),
      );
    }

    // Show edit icon in normal mode
    return payment_assets.Assets.images.editIcon.svg(
      width: 22,
      height: 22,
      colorFilter: ColorFilter.mode(
        theme.foreground.fgSecondary,
        BlendMode.srcIn,
      ),
    );
  }

  /// Builds the content for the selected tab (Bank Accounts or IBANs)
  Widget _buildTabContent(
    BuildContext context,
    BuildContext blocContext,
    WithdrawalSelectAccountLeanState state,
    bool isAccountTab,
    bool isAccountListEmpty,
    bool isIBANListEmpty,
  ) {
    if (isAccountTab) {
      // Bank Accounts tab
      if (isAccountListEmpty) {
        return state.showEmptyPaymentDestination
            ? Padding(
              padding: const EdgeInsets.symmetric(
                vertical: DuploSpacing.spacing_3xl_24,
              ),
              child: LeanPaymentSourceWaitingView(),
            )
            : LeanNoAccountsFoundView();
      }
      return _buildBankAccountsList(context, blocContext, state);
    } // IBANs tab
    if (isIBANListEmpty) {
      return LeanNoIBansFoundView();
    }
    return _buildIBANsList(context, blocContext, state);
  }

  /// Builds the list of bank accounts
  Widget _buildBankAccountsList(
    BuildContext context,
    BuildContext blocContext,
    WithdrawalSelectAccountLeanState state,
  ) {
    return SingleChildScrollView(
      child: Column(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (ctx, index) {
              final bankAccountData = state.bankAccountList[index];
              final bankAccountUiModel = bankAccountData.toUiModel();

              // Find selected account UI model
              final selectedAccountUiModel =
                  state.selectedBankAccount != null
                      ? bankAccountData.destinations
                          .firstOrNullWhere(
                            (dest) => dest.id == state.selectedBankAccount?.id,
                          )
                          ?.toAccountUiModel()
                      : null;

              return LeanPaymentSourceDestinationItem(
                bankConnection: bankAccountUiModel,
                selectedAccount: selectedAccountUiModel,
                isEditingAccountsMode: state.isEditingAccountsMode,
                showDeleteOnChildren: false,
                onAccountSelected: (selectedAccountUi) {
                  _handleBankAccountSelected(
                    context,
                    bankAccountData,
                    selectedAccountUi,
                  );
                },
                onAccountDeleted: (accountUi) {
                  // This won't be called since showDeleteOnChildren is false
                  log(
                    'This should not be called since showDeleteOnChildren is false',
                  );
                },
                onBankConnectionDeleted: () {
                  _handleBankConnectionDeleted(
                    context,
                    blocContext,
                    bankAccountData,
                  );
                },
                onCoolOffExpired: () => _onCoolOffExpired(blocContext),
              );
            },
            separatorBuilder: (ctx, index) {
              return SizedBox(height: DuploSpacing.spacing_lg_12);
            },
            itemCount: state.bankAccountList.length,
          ),
          SizedBox(height: DuploSpacing.spacing_3xl_24),
          if (state.showEmptyPaymentDestination) LeanPaymentSourceWaitingView(),
        ],
      ),
    );
  }

  /// Builds the list of IBANs
  Widget _buildIBANsList(
    BuildContext context,
    BuildContext blocContext,
    WithdrawalSelectAccountLeanState state,
  ) {
    return LeanIBanListView(
      onAccountSelected: (LeanDestinationAccount item) {
        // Track analytics for IBAN selection
        final analytics = diContainer<WithdrawAnalyticsEvent>();
        analytics.withdrawLeanDestinationAccountSelected(
          destinationId: item.id,
          bankIdentifier: item.bankIdentifier ?? '',
          ibanLast4:
              item.accountNumber != null && item.accountNumber!.length > 4
                  ? item.accountNumber!.substring(
                    item.accountNumber!.length - 4,
                  )
                  : null,
          isManual: item.isManual,
        );
        context.read<WithdrawalSelectAccountLeanBloc>().add(
          WithdrawalSelectAccountLeanEvent.onLeanIBanSelected(item),
        );
      },
      onPaymentSourceDeleted: (LeanDestinationAccount item) {
        _showDeleteIbanBottomSheet(context, blocContext, item);
      },
      isEditingAccountsMode: state.isEditingAccountsMode,
      iBansList: state.iBansList,
      selectedIBanAccount: state.selectedIBanAccount,
    );
  }

  /// Handles bank account selection
  void _handleBankAccountSelected(
    BuildContext context,
    LeanDestinationAccountData bankAccountData,
    LeanAccountUiModel selectedAccountUi,
  ) {
    // Find the original LeanDestinationAccount by id
    final originalAccount = bankAccountData.destinations.firstOrNullWhere(
      (dest) => dest.id == selectedAccountUi.id,
    );

    if (originalAccount != null) {
      // Track analytics for bank account selection
      final analytics = diContainer<WithdrawAnalyticsEvent>();
      analytics.withdrawLeanDestinationAccountSelected(
        destinationId: originalAccount.id,
        bankIdentifier: originalAccount.bankIdentifier ?? '',
        accountId: originalAccount.accountNumber,
        isManual: originalAccount.isManual,
      );
      context.read<WithdrawalSelectAccountLeanBloc>().add(
        WithdrawalSelectAccountLeanEvent.onLeanBankAccountSelected(
          originalAccount,
        ),
      );
    }
  }

  /// Handles bank connection deletion
  void _handleBankConnectionDeleted(
    BuildContext context,
    BuildContext blocContext,
    LeanDestinationAccountData bankAccountData,
  ) {
    _showDeleteBankConnectionBottomSheet(context, blocContext, bankAccountData);
  }

  /// Determines if the current tab is empty
  bool _isCurrentTabEmpty(
    WithdrawalSelectAccountLeanState state,
    bool isAccountTab,
  ) {
    if (isAccountTab) {
      return state.bankAccountList.isEmpty;
    }
    return state.iBansList.isEmpty;
  }

  /// Determines if the refresh button should be visible
  /// Refresh button is shown when:
  /// - showRefreshIcon is true
  /// - On the bank account tab
  /// - There are bank connections in progress
  /// - No account is selected
  bool _isRefreshButtonVisible(
    WithdrawalSelectAccountLeanState state,
    bool isAccountTab,
  ) {
    if (!isAccountTab) {
      return false;
    }

    final isAnyAccountInProgress = state.bankAccountList.any(
      (bankData) => bankData.status == LeanBankStatus.IN_PROGRESS,
    );
    final isAnySubAccountInAwaitingBeneficiary = state.bankAccountList.any(
      (bankData) => bankData.destinations.any(
        (account) =>
            account.status == LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF,
      ),
    );

    return (isAnyAccountInProgress || isAnySubAccountInAwaitingBeneficiary) &&
        state.selectedBankAccount == null;
  }
}
