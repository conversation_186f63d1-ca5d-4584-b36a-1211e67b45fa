import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment_assets;
import 'package:flutter/material.dart';
import 'package:payment/src/data/lean_destination_account_response/lean_destination_account_response.dart';
import 'package:payment/src/domain/data/lean_bank_status.dart';
import 'package:payment/src/presentation/widgets/lean/saved_bank_account_base_chip_parent.dart';

class LeanIBanListView extends StatelessWidget {
  const LeanIBanListView({
    super.key,
    required this.isEditingAccountsMode,
    required this.iBansList,
    required this.selectedIBanAccount,
    required this.onAccountSelected,
    required this.onPaymentSourceDeleted,
  });
  final bool isEditingAccountsMode;
  final LeanDestinationAccount? selectedIBanAccount;
  final List<LeanDestinationAccount> iBansList;
  final void Function(LeanDestinationAccount account) onAccountSelected;
  final void Function(LeanDestinationAccount account) onPaymentSourceDeleted;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: DuploSpacing.spacing_3xl_24),
      child: ListView.separated(
        itemBuilder: (ctx, index) {
          final item = iBansList[index];
          final status = item.status;
          return Container(
            decoration: BoxDecoration(
              border: Border.all(color: _getBorderColor(theme, status)),
              borderRadius: _getBorderRadius(status),
              color: _getBackgroundColor(theme, status),
            ),
            child: Column(
              children: [
                DuploTap(
                  key: Key('leanDestinationAccount_${item.id}'),
                  onTap: () => _handleTap(item),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: DuploSpacing.spacing_xl_16,
                      vertical:
                          status == LeanAccountStatus.ACTIVE
                              ? DuploSpacing.spacing_lg_12
                              : DuploSpacing.spacing_3xl_24,
                    ),
                    decoration: BoxDecoration(
                      border: _getInnerBorder(theme, status),
                      borderRadius: _getBorderRadius(status),
                      color: _getIBanBackground(theme, status),
                    ),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(
                            DuploRadius.radius_md_8,
                          ),
                          child: Container(
                            color: theme.background.bgTertiary,
                            height: 48,
                            width: 48,
                            child: Center(
                              child: payment_assets
                                  .Assets
                                  .images
                                  .bankPlaceholder
                                  .svg(height: 32, width: 32),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: DuploSpacing.spacing_md_8,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DuploText(
                                  text:
                                      item.bankIdentifier ??
                                      localization
                                          .payments_lean_bank_name_fallback,
                                  style: textStyle.textMd,
                                  color: theme.text.textSecondary,
                                  fontWeight: DuploFontWeight.semiBold,
                                ),
                                Wrap(
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    _buildChip(
                                      text: item.currencyIsoCode ?? '',
                                      textStyle: textStyle.textXxs,
                                      color: theme.text.textSecondary,
                                    ),
                                    SizedBox(width: 8),
                                    DuploText(
                                      text:
                                          "${localization.payments_lean_iban_prefix}",
                                      style: textStyle.textXs,
                                      color: theme.text.textSecondary,
                                      textAlign: TextAlign.start,
                                      fontWeight: DuploFontWeight.regular,
                                    ),
                                    DuploText(
                                      text: "${item.iban}",
                                      style: textStyle.textXs,
                                      color: theme.text.textSecondary,
                                      textAlign: TextAlign.start,
                                      fontWeight: DuploFontWeight.regular,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        _buildTrailingWidget(theme, item, status),
                      ],
                    ),
                  ),
                ),
                if (status != LeanAccountStatus.ACTIVE)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DuploSpacing.spacing_3xl_24,
                      vertical: DuploSpacing.spacing_lg_12,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          radius: 16,
                          backgroundColor: _getStatusIconBackgroundColor(
                            theme,
                            status,
                          ),
                          child: _returnBankConnectionStatusIcon(theme, status),
                        ),
                        const SizedBox(width: DuploSpacing.spacing_xl_16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DuploText(
                                text: _getBankConnectionStatusTitle(
                                  status,
                                  localization,
                                ),
                                style: textStyle.textSm,
                                fontWeight: DuploFontWeight.semiBold,
                                color: theme.text.textSecondary,
                              ),
                              const SizedBox(height: DuploSpacing.spacing_xs_4),
                              DuploText(
                                text: _getBankConnectionStatusDescription(
                                  status,
                                  localization,
                                ),
                                textAlign: TextAlign.start,
                                style: textStyle.textXs,
                                fontWeight: DuploFontWeight.regular,
                                color: theme.text.textTertiary,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
        separatorBuilder: (ctx, index) {
          return SizedBox(height: DuploSpacing.spacing_lg_12);
        },
        itemCount: iBansList.length,
      ),
    );
  }

  /// Handles tap on IBAN item
  /// In edit mode: triggers delete, in normal mode: triggers selection
  void _handleTap(LeanDestinationAccount item) {
    if (isEditingAccountsMode) {
      // In edit mode: delete the account
      onPaymentSourceDeleted(item);
    } else {
      // In normal mode: select the account only if it is active
      if (item.status != LeanAccountStatus.ACTIVE) {
        return;
      }
      // In normal mode: select the account
      onAccountSelected(item);
    }
  }

  Widget _buildChip({
    required String text,
    required DuploTextStyle textStyle,
    required Color color,
  }) {
    return text.isEmpty
        ? SizedBox.shrink()
        : SavedBankAccountBaseChipParent(
          child: DuploText(
            text: text,
            style: textStyle,
            fontWeight: DuploFontWeight.medium,
            color: color,
          ),
        );
  }

  /// Builds the trailing widget for IBAN item
  /// Shows delete icon in edit mode, radio button in normal mode
  Widget _buildTrailingWidget(
    DuploThemeData theme,
    LeanDestinationAccount item,
    LeanAccountStatus status,
  ) {
    if (isEditingAccountsMode) {
      // Show delete icon in edit mode
      return payment_assets.Assets.images.deleteIc.svg(
        height: 20,
        width: 20,
        colorFilter: ColorFilter.mode(
          theme.button.buttonTertiaryErrorFg,
          BlendMode.srcIn,
        ),
      );
    }
    if (status != LeanAccountStatus.ACTIVE) {
      // Show delete icon in edit mode
      return payment_assets.Assets.images.ban.svg(height: 16, width: 16);
    }

    // Show radio button in normal mode
    final isSelected = selectedIBanAccount?.id == item.id;
    if (isSelected) {
      return Assets.images.radioBase.svg(height: 20, width: 20);
    }
    return Assets.images.emptyRadioBase.svg();
  }

  Color _getBorderColor(DuploThemeData theme, LeanAccountStatus status) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return theme.border.borderSecondary;
      default:
        return theme.utility.utilityError200;
    }
  }

  Color _getBackgroundColor(DuploThemeData theme, LeanAccountStatus status) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return theme.background.bgPrimary;
      default:
        return theme.utility.utilityError50;
    }
  }

  Border _getInnerBorder(DuploThemeData theme, LeanAccountStatus status) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return Border.all(color: theme.border.borderSecondary);
      default:
        return Border(bottom: BorderSide(color: theme.utility.utilityError200));
    }
  }

  Color _getIBanBackground(DuploThemeData theme, LeanAccountStatus status) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return theme.background.bgPrimary;
      default:
        return theme.background.bgSecondary;
    }
  }

  Color _getStatusIconBackgroundColor(
    DuploThemeData theme,
    LeanAccountStatus status,
  ) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return theme.background.bgPrimary;
      default:
        return theme.utility.utilityError100;
    }
  }

  Widget _returnBankConnectionStatusIcon(
    DuploThemeData theme,
    LeanAccountStatus status,
  ) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return const SizedBox.shrink();
      default:
        return Assets.images.duploAlertWarning.svg(
          height: 16,
          width: 16,
          colorFilter: ColorFilter.mode(
            theme.icon.iconFeaturedLightFgError,
            BlendMode.srcIn,
          ),
        );
    }
  }

  String _getBankConnectionStatusTitle(
    LeanAccountStatus status,
    EquitiLocalization localization,
  ) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return '';
      default:
        return localization.payments_lean_account_disabled;
    }
  }

  String _getBankConnectionStatusDescription(
    LeanAccountStatus status,
    EquitiLocalization localization,
  ) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return '';
      default:
        return localization.payments_lean_account_disabled_description;
    }
  }

  BorderRadius _getBorderRadius(LeanAccountStatus status) {
    switch (status) {
      case LeanAccountStatus.ACTIVE:
        return BorderRadius.circular(DuploRadius.radius_sm_6);
      default:
        return BorderRadius.circular(DuploRadius.radius_xl_12);
    }
  }
}
