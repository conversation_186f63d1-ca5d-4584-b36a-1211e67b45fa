import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as payment;
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/bloc/withdraw_card_bloc.dart';

class EmptyCardView extends StatelessWidget {
  const EmptyCardView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    final bloc = context.read<WithdrawCardBloc>();
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(title: localization.payments_withdraw_to_bank_card),
      body: Padding(
        padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    payment.Assets.images.noCard.svg(),
                    SizedBox(height: DuploSpacing.spacing_lg_12),
                    DuploText(
                      text: localization.payments_no_cards_found,
                      style: textStyle.textXl,
                      textAlign: TextAlign.center,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                    ),
                    SizedBox(height: DuploSpacing.spacing_md_8),
                    DuploText(
                      text: localization.payments_no_cards_found_description,
                      style: textStyle.textSm,
                      textAlign: TextAlign.center,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                    ),
                  ],
                ),
              ),
              DuploButton.secondary(
                useFullWidth: true,
                title: localization.payments_choose_another_method,
                onTap:
                    () => bloc.add(
                      const WithdrawCardEvent.onChooseAnotherMethodPressed(),
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
