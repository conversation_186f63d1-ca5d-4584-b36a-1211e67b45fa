import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:lean_sdk_flutter/lean_sdk_flutter.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';

import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/data/deposit_response/deposit_response.dart';
import 'package:payment/src/domain/exceptions/deposit_exception/deposit_exception.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/usecase/get_deposit_details_usecase.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/utils/payment/apple_pay_utils.dart';
import 'package:payment/src/utils/payment/google_pay_utils.dart';

part 'deposit_accounts_and_amount_bloc.freezed.dart';
part 'deposit_accounts_and_amount_event.dart';
part 'deposit_accounts_and_amount_state.dart';

class DepositAccountsAndAmountBloc
    extends Bloc<DepositAccountsAndAmountEvent, DepositAccountsAndAmountState> {
  final GetDepositDetailsUsecase _getDepositDetailsUsecase;
  final PaymentNavigation _paymentNavigation;
  final GooglePayUtils _googlePayUtils;
  final ApplePayUtils _applePayUtils;
  final DepositAnalyticsEvent _depositAnalyticsEvent;

  DepositAccountsAndAmountBloc({
    required GetDepositDetailsUsecase getDepositDetailsUsecase,
    required PaymentNavigation paymentNavigation,
    required GooglePayUtils googlePayUtils,
    required ApplePayUtils applePayUtils,
    required DepositAnalyticsEvent depositAnalyticsEvent,
  }) : _paymentNavigation = paymentNavigation,
       _getDepositDetailsUsecase = getDepositDetailsUsecase,
       _googlePayUtils = googlePayUtils,
       _applePayUtils = applePayUtils,
       _depositAnalyticsEvent = depositAnalyticsEvent,
       super(DepositAccountsAndAmountState()) {
    on<_OnAccountChange>(_onAccountChange);
    on<_NaviagteToSelectedPaymentMethod>(_navigateToSelectedPaymentMethod);
    on<_ChangeButtonState>(_changeButtonState);
    on<_OnAmountChange>(_onAmountChange);
    on<_GetDepositDetails>(_getDepositDetails);
    on<_OnApplePayResult>(_onApplePayResult);
    on<_OnGooglePayResult>(_onGooglePayResult);
    on<OnPaymentSuccessContinuePressed>(_onPaymentSuccessContinuePressed);
    on<OnPaymentMethodChange>(_onPaymentMethodChange);
    on<ResetProcessStateEvent>(_resetProcessState);
    on<_InitArgs>(_initArgs);
    on<_OnLeanPaymentCallback>(_handleLeanPaymentCallback);
  }

  FutureOr<void> _onAccountChange(
    _OnAccountChange event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    _depositAnalyticsEvent.depositAccountSelected(
      type: event.account.accountType.name,
      accountCurrency: event.account.homeCurrency,
    );
    emit(
      state.copyWith(
        selectedAccount: event.account,
        // resetting existing amount and conversion rate details on account change
        accountCurrencyAmount: '',
        selectedCurrencyAmount: '',
        conversionRateString: '',
        selectedCurrency: '',
        isButtonEnabled: false,
      ),
    );
  }

  FutureOr<void> _navigateToSelectedPaymentMethod(
    _NaviagteToSelectedPaymentMethod event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    switch (event.mop) {
      default:
        break;
    }
  }

  FutureOr<void> _changeButtonState(
    _ChangeButtonState event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(state.copyWith(isButtonEnabled: event.isValid, errorMessage: null));
  }

  FutureOr<void> _onAmountChange(
    _OnAmountChange event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(
      state.copyWith(
        accountCurrencyAmount: event.accountCurrencyAmount,
        selectedCurrencyAmount: event.selectedCurrencyAmount,
        ratesModel: event.ratesModel,
        conversionRateString:
            event.conversionRateString ??
            '1 ${state.selectedAccount?.homeCurrency ?? ''} = 1 ${state.selectedAccount?.homeCurrency ?? ''}',
        selectedCurrency:
            event.selectedCurrency ?? state.selectedAccount?.homeCurrency ?? '',
      ),
    );
  }

  FutureOr<void> _getDepositDetails(
    _GetDepositDetails event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) async {
    try {
      FocusManager.instance.primaryFocus?.unfocus();
      SystemChannels.textInput.invokeMethod('TextInput.hide');
      if (!isClosed) {
        switch (event.paymentMethod.mop) {
          case DepositMop.apple_pay:
          case DepositMop.google_pay:
            emit(state.copyWith(isFullPageLoadingEnabled: true));
            break;
          case DepositMop.card:
          case DepositMop.bridgerpay:
          case DepositMop.bank:
          case DepositMop.crypto:
          case DepositMop.unknown:
          case DepositMop.lean:
            emit(state.copyWith(isButtonLoading: true));
        }
      }
      final result =
          await _getDepositDetailsUsecase(
            tradingAccountId: state.selectedAccount!.recordId,
            clientId: state.selectedAccount!.clientId ?? '',
            accountCurrency: state.selectedAccount!.homeCurrency,
            selectedCurrency: state.selectedCurrency,
            accountCurrencyAmount: double.parse(state.accountCurrencyAmount),
            selectedCurrencyAmount: double.parse(state.selectedCurrencyAmount),
            conversionRateToAccountCurrency: state.ratesModel?.rate ?? 1,
            mop: event.paymentMethod.mop,
            conversionRateString: state.conversionRateString,
            isDarkTheme: event.isDarkTheme,
            leanAccountId: state.leanAccountArgs?.selectedLeanAccount?.id,
          ).run();

      result.fold(
        (exception) {
          if (exception is DepositException) {
            switch (exception) {
              default:
                if (!isClosed) {
                  emit(
                    state.copyWith(
                      isButtonLoading: false,
                      errorMessage: exception.message,
                      isFullPageLoadingEnabled: false,
                    ),
                  );
                }
            }
          } else {
            log('Unexpected Error: ${exception.toString()}');
            if (!isClosed) {
              emit(
                state.copyWith(
                  isButtonLoading: false,
                  errorMessage: "Something went wrong! Please try again later.",
                  isFullPageLoadingEnabled: false,
                ),
              );
            }
          }
          addError(exception);
        },
        (r) {
          log(
            "paymentMethod: ${event.paymentMethod.mop} and here is result ${r.data.toJson()}",
          );
          if (!isClosed) emit(state.copyWith(errorMessage: null));
          _depositAnalyticsEvent.depositInitiated(
            transactionId: r.data.transactionId,
            amount: state.selectedCurrencyAmount,
            convertedAmount: state.accountCurrencyAmount,
            accountCurrency: state.selectedAccount!.homeCurrency,
            selectedCurrency: state.selectedCurrency,
            conversionRate: state.ratesModel?.rate ?? 1,
          );

          switch (event.paymentMethod.mop) {
            case DepositMop.card:
              _handleCardPayment(r.data, emit);
              break;
            case DepositMop.apple_pay:
              _handleApplePayPayment(r.data);
              break;
            case DepositMop.google_pay:
              _handleGooglePayPayment(r.data, emit);
              break;
            case DepositMop.bridgerpay:
              _handleBridgerPayPayment(r.data, emit);
              break;
            case DepositMop.lean:
              _handleLeanPayment(r.data, emit);
            default:
              break;
          }
          if (!isClosed) {
            emit(state.copyWith(isFullPageLoadingEnabled: false));
          }
        },
      );
    } on Exception catch (_) {
      if (!isClosed) {
        switch (event.paymentMethod.mop) {
          case DepositMop.apple_pay:
          case DepositMop.google_pay:
            emit(
              state.copyWith(
                isFullPageLoadingEnabled: false,
                errorMessage: "Something went wrong! Please try again later.",
              ),
            );
            break;
          case DepositMop.card:
          case DepositMop.bridgerpay:
          case DepositMop.bank:
          case DepositMop.crypto:
          case DepositMop.unknown:
          case DepositMop.lean:
            emit(
              state.copyWith(
                isButtonLoading: false,
                errorMessage: "Something went wrong! Please try again later.",
              ),
            );
        }
      }
    }
  }

  void _handleCardPayment(
    DepositResponseData data,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(isButtonLoading: false));
    }
    _paymentNavigation.goToEquitiPayCardsScreen(
      data.redirectUrl,
      "Deposit with Card",
      data.transactionId,
      state.selectedAccount!.accountNumber,
      maxPollingAttempts: state.maxPollingAttempts,
      pollingFrequencySeconds: state.pollingFrequencySeconds,
      depositFlowConfig: state.depositFlowConfig!,
    );
  }

  void _handleApplePayPayment(DepositResponseData data) {
    _depositAnalyticsEvent.depositSdkLoadStarted();
    log('Apple Pay Payment Initiated ✅');
    _applePayUtils.setOnPaymentCompleteCallback(({
      String? gatewayCode,
      required ApplePayStatus paymentStatus,
    }) {
      if (!isClosed) {
        add(
          _OnApplePayResult(
            paymentStatus: paymentStatus,
            gatewayCode: gatewayCode,
            applePay: _applePayUtils,
          ),
        );
      }
    });
    log('Apple Pay Payment session started ✅');
    _applePayUtils.initiateSession(data.paymentProviderId);
    _depositAnalyticsEvent.depositSdkLoaded();
  }

  FutureOr<void> _onApplePayResult(
    _OnApplePayResult event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    log(
      'paymentStatus: ${event.paymentStatus}, gatewayCode: ${event.gatewayCode ?? "null"}',
    );
    _depositAnalyticsEvent.depositSdkEvent(
      status: event.paymentStatus.name,
      gatewayCode: event.gatewayCode ?? "null",
    );
    final processState =
        event.paymentStatus == ApplePayStatus.success
            ? PaymentSuccessState()
            : PaymentRejectedState();

    emit(state.copyWith(processState: processState, isButtonLoading: false));
    event.applePay.dispose();
  }

  void _handleGooglePayPayment(
    DepositResponseData data,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    _depositAnalyticsEvent.depositSdkLoadStarted();
    log('Google Pay Payment Initiated ✅');
    _googlePayUtils.setOnPaymentCompleteCallback(({
      String? gatewayCode,
      required GooglePayStatus paymentStatus,
    }) {
      if (!isClosed) {
        add(
          _OnGooglePayResult(
            paymentStatus: paymentStatus,
            gatewayCode: gatewayCode,
            googlePay: _googlePayUtils,
          ),
        );
      }
    });
    log('Google Pay Payment session started ✅');
    _googlePayUtils.initiateSession(data.paymentProviderId);
    emit(state.copyWith(isButtonLoading: false));
    _depositAnalyticsEvent.depositSdkLoaded();
  }

  FutureOr<void> _onGooglePayResult(
    _OnGooglePayResult event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    log(
      'paymentStatus: ${event.paymentStatus}, gatewayCode: ${event.gatewayCode ?? "null"}',
    );
    _depositAnalyticsEvent.depositSdkEvent(
      status: event.paymentStatus.name,
      gatewayCode: event.gatewayCode ?? "null",
    );
    final processState =
        event.paymentStatus == GooglePayStatus.success
            ? PaymentSuccessState()
            : PaymentRejectedState();

    emit(state.copyWith(processState: processState, isButtonLoading: false));

    event.googlePay.dispose();
  }

  FutureOr<void> _handleBridgerPayPayment(
    DepositResponseData data,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(isButtonLoading: false));
    }
    _paymentNavigation.goToAdditionalPaymentScreen(
      data.redirectUrl,
      "Deposit with eWallets",
      data.transactionId,
      state.selectedAccount!.accountNumber,
      maxPollingAttempts: state.maxPollingAttempts,
      pollingFrequencySeconds: state.pollingFrequencySeconds,
      depositFlowConfig: state.depositFlowConfig!,
    );
  }

  FutureOr<void> _onPaymentSuccessContinuePressed(
    OnPaymentSuccessContinuePressed event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    _paymentNavigation.popUntilRoute(state.depositFlowConfig!.origin);
  }

  FutureOr<void> _onPaymentMethodChange(
    OnPaymentMethodChange event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    _paymentNavigation.goBackToDepositPaymentOptionsScreen();
  }

  FutureOr<void> _resetProcessState(
    ResetProcessStateEvent event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(state.copyWith(processState: LoadedState()));
  }

  FutureOr<void> _initArgs(
    _InitArgs event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(
      state.copyWith(
        maxPollingAttempts: event.maxPollingAttempts,
        pollingFrequencySeconds: event.pollingFrequencySeconds,
        depositFlowConfig: event.depositFlowConfig,
        leanAccountArgs: event.leanAccountArgs,
      ),
    );
  }

  void _handleLeanPayment(
    DepositResponseData data,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    emit(
      state.copyWith(
        isButtonLoading: false,
        processState: StartLeanPaymentState(
          transactionId: data.transactionId,
          paymentIntentId: data.paymentProviderId,
          leanMetaData: data.leanMetaData,
        ),
      ),
    );
    add(ResetProcessStateEvent());
  }

  void _handleLeanPaymentCallback(
    _OnLeanPaymentCallback event,
    Emitter<DepositAccountsAndAmountState> emit,
  ) {
    final status = event.response.status;
    if (status == "ERROR") {
      emit(state.copyWith(processState: PaymentRejectedState()));
      return;
    } else if (status == "CANCELLED") {
      return;
    }
    // for all other case we will show the payment status screen
    _paymentNavigation.goToPaymentStatusScreen(
      transactionId: event.transactionId,
      accountNumber: state.selectedAccount!.accountNumber,
      depositFlowConfig: state.depositFlowConfig!,
      maxPollingAttempts: state.maxPollingAttempts,
      pollingFrequencySeconds: state.pollingFrequencySeconds,
    );
    emit(state.copyWith(isButtonLoading: false));
  }
}
