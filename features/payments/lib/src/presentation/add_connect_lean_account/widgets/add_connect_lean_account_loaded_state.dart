import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/bank_lean_response/bank_lean_response.dart';
import 'package:payment/src/data/lean_config_response/lean_config_response.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/exceptions/lean_config_exception/lean_config_exception.dart';
import 'package:payment/src/presentation/add_connect_lean_account/bloc/add_connect_lean_account_bloc.dart';
import 'package:payment/src/presentation/add_connect_lean_account/widgets/lean_bank_account_item.dart';
import 'package:payment/src/utils/payment/lean_payment.dart';

class AddConnectLeanAccountLoadedState extends StatefulWidget {
  const AddConnectLeanAccountLoadedState({
    super.key,
    required this.paymentMethod,
    required this.paymentType,
  });
  final String paymentMethod;
  final PaymentType paymentType;

  @override
  State<AddConnectLeanAccountLoadedState> createState() =>
      _AddConnectLeanAccountLoadedStateState();
}

class _AddConnectLeanAccountLoadedStateState
    extends State<AddConnectLeanAccountLoadedState> {
  final TextEditingController searchTextController = TextEditingController();

  @override
  void dispose() {
    searchTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title:
            widget.paymentType == PaymentType.deposit
                ? localization.payments_lean_deposit_with_payment_method(
                  widget.paymentMethod,
                )
                : localization.payments_lean_withdraw_with_payment_method(
                  widget.paymentMethod,
                ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    top: DuploSpacing.spacing_4xl_32,
                    bottom: DuploSpacing.spacing_3xl_24,
                  ),
                  child: Column(
                    children: [
                      DuploText(
                        text: localization.payments_lean_select_your_bank,
                        style: textStyle.textXl,
                        color: theme.text.textPrimary,
                        fontWeight: DuploFontWeight.semiBold,
                      ),
                      SizedBox(height: DuploSpacing.spacing_md_8),
                      DuploText(
                        text:
                            localization.payments_lean_select_bank_description,
                        style: textStyle.textSm,
                        color: theme.text.textSecondary,
                        fontWeight: DuploFontWeight.regular,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                BlocBuilder<
                  AddConnectLeanAccountBloc,
                  AddConnectLeanAccountState
                >(
                  buildWhen:
                      (previous, current) =>
                          previous.filteredBankList?.length !=
                          current.filteredBankList?.length,
                  builder: (blocCtx, state) {
                    final bloc = blocCtx.read<AddConnectLeanAccountBloc>();
                    return Column(
                      children: [
                        DuploSearchInputField(
                          controller: searchTextController,
                          onChanged:
                              (value) => bloc.add(
                                AddConnectLeanAccountEvent.searchBankAccounts(
                                  value,
                                ),
                              ),
                        ),
                        SizedBox(height: DuploSpacing.spacing_lg_12),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (ctx, index) {
                            final item = state.filteredBankList
                                ?.elementAtOrNull(index);
                            return item == null
                                ? SizedBox.shrink()
                                : DuploTap(
                                  onTap:
                                      () => _onLeanBankAccountPressed(
                                        bloc,
                                        item,
                                        context,
                                      ),
                                  child: LeanBankAccountItem(item: item),
                                );
                          },
                          separatorBuilder: (ctx, index) {
                            return Container(
                              height: 1,
                              width: double.infinity,
                              margin: EdgeInsets.symmetric(
                                vertical: DuploSpacing.spacing_xs_4,
                              ),
                              color: theme.border.borderSecondary,
                            );
                          },
                          itemCount: state.filteredBankList?.length ?? 0,
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onLeanBankAccountPressed(
    AddConnectLeanAccountBloc bloc,
    LeanBankData item,
    BuildContext context,
  ) {
    final isWithdrawal = widget.paymentType == PaymentType.withdrawal;
    final depositAnalytics = diContainer<DepositAnalyticsEvent>();
    final withdrawAnalytics = diContainer<WithdrawAnalyticsEvent>();
    final theme = context.duploTheme;

    // Track bank selection based on payment type
    if (isWithdrawal) {
      withdrawAnalytics.withdrawLeanBankSelected(
        bankIdentifier: item.identifier,
        bankDisplayName: item.name,
      );
      withdrawAnalytics.withdrawLeanConnectStart(
        bankIdentifier: item.identifier,
      );
    } else {
      depositAnalytics.depositLeanBankSelected(
        bankIdentifier: item.identifier,
        bankDisplayName: item.name,
      );
      depositAnalytics.depositLeanConnectStart(bankIdentifier: item.identifier);
    }

    // Show bottom sheet immediately
    DuploSheet.showNonScrollableModalSheet<bool>(
          context: context,
          hasTopBarLayer: false,
          useSafeArea: true,
          backgroundColor: context.duploTheme.background.bgPrimary,
          hasTrailingIc: false,
          showDragHandle: false,
          content: (sheetContext) {
            return SizedBox(
              height: MediaQuery.sizeOf(context).height * .7,
              child: BlocBuilder<
                AddConnectLeanAccountBloc,
                AddConnectLeanAccountState
              >(
                bloc: bloc,
                buildWhen:
                    (previous, current) =>
                        previous.isLoadingLeanConfig !=
                            current.isLoadingLeanConfig ||
                        previous.leanConfigResponse !=
                            current.leanConfigResponse ||
                        previous.leanConfigError != current.leanConfigError,
                builder: (builderContext, state) {
                  if (state.isLoadingLeanConfig) {
                    return LoadingView();
                  }

                  if (state.leanConfigError
                      is LeanConfigAlreadyConnectedError) {
                    bloc.add(
                      AddConnectLeanAccountEvent.onBankAlreadyConnected(
                        bankIdentifier: item.identifier,
                      ),
                    );
                    Navigator.of(context).pop();
                    return LoadingView();
                  }

                  // Show error if config fetch failed
                  if (state.leanConfigError != null) {
                    return Container(
                      height: MediaQuery.sizeOf(context).height * .7,
                      color: theme.background.bgPrimary,
                      child: Center(
                        child: DuploText(
                          text: _getErrorMessage(
                            context,
                            state.leanConfigError!,
                          ),
                          style: context.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    );
                  }

                  // Show Lean connect widget when config is ready
                  final leanConfigResponse = state.leanConfigResponse;
                  if (leanConfigResponse == null) {
                    return LoadingView();
                  }

                  return LeanPayment.connect(
                    context: context,
                    appToken: leanConfigResponse.data.appToken,
                    customerId: leanConfigResponse.data.customerId,
                    accessToken: leanConfigResponse.data.customerToken,
                    bankIdentifier: item.identifier,
                    paymentDestinationId: _getDefaultDestinationId(
                      leanConfigResponse,
                    ),
                    customization: LeanPayment.returnLeanCustomization(
                      context: context,
                    ),
                  );
                },
              ),
            );
          },
        )
        .then((value) {
          if (value == true) {
            // Track success based on payment type
            if (isWithdrawal) {
              withdrawAnalytics.withdrawLeanConnectSuccess(
                bankIdentifier: item.identifier,
              );
            } else {
              depositAnalytics.depositLeanConnectSuccess(
                bankIdentifier: item.identifier,
              );
            }
            Navigator.pop(context, item.identifier);
          } else {
            // Track cancellation based on payment type
            if (isWithdrawal) {
              withdrawAnalytics.withdrawLeanConnectCancelled(
                bankIdentifier: item.identifier,
              );
            } else {
              depositAnalytics.depositLeanConnectCancelled(
                bankIdentifier: item.identifier,
              );
            }
          }
        })
        .catchError((Object e) {
          // Track error based on payment type
          if (isWithdrawal) {
            withdrawAnalytics.withdrawLeanConnectError(
              bankIdentifier: item.identifier,
              errorMessage: e.toString(),
            );
          } else {
            depositAnalytics.depositLeanConnectError(
              bankIdentifier: item.identifier,
              errorMessage: e.toString(),
            );
          }
        });

    // Dispatch event to fetch Lean config
    bloc.add(
      AddConnectLeanAccountEvent.getLeanConfig(bankIdentifier: item.identifier),
    );
  }

  String _getErrorMessage(BuildContext context, Exception exception) {
    if (exception is LeanConfigException) {
      return exception.message;
    }
    return EquitiLocalization.of(context).login_somethingWentWrong;
  }

  String? _getDefaultDestinationId(LeanConfigResponse leanConfigResponse) {
    final String? defaultDestinationId =
        leanConfigResponse.data.depositConfigList
            .where((config) => config.isDefault)
            .firstOrNull
            ?.destinationId;
    return defaultDestinationId;
  }
}
