abstract interface class PaymentFlags {
  bool isLeanPaymentEnabled();

  /// Waits for the Lean payment enabled flag to be available from Firebase Remote Config.
  ///
  /// This method ensures that the returned value comes from Firebase Remote Config,
  /// not from default values. It's useful for first app launch scenarios where you
  /// need to guarantee that remote values are loaded before proceeding.
  ///
  /// Parameters:
  /// - [timeout]: Optional timeout duration. If specified and exceeded, throws [RemoteValueTimeoutException]
  /// - [waitForRealtime]: If true (default), waits for real-time updates if initial fetch doesn't return remote values.
  ///
  /// Returns the value from remote config, or the default value if not available.
  Future<bool> isLeanPaymentEnabledWhenRemote({
    Duration? timeout,
    bool waitForRealtime = true,
  });
}
