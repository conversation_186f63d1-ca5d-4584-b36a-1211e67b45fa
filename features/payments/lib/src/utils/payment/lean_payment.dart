import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:lean_sdk_flutter/lean_sdk_flutter.dart';
import 'package:intl/intl.dart' as intl;
import 'package:payment/src/di/di_container.dart';

class LeanPayment {
  const LeanPayment._();

  /// Gets the isSandbox configuration value from DI container.
  /// Returns true for sandbox mode, false for production mode.
  static bool get _isSandbox =>
      diContainer<bool>(instanceName: 'isLeanInSandbox');

  static Map<String, String> returnLeanCustomization({
    required BuildContext context,
  }) {
    final theme = context.duploTheme;

    return {
      'overlay_color': theme.background.bgPrimary.toHexLean(),
      'theme_color': theme.button.buttonPrimaryBg.toHexLean(),
      'button_text_color': theme.button.buttonPrimaryFg.toHexLean(),
      'link_color': theme.button.buttonPrimaryFg.toHexLean(),
      'dialog_mode': 'uncontained',
    };
  }

  /// Creates a Lean connect widget for bank account connection.
  ///
  /// This widget initiates the Lean SDK flow to connect a user's bank account
  /// and grant permissions for accessing bank data.
  ///
  /// Parameters:
  /// - [context]: The build context for navigation
  /// - [appToken]: Lean app authentication token
  /// - [customerId]: The customer ID in Lean system
  /// - [accessToken]: User access token for Lean API
  /// - [bankIdentifier]: The identifier of the bank to connect to
  /// - [permissions]: Optional list of permissions to request (defaults to all)
  /// - [customization]: Optional map of customization parameters for Lean UI
  ///
  /// Returns a Widget that displays the Lean bank connection flow.
  /// Automatically pops the navigation with success/failure status.
  static Widget connect({
    required BuildContext context,
    required String appToken,
    required String customerId,
    required String accessToken,
    required String bankIdentifier,
    List<LeanPermissions>? permissions,
    String? paymentDestinationId,
    Map<String, String> customization = const {},
  }) {
    final perms =
        permissions ??
        const [
          LeanPermissions.identity,
          LeanPermissions.transactions,
          LeanPermissions.balance,
          LeanPermissions.accounts,
          LeanPermissions.payments,
        ];
    return Lean.connect(
      permissions: perms,
      customization: customization,
      appToken: appToken,
      customerId: customerId,
      language: _getLeanLanguage(context: context),
      accessToken: accessToken,
      bankIdentifier: bankIdentifier,
      paymentDestinationId: paymentDestinationId,
      isSandbox: _isSandbox,
      callback: (response) {
        log('Lean connect response: ${response.status}');
        Navigator.pop(context, response.status == 'SUCCESS');
      },
    );
  }

  /// Creates a Lean payment widget for processing payments.
  ///
  /// This widget initiates the Lean SDK flow to process a payment using
  /// a payment intent.
  ///
  /// Parameters:
  /// - [context]: The build context for navigation
  /// - [paymentIntentId]: The ID of the payment intent to process
  /// - [appToken]: Lean app authentication token
  /// - [accessToken]: User access token for Lean API
  /// - [accountId]: Optional account ID to use for payment
  /// - [showLogs]: Whether to show debug logs (default: true)
  /// - [onCancelled]: Optional callback when user cancels the flow
  /// - [onCallback]: Optional callback with status when flow completes
  /// - [customization]: Optional map of customization parameters for Lean UI
  ///
  /// Returns a Widget that displays the Lean payment flow.
  /// Automatically pops the navigation with success/failure status.
  static Widget pay({
    required BuildContext context,
    required String paymentIntentId,
    required String appToken,
    required String accessToken,
    String? accountId,
    bool showLogs = true,
    VoidCallback? onCancelled,
    void Function(LeanResponse response)? onCallback,
    Map<String, String> customization = const {},
  }) {
    return Lean.pay(
      paymentIntentId: paymentIntentId,
      showLogs: showLogs,
      accountId: accountId,
      appToken: appToken,
      accessToken: accessToken,
      language: _getLeanLanguage(context: context),
      isSandbox: _isSandbox,
      actionCancelled: () {
        log('Lean pay action cancelled by user');
        Navigator.pop(context, false);
        onCancelled?.call();
      },
      callback: (response) {
        final status = response.status;
        Navigator.pop(context, status == 'SUCCESS');
        onCallback?.call(response);
      },
      customization: customization,
    );
  }

  /// Creates a Lean beneficiary widget for payment source setup.
  ///
  /// This widget initiates the Lean SDK flow to create a beneficiary account
  /// that can be used for future payments.
  ///
  /// Parameters:
  /// - [context]: The build context for navigation
  /// - [paymentSourceId]: The ID of the payment source to create beneficiary for
  /// - [appToken]: Lean app authentication token
  /// - [accessToken]: User access token for Lean API
  /// - [customerId]: The customer ID in Lean system
  /// - [showLogs]: Whether to show debug logs (default: true)
  /// - [onCancelled]: Optional callback when user cancels the flow
  /// - [onCallback]: Optional callback with status when flow completes
  /// - [customization]: Optional map of customization parameters for Lean UI
  ///
  /// Returns a Widget that displays the Lean beneficiary creation flow.
  /// Automatically pops the navigation with success/failure status.
  static Widget createBeneficiary({
    required BuildContext context,
    required String paymentDestinationId,
    String? paymentSourceId,
    required String appToken,
    required String accessToken,
    required String customerId,
    bool showLogs = true,
    VoidCallback? onCancelled,
    void Function(String status)? onCallback,
    Map<String, String> customization = const {},
  }) {
    return Lean.createBeneficiary(
      showLogs: showLogs,
      paymentDestinationId: paymentDestinationId,
      paymentSourceId: paymentSourceId,
      customerId: customerId,
      appToken: appToken,
      language: _getLeanLanguage(context: context),
      accessToken: accessToken,
      isSandbox: _isSandbox,
      actionCancelled: () {
        log('Lean create beneficiary action cancelled by user');
        Navigator.pop(context, false);
        onCancelled?.call();
      },
      callback: (response) {
        final status = response.status;
        log('Lean create beneficiary response: $status');
        Navigator.pop(context, status == 'SUCCESS');
        onCallback?.call(status);
      },
      customization: customization,
    );
  }

  /// Determines if text direction should be right-to-left based on language or context.
  ///
  /// If [language] is provided, uses Bidi algorithm to check if it's an RTL language.
  /// Otherwise, falls back to the current widget's directionality from [context].
  static bool _isTextDirectionRTL({
    String? language,
    required BuildContext context,
  }) {
    return language != null
        ? intl.Bidi.isRtlLanguage(language)
        : Directionality.of(context) == TextDirection.rtl;
  }

  static LeanLanguage _getLeanLanguage({
    String? language,
    required BuildContext context,
  }) {
    final bool isRTL = _isTextDirectionRTL(
      context: context,
      language: language,
    );
    return isRTL ? LeanLanguage.ar : LeanLanguage.en;
  }
}
