// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lean_customer_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LeanCustomerResponse {

 bool get success;@JsonKey(name: 'data') List<PaymentSource> get paymentSourceList;
/// Create a copy of LeanCustomerResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeanCustomerResponseCopyWith<LeanCustomerResponse> get copyWith => _$LeanCustomerResponseCopyWithImpl<LeanCustomerResponse>(this as LeanCustomerResponse, _$identity);

  /// Serializes this LeanCustomerResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeanCustomerResponse&&(identical(other.success, success) || other.success == success)&&const DeepCollectionEquality().equals(other.paymentSourceList, paymentSourceList));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,const DeepCollectionEquality().hash(paymentSourceList));

@override
String toString() {
  return 'LeanCustomerResponse(success: $success, paymentSourceList: $paymentSourceList)';
}


}

/// @nodoc
abstract mixin class $LeanCustomerResponseCopyWith<$Res>  {
  factory $LeanCustomerResponseCopyWith(LeanCustomerResponse value, $Res Function(LeanCustomerResponse) _then) = _$LeanCustomerResponseCopyWithImpl;
@useResult
$Res call({
 bool success,@JsonKey(name: 'data') List<PaymentSource> paymentSourceList
});




}
/// @nodoc
class _$LeanCustomerResponseCopyWithImpl<$Res>
    implements $LeanCustomerResponseCopyWith<$Res> {
  _$LeanCustomerResponseCopyWithImpl(this._self, this._then);

  final LeanCustomerResponse _self;
  final $Res Function(LeanCustomerResponse) _then;

/// Create a copy of LeanCustomerResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? paymentSourceList = null,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,paymentSourceList: null == paymentSourceList ? _self.paymentSourceList : paymentSourceList // ignore: cast_nullable_to_non_nullable
as List<PaymentSource>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LeanCustomerResponse implements LeanCustomerResponse {
  const _LeanCustomerResponse({required this.success, @JsonKey(name: 'data') final  List<PaymentSource> paymentSourceList = const []}): _paymentSourceList = paymentSourceList;
  factory _LeanCustomerResponse.fromJson(Map<String, dynamic> json) => _$LeanCustomerResponseFromJson(json);

@override final  bool success;
 final  List<PaymentSource> _paymentSourceList;
@override@JsonKey(name: 'data') List<PaymentSource> get paymentSourceList {
  if (_paymentSourceList is EqualUnmodifiableListView) return _paymentSourceList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_paymentSourceList);
}


/// Create a copy of LeanCustomerResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeanCustomerResponseCopyWith<_LeanCustomerResponse> get copyWith => __$LeanCustomerResponseCopyWithImpl<_LeanCustomerResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeanCustomerResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeanCustomerResponse&&(identical(other.success, success) || other.success == success)&&const DeepCollectionEquality().equals(other._paymentSourceList, _paymentSourceList));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,const DeepCollectionEquality().hash(_paymentSourceList));

@override
String toString() {
  return 'LeanCustomerResponse(success: $success, paymentSourceList: $paymentSourceList)';
}


}

/// @nodoc
abstract mixin class _$LeanCustomerResponseCopyWith<$Res> implements $LeanCustomerResponseCopyWith<$Res> {
  factory _$LeanCustomerResponseCopyWith(_LeanCustomerResponse value, $Res Function(_LeanCustomerResponse) _then) = __$LeanCustomerResponseCopyWithImpl;
@override @useResult
$Res call({
 bool success,@JsonKey(name: 'data') List<PaymentSource> paymentSourceList
});




}
/// @nodoc
class __$LeanCustomerResponseCopyWithImpl<$Res>
    implements _$LeanCustomerResponseCopyWith<$Res> {
  __$LeanCustomerResponseCopyWithImpl(this._self, this._then);

  final _LeanCustomerResponse _self;
  final $Res Function(_LeanCustomerResponse) _then;

/// Create a copy of LeanCustomerResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? paymentSourceList = null,}) {
  return _then(_LeanCustomerResponse(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,paymentSourceList: null == paymentSourceList ? _self._paymentSourceList : paymentSourceList // ignore: cast_nullable_to_non_nullable
as List<PaymentSource>,
  ));
}


}


/// @nodoc
mixin _$PaymentSource {

 String get id;@JsonKey(name: 'customer_id') String get customerId;@JsonKey(name: 'app_id') String get appId;@JsonKey(name: 'bank_identifier') String get bankIdentifier;@JsonKey(name: 'status', unknownEnumValue: LeanBankStatus.UNKNOWN) LeanBankStatus get status;@JsonKey(name: 'accounts') List<LeanAccount> get accountList;@JsonKey(name: 'beneficiaries') List<Beneficiary> get beneficiaryList;
/// Create a copy of PaymentSource
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaymentSourceCopyWith<PaymentSource> get copyWith => _$PaymentSourceCopyWithImpl<PaymentSource>(this as PaymentSource, _$identity);

  /// Serializes this PaymentSource to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentSource&&(identical(other.id, id) || other.id == id)&&(identical(other.customerId, customerId) || other.customerId == customerId)&&(identical(other.appId, appId) || other.appId == appId)&&(identical(other.bankIdentifier, bankIdentifier) || other.bankIdentifier == bankIdentifier)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.accountList, accountList)&&const DeepCollectionEquality().equals(other.beneficiaryList, beneficiaryList));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,customerId,appId,bankIdentifier,status,const DeepCollectionEquality().hash(accountList),const DeepCollectionEquality().hash(beneficiaryList));

@override
String toString() {
  return 'PaymentSource(id: $id, customerId: $customerId, appId: $appId, bankIdentifier: $bankIdentifier, status: $status, accountList: $accountList, beneficiaryList: $beneficiaryList)';
}


}

/// @nodoc
abstract mixin class $PaymentSourceCopyWith<$Res>  {
  factory $PaymentSourceCopyWith(PaymentSource value, $Res Function(PaymentSource) _then) = _$PaymentSourceCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'customer_id') String customerId,@JsonKey(name: 'app_id') String appId,@JsonKey(name: 'bank_identifier') String bankIdentifier,@JsonKey(name: 'status', unknownEnumValue: LeanBankStatus.UNKNOWN) LeanBankStatus status,@JsonKey(name: 'accounts') List<LeanAccount> accountList,@JsonKey(name: 'beneficiaries') List<Beneficiary> beneficiaryList
});




}
/// @nodoc
class _$PaymentSourceCopyWithImpl<$Res>
    implements $PaymentSourceCopyWith<$Res> {
  _$PaymentSourceCopyWithImpl(this._self, this._then);

  final PaymentSource _self;
  final $Res Function(PaymentSource) _then;

/// Create a copy of PaymentSource
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? customerId = null,Object? appId = null,Object? bankIdentifier = null,Object? status = null,Object? accountList = null,Object? beneficiaryList = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,customerId: null == customerId ? _self.customerId : customerId // ignore: cast_nullable_to_non_nullable
as String,appId: null == appId ? _self.appId : appId // ignore: cast_nullable_to_non_nullable
as String,bankIdentifier: null == bankIdentifier ? _self.bankIdentifier : bankIdentifier // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as LeanBankStatus,accountList: null == accountList ? _self.accountList : accountList // ignore: cast_nullable_to_non_nullable
as List<LeanAccount>,beneficiaryList: null == beneficiaryList ? _self.beneficiaryList : beneficiaryList // ignore: cast_nullable_to_non_nullable
as List<Beneficiary>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PaymentSource implements PaymentSource {
  const _PaymentSource({required this.id, @JsonKey(name: 'customer_id') required this.customerId, @JsonKey(name: 'app_id') required this.appId, @JsonKey(name: 'bank_identifier') required this.bankIdentifier, @JsonKey(name: 'status', unknownEnumValue: LeanBankStatus.UNKNOWN) required this.status, @JsonKey(name: 'accounts') final  List<LeanAccount> accountList = const [], @JsonKey(name: 'beneficiaries') final  List<Beneficiary> beneficiaryList = const []}): _accountList = accountList,_beneficiaryList = beneficiaryList;
  factory _PaymentSource.fromJson(Map<String, dynamic> json) => _$PaymentSourceFromJson(json);

@override final  String id;
@override@JsonKey(name: 'customer_id') final  String customerId;
@override@JsonKey(name: 'app_id') final  String appId;
@override@JsonKey(name: 'bank_identifier') final  String bankIdentifier;
@override@JsonKey(name: 'status', unknownEnumValue: LeanBankStatus.UNKNOWN) final  LeanBankStatus status;
 final  List<LeanAccount> _accountList;
@override@JsonKey(name: 'accounts') List<LeanAccount> get accountList {
  if (_accountList is EqualUnmodifiableListView) return _accountList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accountList);
}

 final  List<Beneficiary> _beneficiaryList;
@override@JsonKey(name: 'beneficiaries') List<Beneficiary> get beneficiaryList {
  if (_beneficiaryList is EqualUnmodifiableListView) return _beneficiaryList;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_beneficiaryList);
}


/// Create a copy of PaymentSource
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaymentSourceCopyWith<_PaymentSource> get copyWith => __$PaymentSourceCopyWithImpl<_PaymentSource>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaymentSourceToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaymentSource&&(identical(other.id, id) || other.id == id)&&(identical(other.customerId, customerId) || other.customerId == customerId)&&(identical(other.appId, appId) || other.appId == appId)&&(identical(other.bankIdentifier, bankIdentifier) || other.bankIdentifier == bankIdentifier)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._accountList, _accountList)&&const DeepCollectionEquality().equals(other._beneficiaryList, _beneficiaryList));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,customerId,appId,bankIdentifier,status,const DeepCollectionEquality().hash(_accountList),const DeepCollectionEquality().hash(_beneficiaryList));

@override
String toString() {
  return 'PaymentSource(id: $id, customerId: $customerId, appId: $appId, bankIdentifier: $bankIdentifier, status: $status, accountList: $accountList, beneficiaryList: $beneficiaryList)';
}


}

/// @nodoc
abstract mixin class _$PaymentSourceCopyWith<$Res> implements $PaymentSourceCopyWith<$Res> {
  factory _$PaymentSourceCopyWith(_PaymentSource value, $Res Function(_PaymentSource) _then) = __$PaymentSourceCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'customer_id') String customerId,@JsonKey(name: 'app_id') String appId,@JsonKey(name: 'bank_identifier') String bankIdentifier,@JsonKey(name: 'status', unknownEnumValue: LeanBankStatus.UNKNOWN) LeanBankStatus status,@JsonKey(name: 'accounts') List<LeanAccount> accountList,@JsonKey(name: 'beneficiaries') List<Beneficiary> beneficiaryList
});




}
/// @nodoc
class __$PaymentSourceCopyWithImpl<$Res>
    implements _$PaymentSourceCopyWith<$Res> {
  __$PaymentSourceCopyWithImpl(this._self, this._then);

  final _PaymentSource _self;
  final $Res Function(_PaymentSource) _then;

/// Create a copy of PaymentSource
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? customerId = null,Object? appId = null,Object? bankIdentifier = null,Object? status = null,Object? accountList = null,Object? beneficiaryList = null,}) {
  return _then(_PaymentSource(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,customerId: null == customerId ? _self.customerId : customerId // ignore: cast_nullable_to_non_nullable
as String,appId: null == appId ? _self.appId : appId // ignore: cast_nullable_to_non_nullable
as String,bankIdentifier: null == bankIdentifier ? _self.bankIdentifier : bankIdentifier // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as LeanBankStatus,accountList: null == accountList ? _self._accountList : accountList // ignore: cast_nullable_to_non_nullable
as List<LeanAccount>,beneficiaryList: null == beneficiaryList ? _self._beneficiaryList : beneficiaryList // ignore: cast_nullable_to_non_nullable
as List<Beneficiary>,
  ));
}


}


/// @nodoc
mixin _$LeanAccount {

 String get id;@JsonKey(name: 'account_id') String get accountId;@JsonKey(name: 'account_name') String get accountName;@JsonKey(name: 'account_number') String get accountNumber;@JsonKey(name: 'currency') String get currency;@JsonKey(name: 'iban') String get iban;@JsonKey(name: 'status', unknownEnumValue: LeanAccountStatus.UNKNOWN) LeanAccountStatus get status;@JsonKey(name: 'cool_off_expiry') DateTime? get coolOffExpiry;
/// Create a copy of LeanAccount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LeanAccountCopyWith<LeanAccount> get copyWith => _$LeanAccountCopyWithImpl<LeanAccount>(this as LeanAccount, _$identity);

  /// Serializes this LeanAccount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LeanAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.accountName, accountName) || other.accountName == accountName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.status, status) || other.status == status)&&(identical(other.coolOffExpiry, coolOffExpiry) || other.coolOffExpiry == coolOffExpiry));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,accountId,accountName,accountNumber,currency,iban,status,coolOffExpiry);

@override
String toString() {
  return 'LeanAccount(id: $id, accountId: $accountId, accountName: $accountName, accountNumber: $accountNumber, currency: $currency, iban: $iban, status: $status, coolOffExpiry: $coolOffExpiry)';
}


}

/// @nodoc
abstract mixin class $LeanAccountCopyWith<$Res>  {
  factory $LeanAccountCopyWith(LeanAccount value, $Res Function(LeanAccount) _then) = _$LeanAccountCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'account_id') String accountId,@JsonKey(name: 'account_name') String accountName,@JsonKey(name: 'account_number') String accountNumber,@JsonKey(name: 'currency') String currency,@JsonKey(name: 'iban') String iban,@JsonKey(name: 'status', unknownEnumValue: LeanAccountStatus.UNKNOWN) LeanAccountStatus status,@JsonKey(name: 'cool_off_expiry') DateTime? coolOffExpiry
});




}
/// @nodoc
class _$LeanAccountCopyWithImpl<$Res>
    implements $LeanAccountCopyWith<$Res> {
  _$LeanAccountCopyWithImpl(this._self, this._then);

  final LeanAccount _self;
  final $Res Function(LeanAccount) _then;

/// Create a copy of LeanAccount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? accountId = null,Object? accountName = null,Object? accountNumber = null,Object? currency = null,Object? iban = null,Object? status = null,Object? coolOffExpiry = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,accountName: null == accountName ? _self.accountName : accountName // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as LeanAccountStatus,coolOffExpiry: freezed == coolOffExpiry ? _self.coolOffExpiry : coolOffExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LeanAccount implements LeanAccount {
  const _LeanAccount({required this.id, @JsonKey(name: 'account_id') required this.accountId, @JsonKey(name: 'account_name') required this.accountName, @JsonKey(name: 'account_number') required this.accountNumber, @JsonKey(name: 'currency') required this.currency, @JsonKey(name: 'iban') required this.iban, @JsonKey(name: 'status', unknownEnumValue: LeanAccountStatus.UNKNOWN) required this.status, @JsonKey(name: 'cool_off_expiry') this.coolOffExpiry});
  factory _LeanAccount.fromJson(Map<String, dynamic> json) => _$LeanAccountFromJson(json);

@override final  String id;
@override@JsonKey(name: 'account_id') final  String accountId;
@override@JsonKey(name: 'account_name') final  String accountName;
@override@JsonKey(name: 'account_number') final  String accountNumber;
@override@JsonKey(name: 'currency') final  String currency;
@override@JsonKey(name: 'iban') final  String iban;
@override@JsonKey(name: 'status', unknownEnumValue: LeanAccountStatus.UNKNOWN) final  LeanAccountStatus status;
@override@JsonKey(name: 'cool_off_expiry') final  DateTime? coolOffExpiry;

/// Create a copy of LeanAccount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LeanAccountCopyWith<_LeanAccount> get copyWith => __$LeanAccountCopyWithImpl<_LeanAccount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LeanAccountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LeanAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.accountName, accountName) || other.accountName == accountName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.status, status) || other.status == status)&&(identical(other.coolOffExpiry, coolOffExpiry) || other.coolOffExpiry == coolOffExpiry));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,accountId,accountName,accountNumber,currency,iban,status,coolOffExpiry);

@override
String toString() {
  return 'LeanAccount(id: $id, accountId: $accountId, accountName: $accountName, accountNumber: $accountNumber, currency: $currency, iban: $iban, status: $status, coolOffExpiry: $coolOffExpiry)';
}


}

/// @nodoc
abstract mixin class _$LeanAccountCopyWith<$Res> implements $LeanAccountCopyWith<$Res> {
  factory _$LeanAccountCopyWith(_LeanAccount value, $Res Function(_LeanAccount) _then) = __$LeanAccountCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'account_id') String accountId,@JsonKey(name: 'account_name') String accountName,@JsonKey(name: 'account_number') String accountNumber,@JsonKey(name: 'currency') String currency,@JsonKey(name: 'iban') String iban,@JsonKey(name: 'status', unknownEnumValue: LeanAccountStatus.UNKNOWN) LeanAccountStatus status,@JsonKey(name: 'cool_off_expiry') DateTime? coolOffExpiry
});




}
/// @nodoc
class __$LeanAccountCopyWithImpl<$Res>
    implements _$LeanAccountCopyWith<$Res> {
  __$LeanAccountCopyWithImpl(this._self, this._then);

  final _LeanAccount _self;
  final $Res Function(_LeanAccount) _then;

/// Create a copy of LeanAccount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? accountId = null,Object? accountName = null,Object? accountNumber = null,Object? currency = null,Object? iban = null,Object? status = null,Object? coolOffExpiry = freezed,}) {
  return _then(_LeanAccount(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,accountName: null == accountName ? _self.accountName : accountName // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as LeanAccountStatus,coolOffExpiry: freezed == coolOffExpiry ? _self.coolOffExpiry : coolOffExpiry // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$Beneficiary {

 String get id;@JsonKey(name: 'payment_destination_id') String get paymentDestinationId;@JsonKey(name: 'status') String get status;@JsonKey(name: 'beneficiary_cool_off_expiry') String? get beneficiaryCoolOffExpiry;@JsonKey(name: 'bank_identifier') String get bankIdentifier;
/// Create a copy of Beneficiary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BeneficiaryCopyWith<Beneficiary> get copyWith => _$BeneficiaryCopyWithImpl<Beneficiary>(this as Beneficiary, _$identity);

  /// Serializes this Beneficiary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Beneficiary&&(identical(other.id, id) || other.id == id)&&(identical(other.paymentDestinationId, paymentDestinationId) || other.paymentDestinationId == paymentDestinationId)&&(identical(other.status, status) || other.status == status)&&(identical(other.beneficiaryCoolOffExpiry, beneficiaryCoolOffExpiry) || other.beneficiaryCoolOffExpiry == beneficiaryCoolOffExpiry)&&(identical(other.bankIdentifier, bankIdentifier) || other.bankIdentifier == bankIdentifier));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,paymentDestinationId,status,beneficiaryCoolOffExpiry,bankIdentifier);

@override
String toString() {
  return 'Beneficiary(id: $id, paymentDestinationId: $paymentDestinationId, status: $status, beneficiaryCoolOffExpiry: $beneficiaryCoolOffExpiry, bankIdentifier: $bankIdentifier)';
}


}

/// @nodoc
abstract mixin class $BeneficiaryCopyWith<$Res>  {
  factory $BeneficiaryCopyWith(Beneficiary value, $Res Function(Beneficiary) _then) = _$BeneficiaryCopyWithImpl;
@useResult
$Res call({
 String id,@JsonKey(name: 'payment_destination_id') String paymentDestinationId,@JsonKey(name: 'status') String status,@JsonKey(name: 'beneficiary_cool_off_expiry') String? beneficiaryCoolOffExpiry,@JsonKey(name: 'bank_identifier') String bankIdentifier
});




}
/// @nodoc
class _$BeneficiaryCopyWithImpl<$Res>
    implements $BeneficiaryCopyWith<$Res> {
  _$BeneficiaryCopyWithImpl(this._self, this._then);

  final Beneficiary _self;
  final $Res Function(Beneficiary) _then;

/// Create a copy of Beneficiary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? paymentDestinationId = null,Object? status = null,Object? beneficiaryCoolOffExpiry = freezed,Object? bankIdentifier = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,paymentDestinationId: null == paymentDestinationId ? _self.paymentDestinationId : paymentDestinationId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,beneficiaryCoolOffExpiry: freezed == beneficiaryCoolOffExpiry ? _self.beneficiaryCoolOffExpiry : beneficiaryCoolOffExpiry // ignore: cast_nullable_to_non_nullable
as String?,bankIdentifier: null == bankIdentifier ? _self.bankIdentifier : bankIdentifier // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Beneficiary implements Beneficiary {
  const _Beneficiary({required this.id, @JsonKey(name: 'payment_destination_id') required this.paymentDestinationId, @JsonKey(name: 'status') required this.status, @JsonKey(name: 'beneficiary_cool_off_expiry') this.beneficiaryCoolOffExpiry, @JsonKey(name: 'bank_identifier') required this.bankIdentifier});
  factory _Beneficiary.fromJson(Map<String, dynamic> json) => _$BeneficiaryFromJson(json);

@override final  String id;
@override@JsonKey(name: 'payment_destination_id') final  String paymentDestinationId;
@override@JsonKey(name: 'status') final  String status;
@override@JsonKey(name: 'beneficiary_cool_off_expiry') final  String? beneficiaryCoolOffExpiry;
@override@JsonKey(name: 'bank_identifier') final  String bankIdentifier;

/// Create a copy of Beneficiary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BeneficiaryCopyWith<_Beneficiary> get copyWith => __$BeneficiaryCopyWithImpl<_Beneficiary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BeneficiaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Beneficiary&&(identical(other.id, id) || other.id == id)&&(identical(other.paymentDestinationId, paymentDestinationId) || other.paymentDestinationId == paymentDestinationId)&&(identical(other.status, status) || other.status == status)&&(identical(other.beneficiaryCoolOffExpiry, beneficiaryCoolOffExpiry) || other.beneficiaryCoolOffExpiry == beneficiaryCoolOffExpiry)&&(identical(other.bankIdentifier, bankIdentifier) || other.bankIdentifier == bankIdentifier));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,paymentDestinationId,status,beneficiaryCoolOffExpiry,bankIdentifier);

@override
String toString() {
  return 'Beneficiary(id: $id, paymentDestinationId: $paymentDestinationId, status: $status, beneficiaryCoolOffExpiry: $beneficiaryCoolOffExpiry, bankIdentifier: $bankIdentifier)';
}


}

/// @nodoc
abstract mixin class _$BeneficiaryCopyWith<$Res> implements $BeneficiaryCopyWith<$Res> {
  factory _$BeneficiaryCopyWith(_Beneficiary value, $Res Function(_Beneficiary) _then) = __$BeneficiaryCopyWithImpl;
@override @useResult
$Res call({
 String id,@JsonKey(name: 'payment_destination_id') String paymentDestinationId,@JsonKey(name: 'status') String status,@JsonKey(name: 'beneficiary_cool_off_expiry') String? beneficiaryCoolOffExpiry,@JsonKey(name: 'bank_identifier') String bankIdentifier
});




}
/// @nodoc
class __$BeneficiaryCopyWithImpl<$Res>
    implements _$BeneficiaryCopyWith<$Res> {
  __$BeneficiaryCopyWithImpl(this._self, this._then);

  final _Beneficiary _self;
  final $Res Function(_Beneficiary) _then;

/// Create a copy of Beneficiary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? paymentDestinationId = null,Object? status = null,Object? beneficiaryCoolOffExpiry = freezed,Object? bankIdentifier = null,}) {
  return _then(_Beneficiary(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,paymentDestinationId: null == paymentDestinationId ? _self.paymentDestinationId : paymentDestinationId // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,beneficiaryCoolOffExpiry: freezed == beneficiaryCoolOffExpiry ? _self.beneficiaryCoolOffExpiry : beneficiaryCoolOffExpiry // ignore: cast_nullable_to_non_nullable
as String?,bankIdentifier: null == bankIdentifier ? _self.bankIdentifier : bankIdentifier // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
