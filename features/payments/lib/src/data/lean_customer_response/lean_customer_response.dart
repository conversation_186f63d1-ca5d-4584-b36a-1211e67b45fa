import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/domain/data/lean_bank_status.dart';

part 'lean_customer_response.freezed.dart';
part 'lean_customer_response.g.dart';

@freezed
sealed class LeanCustomerResponse with _$LeanCustomerResponse {
  const factory LeanCustomerResponse({
    required bool success,
    @JsonKey(name: 'data') @Default([]) List<PaymentSource> paymentSourceList,
  }) = _LeanCustomerResponse;

  factory LeanCustomerResponse.fromJson(Map<String, dynamic> json) =>
      _$LeanCustomerResponseFromJson(json);
}

@freezed
sealed class PaymentSource with _$PaymentSource {
  const factory PaymentSource({
    required String id,
    @JsonKey(name: 'customer_id') required String customerId,
    @JsonKey(name: 'app_id') required String appId,
    @JsonKey(name: 'bank_identifier') required String bankIdentifier,
    @JsonKey(name: 'status', unknownEnumValue: LeanBankStatus.UNKNOWN)
    required LeanBankStatus status,
    @JsonKey(name: 'accounts') @Default([]) List<LeanAccount> accountList,
    @JsonKey(name: 'beneficiaries')
    @Default([])
    List<Beneficiary> beneficiaryList,
  }) = _PaymentSource;
  factory PaymentSource.fromJson(Map<String, dynamic> json) =>
      _$PaymentSourceFromJson(json);
}

@freezed
sealed class LeanAccount with _$LeanAccount {
  const factory LeanAccount({
    required String id,
    @JsonKey(name: 'account_id') required String accountId,
    @JsonKey(name: 'account_name') required String accountName,
    @JsonKey(name: 'account_number') required String accountNumber,
    @JsonKey(name: 'currency') required String currency,
    @JsonKey(name: 'iban') required String iban,
    @JsonKey(name: 'status', unknownEnumValue: LeanAccountStatus.UNKNOWN)
    required LeanAccountStatus status,
    @JsonKey(name: 'cool_off_expiry') DateTime? coolOffExpiry,
  }) = _LeanAccount;
  factory LeanAccount.fromJson(Map<String, dynamic> json) =>
      _$LeanAccountFromJson(json);
}

@freezed
sealed class Beneficiary with _$Beneficiary {
  const factory Beneficiary({
    required String id,
    @JsonKey(name: 'payment_destination_id')
    required String paymentDestinationId,
    @JsonKey(name: 'status') required String status,
    @JsonKey(name: 'beneficiary_cool_off_expiry')
    String? beneficiaryCoolOffExpiry,
    @JsonKey(name: 'bank_identifier') required String bankIdentifier,
  }) = _Beneficiary;
  factory Beneficiary.fromJson(Map<String, dynamic> json) =>
      _$BeneficiaryFromJson(json);
}
