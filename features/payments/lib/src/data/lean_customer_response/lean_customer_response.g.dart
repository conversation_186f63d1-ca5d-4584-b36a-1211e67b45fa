// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lean_customer_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LeanCustomerResponse _$LeanCustomerResponseFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('_LeanCustomerResponse', json, ($checkedConvert) {
  final val = _LeanCustomerResponse(
    success: $checkedConvert('success', (v) => v as bool),
    paymentSourceList: $checkedConvert(
      'data',
      (v) =>
          (v as List<dynamic>?)
              ?.map((e) => PaymentSource.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    ),
  );
  return val;
}, fieldKeyMap: const {'paymentSourceList': 'data'});

Map<String, dynamic> _$LeanCustomerResponseToJson(
  _LeanCustomerResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  'data': instance.paymentSourceList.map((e) => e.toJson()).toList(),
};

_PaymentSource _$PaymentSourceFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  '_PaymentSource',
  json,
  ($checkedConvert) {
    final val = _PaymentSource(
      id: $checkedConvert('id', (v) => v as String),
      customerId: $checkedConvert('customer_id', (v) => v as String),
      appId: $checkedConvert('app_id', (v) => v as String),
      bankIdentifier: $checkedConvert('bank_identifier', (v) => v as String),
      status: $checkedConvert(
        'status',
        (v) => $enumDecode(
          _$LeanBankStatusEnumMap,
          v,
          unknownValue: LeanBankStatus.UNKNOWN,
        ),
      ),
      accountList: $checkedConvert(
        'accounts',
        (v) =>
            (v as List<dynamic>?)
                ?.map((e) => LeanAccount.fromJson(e as Map<String, dynamic>))
                .toList() ??
            const [],
      ),
      beneficiaryList: $checkedConvert(
        'beneficiaries',
        (v) =>
            (v as List<dynamic>?)
                ?.map((e) => Beneficiary.fromJson(e as Map<String, dynamic>))
                .toList() ??
            const [],
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'customerId': 'customer_id',
    'appId': 'app_id',
    'bankIdentifier': 'bank_identifier',
    'accountList': 'accounts',
    'beneficiaryList': 'beneficiaries',
  },
);

Map<String, dynamic> _$PaymentSourceToJson(_PaymentSource instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customer_id': instance.customerId,
      'app_id': instance.appId,
      'bank_identifier': instance.bankIdentifier,
      'status': _$LeanBankStatusEnumMap[instance.status]!,
      'accounts': instance.accountList.map((e) => e.toJson()).toList(),
      'beneficiaries': instance.beneficiaryList.map((e) => e.toJson()).toList(),
    };

const _$LeanBankStatusEnumMap = {
  LeanBankStatus.ACTIVE: 'ACTIVE',
  LeanBankStatus.INACTIVE: 'INACTIVE',
  LeanBankStatus.IN_PROGRESS: 'IN_PROGRESS',
  LeanBankStatus.UNKNOWN: 'UNKNOWN',
};

_LeanAccount _$LeanAccountFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_LeanAccount',
  json,
  ($checkedConvert) {
    final val = _LeanAccount(
      id: $checkedConvert('id', (v) => v as String),
      accountId: $checkedConvert('account_id', (v) => v as String),
      accountName: $checkedConvert('account_name', (v) => v as String),
      accountNumber: $checkedConvert('account_number', (v) => v as String),
      currency: $checkedConvert('currency', (v) => v as String),
      iban: $checkedConvert('iban', (v) => v as String),
      status: $checkedConvert(
        'status',
        (v) => $enumDecode(
          _$LeanAccountStatusEnumMap,
          v,
          unknownValue: LeanAccountStatus.UNKNOWN,
        ),
      ),
      coolOffExpiry: $checkedConvert(
        'cool_off_expiry',
        (v) => v == null ? null : DateTime.parse(v as String),
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'accountId': 'account_id',
    'accountName': 'account_name',
    'accountNumber': 'account_number',
    'coolOffExpiry': 'cool_off_expiry',
  },
);

Map<String, dynamic> _$LeanAccountToJson(_LeanAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'account_id': instance.accountId,
      'account_name': instance.accountName,
      'account_number': instance.accountNumber,
      'currency': instance.currency,
      'iban': instance.iban,
      'status': _$LeanAccountStatusEnumMap[instance.status]!,
      if (instance.coolOffExpiry?.toIso8601String() case final value?)
        'cool_off_expiry': value,
    };

const _$LeanAccountStatusEnumMap = {
  LeanAccountStatus.AWAITING_BENEFICIARY_COOL_OFF:
      'AWAITING_BENEFICIARY_COOL_OFF',
  LeanAccountStatus.ACTIVE: 'ACTIVE',
  LeanAccountStatus.REAUTHORIZE: 'REAUTHORIZE',
  LeanAccountStatus.UNKNOWN: 'UNKNOWN',
};

_Beneficiary _$BeneficiaryFromJson(Map<String, dynamic> json) => $checkedCreate(
  '_Beneficiary',
  json,
  ($checkedConvert) {
    final val = _Beneficiary(
      id: $checkedConvert('id', (v) => v as String),
      paymentDestinationId: $checkedConvert(
        'payment_destination_id',
        (v) => v as String,
      ),
      status: $checkedConvert('status', (v) => v as String),
      beneficiaryCoolOffExpiry: $checkedConvert(
        'beneficiary_cool_off_expiry',
        (v) => v as String?,
      ),
      bankIdentifier: $checkedConvert('bank_identifier', (v) => v as String),
    );
    return val;
  },
  fieldKeyMap: const {
    'paymentDestinationId': 'payment_destination_id',
    'beneficiaryCoolOffExpiry': 'beneficiary_cool_off_expiry',
    'bankIdentifier': 'bank_identifier',
  },
);

Map<String, dynamic> _$BeneficiaryToJson(_Beneficiary instance) =>
    <String, dynamic>{
      'id': instance.id,
      'payment_destination_id': instance.paymentDestinationId,
      'status': instance.status,
      if (instance.beneficiaryCoolOffExpiry case final value?)
        'beneficiary_cool_off_expiry': value,
      'bank_identifier': instance.bankIdentifier,
    };
