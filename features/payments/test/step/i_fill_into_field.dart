import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Usage: I fill {"50"} into {"amount_field"} field
Future<void> iFillIntoField(
  WidgetTester tester,
  String param1,
  String param2,
) async {
  // param1 is the text to enter (e.g., "50")
  // param2 is the field identifier (e.g., "amount_field")

  // Wait for the widget tree to settle
  await tester.pumpAndSettle();

  // Find the text field - you can use different approaches:

  // Option 1: Find by key (if the field has a key)
  if (param2 == "amount_field") {
    final textFieldFinder = find.byKey(Key('amount_field'));

    // If key doesn't exist, try finding by widget type
    if (textFieldFinder.evaluate().isEmpty) {
      // Find TextField widgets
      final textFields = find.byType(TextField);

      // If multiple text fields exist, you might need to be more specific
      if (textFields.evaluate().isNotEmpty) {
        // Take the first TextField (or specify which one you need)
        await tester.enterText(textFields.first, param1);
      } else {
        // Try finding TextFormField if TextField doesn't exist
        final textFormFields = find.byType(TextFormField);
        if (textFormFields.evaluate().isNotEmpty) {
          await tester.enterText(textFormFields.first, param1);
        } else {
          throw Exception('No text field found for $param2');
        }
      }
    } else {
      await tester.enterText(textFieldFinder, param1);
    }
  } else {
    // Generic approach for other field types
    // Try to find by key first
    final keyFinder = find.byKey(Key(param2));
    if (keyFinder.evaluate().isNotEmpty) {
      await tester.enterText(keyFinder, param1);
    } else {
      // Try to find by semantics or other properties
      final semanticsFinder = find.bySemanticsLabel(param2);
      if (semanticsFinder.evaluate().isNotEmpty) {
        await tester.enterText(semanticsFinder, param1);
      } else {
        throw Exception('Field with identifier "$param2" not found');
      }
    }
  }

  // Wait for the UI to update after entering text
  await tester.pumpAndSettle();

  // Wait for the debounced rounding to complete (500ms debounce + buffer)
  await tester.pump(const Duration(milliseconds: 600));
  await tester.pumpAndSettle();
}
