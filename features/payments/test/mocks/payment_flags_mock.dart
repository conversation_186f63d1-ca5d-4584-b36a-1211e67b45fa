import 'package:mocktail/mocktail.dart';
import 'package:payment/payments.dart';

class PaymentFlagsMock extends Mock implements PaymentFlags {
  PaymentFlagsMock() {
    when(() => isLeanPaymentEnabled()).thenReturn(true);
    when(
      () => isLeanPaymentEnabledWhenRemote(
        timeout: any(named: 'timeout'),
        waitForRealtime: any(named: 'waitForRealtime'),
      ),
    ).thenAnswer((_) async => true);
  }
}
