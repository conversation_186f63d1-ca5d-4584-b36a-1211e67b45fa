enum TradeAnalyticsEvent {
  //accounts
  accountSelected('Trade', 'accountSelected'),
  accountBalancehubConnection('Trade', 'accountBalancehubConnection'),
  retrivedListOfAccountsFromApi('Trade', 'retrivedListOfAccountsFromApi'),
  //Trade orders and alerts
  symbolSelected('Trade', 'symbolSelected'),
  placingTrade('Trade', 'placingTrade'),
  placingOrder('Trade', 'placingOrder'),
  placingAlert('Trade', 'placingAlert'),
  tradePlacedResult('Trade', 'tradePlacedResult'),
  orderPlacedResult('Trade', 'orderPlacedResult'),
  alertPlacedResult('Trade', 'alertPlacedResult'),
  // close trades and delete alerts manually
  startCloseTrade('Trade', 'startCloseTrade'),
  closeTradeResult('Trade', 'closeTradeResult'),
  startDeleteAlert('Trade', 'startDeleteAlert'),
  deleteAlertResult('Trade', 'deleteAlertResult'),
  startDeleteOrder('Trade', 'startDeleteOrder'),
  deleteOrderResult('Trade', 'deleteOrderResult'),
  //portfolio
  positionAdded('Trade', 'positionAdded'),
  positionDeleted('Trade', 'positionDeleted'),
  orderAdded('Trade', 'orderAdded'),
  orderDeleted('Trade', 'orderDeleted'),
  alertAdded('Trade', 'alertAdded'),
  alertDeleted('Trade', 'alertDeleted'),
  //Portfolio details page loading status
  loadPortfolioPositionDetails('Trade', 'loadPortfolioPositionDetails'),
  loadPortfolioTradeDetails('Trade', 'loadPortfolioTradeDetails'),
  loadPortfolioOrderDetails('Trade', 'loadPortfolioOrderDetails'),
  loadPortfolioAlertDetails('Trade', 'loadPortfolioAlerDetails'),
  doneLoadingPositionDetails('Trade', 'doneLoadingPositionDetails'),
  doneLoadingTradeDetails('Trade', 'doneLoadingTradeDetails'),
  doneLoadingOrderDetails('Trade', 'doneLoadingOrderDetails'),
  doneLoadingAlertDetails('Trade', 'doneLoadingAlertDetails'),
  failedLoadingPositionDetails('Trade', 'failedLoadingPositionDetails'),
  failedLoadingTradeDetails('Trade', 'failedLoadingTradeDetails'),
  failedLoadingOrderDetails('Trade', 'failedLoadingOrderDetails'),
  failedLoadingAlertDetails('Trade', 'failedLoadingAlertDetails'),
  //Modify
  startModifyTrade('Trade', 'startModifyTrade'),
  modifyTradeResult('Trade', 'modifyTradeResult'),
  startModifyOrder('Trade', 'startModifyOrder'),
  modifyOrderResult('Trade', 'modifyOrderResult'),
  startModifyAlert('Trade', 'startModifyAlert'),
  modifyAlertResult('Trade', 'modifyAlertResult'),
  //search
  startSearchSymbol('Trade', 'startSearchSymbol'),
  searchSymbol('Trade', 'searchSymbol');

  const TradeAnalyticsEvent(this.eventType, this.eventName);

  final String eventType;
  final String eventName;
}
