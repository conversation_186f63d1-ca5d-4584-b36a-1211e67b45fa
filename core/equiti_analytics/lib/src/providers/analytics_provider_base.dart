import '../model/analytics_event.dart';
import '../model/analytics_config.dart';

/// Abstract base class for analytics providers
abstract class AnalyticsProviderBase {
  /// Initializes the analytics provider with configuration
  Future<bool> initialize(AnalyticsConfig configuration);

  /// Sets global attributes for analytics
  Future<bool> setGlobalAttributes(Map<String, dynamic> attributes);

  /// Removes global attributes for analytics
  Future<bool> removeGlobalAttributes(List<String> keys);

  /// Sends a single analytics event
  Future<bool> sendEvent(AnalyticsEvent event);

  Future<String> startInteraction(String actionName);

  void endInteraction(String interactionId);

  /// Clears all stored analytics data
  Future<bool> clearData();

  /// Gets the provider name for identification
  String get providerName;

  /// Gets the current configuration
  AnalyticsConfig? get config;

  /// Disposes of any resources used by the provider
  Future<void> dispose();
}

/// Mixin for common analytics provider functionality
mixin AnalyticsProviderMixin on AnalyticsProviderBase {
  AnalyticsConfig? _config;

  @override
  AnalyticsConfig? get config => _config;

  /// Sets the configuration
  void setConfig(AnalyticsConfig newConfig) {
    _config = newConfig;
  }
}
