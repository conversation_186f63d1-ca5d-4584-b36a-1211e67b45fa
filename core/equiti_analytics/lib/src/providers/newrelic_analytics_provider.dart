import 'package:newrelic_mobile/config.dart';
import 'package:newrelic_mobile/newrelic_mobile.dart';

import '../model/analytics_event.dart';
import '../model/analytics_config.dart';
import 'analytics_provider_base.dart';

/// New Relic implementation of analytics provider
class NewRelicAnalyticsProvider extends AnalyticsProviderBase
    with AnalyticsProviderMixin {
  bool _isInitialized = false;

  @override
  String get providerName => 'NewRelic';

  @override
  Future<bool> initialize(AnalyticsConfig configuration) async {
    try {
      if (configuration.newRelicConfig == null) {
        return false;
      }

      setConfig(configuration);

      final newRelicConfig = configuration.newRelicConfig!;

      // Configure New Relic based on platform
      final nrConfig = Config(
        accessToken: newRelicConfig.appToken,
        analyticsEventEnabled: newRelicConfig.analyticsEventEnabled,
        networkErrorRequestEnabled: newRelicConfig.networkErrorRequestEnabled,
        networkRequestEnabled: newRelicConfig.networkRequestEnabled,
        crashReportingEnabled: newRelicConfig.crashReportingEnabled,
        interactionTracingEnabled: newRelicConfig.interactionTracingEnabled,
        httpResponseBodyCaptureEnabled:
            newRelicConfig.httpResponseBodyCaptureEnabled,
        loggingEnabled: newRelicConfig.loggingEnabled,
        webViewInstrumentation: newRelicConfig.webViewInstrumentation,
        printStatementAsEventsEnabled:
            newRelicConfig.printStatementAsEventsEnabled,
        httpInstrumentationEnabled: newRelicConfig.httpInstrumentationEnabled,
      );

      await NewrelicMobile.instance.startAgent(nrConfig);

      // Set global attributes (with prefix to avoid conflicts)
      for (final entry in configuration.globalAttributes.entries) {
        await NewrelicMobile.instance.setAttribute(entry.key, entry.value);
      }

      _isInitialized = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> setGlobalAttributes(Map<String, dynamic> attributes) async {
    bool success = false;
    for (final entry in attributes.entries) {
      success = await NewrelicMobile.instance.setAttribute(
        entry.key,
        entry.value,
      );
    }
    return success;
  }

  @override
  Future<bool> removeGlobalAttributes(List<String> keys) async {
    bool success = false;
    for (final key in keys) {
      success = await NewrelicMobile.instance.removeAttribute(key);
    }
    return success;
  }

  @override
  Future<bool> sendEvent(AnalyticsEvent event) async {
    if (!_isInitialized) {
      return false;
    }

    try {
      final success = await NewrelicMobile.instance.recordCustomEvent(
        event.eventType,
        eventName: event.eventName,
        eventAttributes: event.attributes,
      );

      return success;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> clearData() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      await NewrelicMobile.instance.setUserId("");
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> dispose() async {
    _isInitialized = false;
  }

  @override
  Future<String> startInteraction(String actionName) async =>
      await NewrelicMobile.instance.startInteraction(actionName);

  @override
  void endInteraction(String interactionId) =>
      NewrelicMobile.instance.endInteraction(interactionId);
}
