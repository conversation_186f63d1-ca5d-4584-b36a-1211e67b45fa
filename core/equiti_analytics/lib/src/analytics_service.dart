import 'package:equiti_analytics/src/enum/analytics_global_attributes.dart';

import 'model/analytics_event.dart';
import 'model/analytics_config.dart';
import 'providers/analytics_provider_base.dart';

/// Main service interface for analytics operations
class AnalyticsService {
  AnalyticsService(this._provider);

  final AnalyticsProviderBase _provider;

  bool _isInitialized = false;

  /// Initializes the analytics service with configuration
  Future<bool> initialize(AnalyticsConfig config) async {
    try {
      final success = await _provider.initialize(config);
      if (success) {
        _isInitialized = true;
      }
      return success;
    } catch (e) {
      _isInitialized = false;
      return false;
    }
  }

  /// Checks if the service is initialized and available
  bool get isInitialized => _isInitialized;

  // Event sending methods

  Future<bool> setGlobalAttributes(
    Map<AnalyticsGlobalAttributes, dynamic> attributes,
  ) async {
    if (!_isInitialized) return false;

    return _provider.setGlobalAttributes(
      attributes.map((k, v) {
        return MapEntry(k.name, v);
      }),
    );
  }

  Future<bool> removeGlobalAttributes(
    List<AnalyticsGlobalAttributes> keys,
  ) async {
    if (!_isInitialized) return false;
    return _provider.removeGlobalAttributes(
      keys.map((key) => key.name).toList(),
    );
  }

  /// Sends a business event
  Future<bool> sendEvent({
    required String eventType,
    required String eventName,
    Map<String, dynamic>? metadata,
    String? userId,
  }) async {
    if (!_isInitialized) return false;
    final event = AnalyticsEvent(
      eventType: eventType,
      eventName: eventName,
      attributes: {'eventName': eventName, ...?metadata},
      userId: userId,
    );
    return _provider.sendEvent(event);
  }

  /// Starts a New Relic interaction and returns its identifier
  Future<String?> startInteraction(String actionName) async {
    if (!_isInitialized) return null;
    return _provider.startInteraction(actionName);
  }

  /// Ends a previously started New Relic interaction
  void endInteraction(String interactionId) {
    if (!_isInitialized) return;
    _provider.endInteraction(interactionId);
  }

  /// Disposes of the analytics service
  Future<void> dispose() async {
    await _provider.dispose();
    _isInitialized = false;
  }
}
