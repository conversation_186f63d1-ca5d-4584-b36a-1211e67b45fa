/// A core package for sending business metrics events to analytics providers
library equiti_analytics;

// Domain exports
export 'src/model/analytics_event.dart';
export 'src/model/analytics_config.dart';
export 'src/model/appsflyer_config.dart';

// Data exports
export 'src/providers/analytics_provider_base.dart';
export 'src/providers/newrelic_analytics_provider.dart';
export 'src/services/appsflyer_service.dart';

// Service exports
export 'src/analytics_service.dart';

// DI exports
export 'src/di/analytics_module.dart';
export 'src/di/di_initializer.module.dart';

// Events exports
export 'src/events/onboarding_analytics_event.dart';
export 'src/events/trade_analytics_event.dart';

export 'src/events/payments_analytics_event.dart';

export 'src/enum/analytics_global_attributes.dart';
export 'package:newrelic_mobile/newrelic_navigation_observer.dart';
