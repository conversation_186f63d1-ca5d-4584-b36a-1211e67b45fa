import 'package:flutter/foundation.dart';
import 'package:remote_configuration/remote_configuration.dart';

import 'flag_key.dart';

class FeatureFlagService {
  const FeatureFlagService(this._rc);

  final RemoteConfiguration _rc;

  T get<T>(FlagKey<T> flag) {
    if (T == bool)
      return _rc.getBool(flag.key, defaultValue: flag.defaultValue as bool)
          as T;
    if (T == int)
      return _rc.getInt(flag.key, defaultValue: flag.defaultValue as int) as T;
    if (T == double)
      return _rc.getDouble(flag.key, defaultValue: flag.defaultValue as double)
          as T;
    if (T == String)
      return _rc.getString(flag.key, defaultValue: flag.defaultValue as String)
          as T;
    if (flag.defaultValue is Map<String, dynamic>) {
      return _rc.getJson(
            flag.key,
            defaultValue: flag.defaultValue as Map<String, dynamic>,
          )
          as T;
    }
    return flag.defaultValue;
  }

  Stream<T> watch<T>(FlagKey<T> flag) =>
      _rc.watchKey<T>(flag.key, defaultValue: flag.defaultValue);

  ValueListenable<T> listenable<T>(FlagKey<T> flag) =>
      _rc.listenableKey<T>(flag.key, defaultValue: flag.defaultValue);

  Future<void> fetch() => _rc.fetch();

  Future<bool> activate() => _rc.activate();

  Future<bool> fetchAndActivate({bool force = false}) =>
      _rc.fetchAndActivate(force: force);

  /// Waits for a remote value to be available for the given [flag].
  ///
  /// This method ensures that the returned value comes from Firebase Remote Config,
  /// not from default values. It's useful for first app launch scenarios where you
  /// need to guarantee that remote values are loaded before proceeding.
  ///
  /// Only supported when using FirebaseRemoteConfigBackend. Throws [UnsupportedError]
  /// if used with other backends.
  ///
  /// Parameters:
  /// - [flag]: The flag key to retrieve
  /// - [timeout]: Optional timeout duration. If specified and exceeded, throws [RemoteValueTimeoutException]
  /// - [waitForRealtime]: If true (default), waits for real-time updates if initial fetch doesn't return remote values.
  ///
  /// Returns the value from remote config. If the value is not available from remote,
  /// returns the flag's default value.
  Future<T> getWhenRemote<T>(
    FlagKey<T> flag, {
    Duration? timeout,
    bool waitForRealtime = true,
  }) async {
    if (T == bool) {
      final value = await _rc.getBoolWhenRemote(
        flag.key,
        timeout: timeout,
        waitForRealtime: waitForRealtime,
      );
      return (value ?? flag.defaultValue as bool) as T;
    }
    if (T == int) {
      final value = await _rc.getIntWhenRemote(
        flag.key,
        timeout: timeout,
        waitForRealtime: waitForRealtime,
      );
      return (value ?? flag.defaultValue as int) as T;
    }
    if (T == double) {
      final value = await _rc.getDoubleWhenRemote(
        flag.key,
        timeout: timeout,
        waitForRealtime: waitForRealtime,
      );
      return (value ?? flag.defaultValue as double) as T;
    }
    if (T == String) {
      final value = await _rc.getStringWhenRemote(
        flag.key,
        timeout: timeout,
        waitForRealtime: waitForRealtime,
      );
      return (value ?? flag.defaultValue as String) as T;
    }
    if (flag.defaultValue is Map<String, dynamic>) {
      final value = await _rc.getJsonWhenRemote(
        flag.key,
        timeout: timeout,
        waitForRealtime: waitForRealtime,
      );
      return (value ?? flag.defaultValue as Map<String, dynamic>) as T;
    }
    // For unsupported types, return default value
    return flag.defaultValue;
  }
}
