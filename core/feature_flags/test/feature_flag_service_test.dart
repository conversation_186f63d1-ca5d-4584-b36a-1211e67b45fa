import 'package:flutter_test/flutter_test.dart';
import 'package:feature_flags/feature_flags.dart';
import 'package:remote_configuration/remote_configuration.dart';

void main() {
  group('FeatureFlagService', () {
    test('get returns typed values with defaults', () async {
      final rc = await RemoteConfiguration.mock({
        'a.bool': true,
        'a.int': 42,
        'a.double': 3.14,
        'a.string': 'hi',
        'a.json': <String, dynamic>{'x': 1},
      });
      final svc = FeatureFlagService(rc);

      const kb = FlagKey<bool>(key: 'a.bool', defaultValue: false);
      const ki = FlagKey<int>(key: 'a.int', defaultValue: 0);
      const kd = FlagKey<double>(key: 'a.double', defaultValue: 0.0);
      const ks = FlagKey<String>(key: 'a.string', defaultValue: '');
      const kj = FlagKey<Map<String, dynamic>>(key: 'a.json', defaultValue: {});

      expect(svc.get(kb), isTrue);
      expect(svc.get(ki), 42);
      expect(svc.get(kd), closeTo(3.14, 1e-9));
      expect(svc.get(ks), 'hi');
      expect(svc.get(kj), containsPair('x', 1));

      // Unknown type falls back to default
      const ku = FlagKey<List<int>>(key: 'a.unknown', defaultValue: <int>[]);
      expect(svc.get(ku), isEmpty);
    });

    test('watch emits updates when RC changes', () async {
      final rc = await RemoteConfiguration.mock({'a.bool': false});
      final svc = FeatureFlagService(rc);
      const kb = FlagKey<bool>(key: 'a.bool', defaultValue: false);

      final values = <bool>[];
      final sub = svc.watch(kb).listen(values.add);

      // Initial value not emitted; simulate change via override
      rc.setOverride('a.bool', true);
      await Future<void>.delayed(const Duration(milliseconds: 10));

      expect(values, contains(true));
      await sub.cancel();
    });

    test('listenable reflects current value', () async {
      final rc = await RemoteConfiguration.mock({'a.int': 1});
      final svc = FeatureFlagService(rc);
      const ki = FlagKey<int>(key: 'a.int', defaultValue: 0);

      final ln = svc.listenable(ki);
      expect(ln.value, 1);
      rc.setOverride('a.int', 2);
      await Future<void>.delayed(const Duration(milliseconds: 10));
      expect(ln.value, 2);
    });

    test('getWhenRemote throws UnsupportedError with mock backend', () async {
      final rc = await RemoteConfiguration.mock({'a.bool': true});
      final svc = FeatureFlagService(rc);
      const kb = FlagKey<bool>(key: 'a.bool', defaultValue: false);

      // Mock backend doesn't support getWhenRemote methods
      expect(() => svc.getWhenRemote(kb), throwsA(isA<UnsupportedError>()));
    });

    test('getWhenRemote returns default for unsupported types', () async {
      final rc = await RemoteConfiguration.mock({});
      final svc = FeatureFlagService(rc);
      const ku = FlagKey<List<int>>(key: 'a.list', defaultValue: [1, 2, 3]);

      // For unsupported types, should return default value without calling backend
      final result = await svc.getWhenRemote(ku);
      expect(result, [1, 2, 3]);
    });

    test('getWhenRemote handles all supported types', () async {
      final rc = await RemoteConfiguration.mock({});
      final svc = FeatureFlagService(rc);

      // Test that each type path is covered (will throw UnsupportedError with mock)
      const kb = FlagKey<bool>(key: 'a.bool', defaultValue: false);
      const ki = FlagKey<int>(key: 'a.int', defaultValue: 0);
      const kd = FlagKey<double>(key: 'a.double', defaultValue: 0.0);
      const ks = FlagKey<String>(key: 'a.string', defaultValue: '');
      const kj = FlagKey<Map<String, dynamic>>(key: 'a.json', defaultValue: {});

      // All should throw UnsupportedError with mock backend
      expect(() => svc.getWhenRemote(kb), throwsA(isA<UnsupportedError>()));
      expect(() => svc.getWhenRemote(ki), throwsA(isA<UnsupportedError>()));
      expect(() => svc.getWhenRemote(kd), throwsA(isA<UnsupportedError>()));
      expect(() => svc.getWhenRemote(ks), throwsA(isA<UnsupportedError>()));
      expect(() => svc.getWhenRemote(kj), throwsA(isA<UnsupportedError>()));
    });
  });
}
