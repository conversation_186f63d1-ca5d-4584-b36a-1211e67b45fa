{"@@locale": "en", "@@prefix_key": "onboarding", "onboarding_enterEmail": "Enter email", "@onboarding_enterEmail": {}, "onboarding_emailAddress": "Email address", "@onboarding_emailAddress": {}, "onboarding_emailTooltip": "Enter your email address", "@onboarding_emailTooltip": {}, "onboarding_createPassword": "Create password", "onboarding_makeItDifficult": "Try to make it a little difficult.", "onboarding_passwordPrivateNote": "Your password is private to you. <PERSON><PERSON><PERSON> will never ask for your password at any point.", "onboarding_continueText": "Continue", "onboarding_whatIsYourEmail": "What is your email address?", "onboarding_useThisToLogin": "You’ll use this to login next time", "onboarding_privacyPolicyAgreement": "By continuing, you agree to the privacy policy of Equiti Group and its affiliates. Tap below to read it.", "onboarding_tellUsYourName": "Tell us your name", "onboarding_enterFirstAndLastName": "Enter first and last name as they appear in your official government documents.", "onboarding_firstName": "First name", "onboarding_lastName": "Last name", "onboarding_enterFirstName": "Enter first name", "onboarding_enterLastName": "Enter last name", "onboarding_enterLegalFirstName": "Enter your legal first name", "onboarding_enterLegalLastName": "Enter your legal last name", "onboarding_enterFirstNameAndLastNameError": "Please enter your first and last name.", "onboarding_invalidCharactersError": "Name contains invalid characters. Please try again.", "onboarding_consecutiveHyphensError": "Name contains consecutive hyphens. Please try again.", "onboarding_consecutiveApostrophesError": "Name contains consecutive apostrophes. Please try again.", "onboarding_consecutiveSpacesError": "Name contains consecutive spaces. Please try again.", "onboarding_nameTooShortError": "Name is too short. Please try again.", "onboarding_nameTooLongError": "Name is too long. Please try again.", "onboarding_privacyPolicy": "Privacy policy", "onboarding_emailInvalid": "Please enter a valid email address", "onboarding_emailExists": "Email already exists", "onboarding_emailExistsTitle": "You’re already registered!", "onboarding_emailExistsDescription": "This email is attached to an existing Equiti account, sign in or use another email", "onboarding_signIn": "Sign in", "onboarding_login": "Log in", "@onboarding_login": {}, "onboarding_signup": "Sign up", "@onboarding_signup": {}, "onboarding_choose_how_signup": "Choose how to sign up", "@onboarding_choose_how_signup": {}, "onboarding_signup_options_description": "Sign up with your email or UAE PASS for a faster experience.", "@onboarding_signup_options_description": {}, "onboarding_or": "or", "@onboarding_or": {}, "onboarding_signUpWithEmail": "Sign up with email", "@onboarding_signUpWithEmail": {}, "onboarding_signUpWithUaePass": "Sign up with UAE Pass", "@onboarding_signUpWithUaePass": {}, "onboarding_alreadyGotAccount": "Already got an account?", "@onboarding_alreadyGotAccount": {}, "onboarding_verifyPhoneNumberTitle": "Verify your phone number", "@onboarding_verifyPhoneNumberTitle": {"description": "Title displayed when asking the user to verify their phone number"}, "onboarding_verifyPhoneNumberSubtitle": "Keep your account secure and stay updated with important notifications.", "@onboarding_verifyPhoneNumberSubtitle": {"description": "Message below the title explaining the purpose of phone number verification"}, "onboarding_sendCodeViaWhatsApp": "Send code via WhatsApp", "@onboarding_sendCodeViaWhatsApp": {"description": "Button label to send the verification code through WhatsApp"}, "onboarding_sendCodeViaSMS": "Send code via SMS", "@onboarding_sendCodeViaSMS": {"description": "Button label to send the verification code through SMS"}, "onboarding_doThisLater": "I’ll do this later", "@onboarding_doThisLater": {"description": "Link or button text to skip phone verification and proceed"}, "onboarding_mobilePhoneNumberTitle": "What’s your mobile phone number?", "@onboarding_mobilePhoneNumberTitle": {"description": "Title for the screen where the user inputs their mobile phone number"}, "onboarding_mobilePhoneNumberSubtitle": "This allows us to send you verification codes and important alerts.", "@onboarding_mobilePhoneNumberSubtitle": {"description": "Subtitle explaining why a mobile phone number is required"}, "onboarding_phoneNumberPlaceholder": "(79) 000-0000", "@onboarding_phoneNumberPlaceholder": {"description": "Placeholder text in the phone number input field"}, "onboarding_phoneNumber": "Phone Number", "@onboarding_phoneNumber": {"description": "Label for the input field where the user enters their phone number"}, "onboarding_confirmButton": "Confirm", "@onboarding_confirmButton": {"description": "Button text to submit the phone number or verification code"}, "onboarding_consentMarketingText": "By pressing \"Confirm\" you consent to receiving marketing communications. You can disable this option at any time by updating your preferences.", "@onboarding_consentMarketingText": {"description": "Legal disclaimer about marketing consent shown below the confirm button"}, "onboarding_verifyNumberTitle": "Verify your phone number", "@onboarding_verifyNumberTitle": {"description": "Title for the screen where the user enters the verification code"}, "onboarding_verifyNumberMessageWhatsapp": "You will receive a WhatsApp message with a verification code on number ending {lastDigits}.", "@onboarding_verifyNumberMessageWhatsapp": {"description": "Instructional message telling the user where the code will be sent", "placeholders": {"lastDigits": {"description": "The last 4 digits of the user's phone number"}}}, "onboarding_verifyNumberMessageSms": "You will receive a SMS message with a verification code on number ending {lastDigits}.", "@onboarding_verifyNumberMessageSms": {"description": "Instructional message telling the user where the code will be sent", "placeholders": {"lastDigits": {"description": "The last 4 digits of the user's phone number"}}}, "onboarding_verifyYourIdentity": "Verify Your Identity", "@onboarding_verifyYourIdentity": {"description": "Title for the verify identity screen"}, "onboarding_verifyIdentitySecurityMessage": "This will help secure your account and adheres to regulatory requirements. Your documents will be encrypted and safely stored.", "@onboarding_verifyIdentitySecurityMessage": {"description": "Subtitle for the verify identity screen"}, "onboarding_otpMightBeDelayed": "It might take a few minutes for the OTP to arrive", "@onboarding_otpMightBeDelayed": {"description": "Text displayed when the OTP might be delayed"}, "onboarding_loading": "Loading", "@onboarding_loading": {"description": "Text displayed when the button is loading"}, "onboarding_resendCode": "Resend code", "@onboarding_resendCode": {"description": "Link or button to request a new verification code"}, "onboarding_editPhoneNumber": "Edit phone number", "@onboarding_editPhoneNumber": {"description": "Link or button to go back and edit the entered phone number"}, "onboarding_generateNewCodeIn": "Generate new code in ", "@onboarding_generateNewCodeIn": {"description": "Text displaying countdown before user can request a new verification code"}, "onboarding_otpConfirmButton": "Confirm", "@onboarding_otpConfirmButton": {"description": "<PERSON>ton to submit the entered OTP code"}, "onboarding_phoneNumberVerifiedTitle": "Phone number verified!", "@onboarding_phoneNumberVerifiedTitle": {"description": "Success title shown after the user successfully verifies their number"}, "onboarding_phoneNumberVerifiedSubtitle": "Thank you! Please be aware if your phone number changes, you’ll need to verify it again.", "@onboarding_phoneNumberVerifiedSubtitle": {"description": "Message informing user about future verification if phone number changes"}, "onboarding_continueButton": "Continue", "@onboarding_continueButton": {"description": "Button to proceed to the next step after verification"}, "onboarding_personalDetailsTitle": "Personal Details", "@onboarding_personalDetailsTitle": {"description": "Title for the personal information section"}, "onboarding_personalDetailsSubtitle": "Share your phone number, date of birth, nationality and address to secure your account.", "@onboarding_personalDetailsSubtitle": {"description": "Instruction text prompting the user to complete their profile information"}, "onboarding_alreadyRegisteredTitle": "You’re already registered!", "@onboarding_alreadyRegisteredTitle": {"description": "Title displayed when the phone number is already associated with an existing account"}, "onboarding_alreadyRegisteredMessage": "This phone number is attached to an existing Equiti account, sign in or use another number.", "@onboarding_alreadyRegisteredMessage": {"description": "Explanation that the number is linked to an account and user needs to sign in or use a different number"}, "onboarding_signInButton": "Sign in", "@onboarding_signInButton": {"description": "Button label to proceed to sign in"}, "onboarding_useAnotherNumberButton": "Use another phone number", "@onboarding_useAnotherNumberButton": {"description": "But<PERSON> label to return to the phone number input screen and try another number"}, "onboarding_otpTroubleTitle": "Having trouble with OTP?", "@onboarding_otpTroubleTitle": {"description": "Title for the screen shown when OTP verification fails"}, "onboarding_otpTroubleMessage": "We couldn’t verify your OTP. You can try a different verification method or update your phone number to receive a new code.", "@onboarding_otpTroubleMessage": {"description": "Message suggesting alternatives if OTP verification fails"}, "onboarding_chooseAlternativeMethod": "Choose alternative method", "@onboarding_chooseAlternativeMethod": {"description": "Button label to try another way of verification"}, "onboarding_otpLockedTitle": "Too many unsuccessful attempts", "@onboarding_otpLockedTitle": {"description": "Title shown when user fails OTP verification multiple times and gets locked out"}, "onboarding_otpLockedMessage": "OTP is currently locked due to too many unsuccessful attempts, please try again later.", "@onboarding_otpLockedMessage": {"description": "Message informing the user that OTP entry is locked temporarily"}, "onboarding_contactCustomerService": "Contact Customer Service", "@onboarding_contactCustomerService": {"description": "Button label to get support after OTP lockout"}, "onboarding_skipForNow": "Skip for now", "@onboarding_skipForNow": {"description": "Option for the user to skip OTP or customer service and move forward"}, "onboarding_errorOccurred": "An error occurred", "onboarding_invalidMobileNumberTitle": "Invalid mobile number", "@onboarding_invalidMobileNumberTitle": {"description": "Title shown when user enters an invalid mobile number"}, "onboarding_invalidMobileNumberMessage": "Please enter a valid mobile number and try again.", "@onboarding_invalidMobileNumberMessage": {"description": "Message explaining that the mobile number entered is invalid"}, "onboardingWelcomeTitle": "Welcome to E<PERSON><PERSON>", "onboardingWelcomeDescription": "Step into regulated online financial markets with smart tools that grow with your ambitions.", "onboardingTradeTitle": "Trade what you like", "onboardingTradeDescription": "One app, thousands of assets — everything you need to build a well-rounded portfolio.", "onboardingToolsTitle": "Tools that think ahead", "onboardingToolsDescription": "Analyse assets, monitor margin health, tweak risk controls on live trades, and track all activity.", "onboardingFundingTitle": "Flexible funding", "onboardingFundingDescription": "Crypto, cards, wallets, wires — multiple ways to deposit and withdraw quickly, simply, and safely.", "onboardingSecureTitle": "Secure & supported", "onboardingSecureDescription": "Assurance at every level — from biometric logins to local support that speaks your language.", "onboarding_createTradingAccount": "Create a trading account", "onboarding_createTradingAccountDescription": "Your profile is set up! Next, we’ll need you to set up a trading account. You can have multiple trading accounts based on trading platforms or currencies.", "onboarding_accountCreation": "Account Creation", "onboarding_choosePlatform": "Choose your platform", "onboarding_chooseAccount": "Choose your account", "onboarding_chooseAccountType": "Choose Your Account Type", "onboarding_fundYourTrades": "Fund your trades", "@onboarding_fundYourTrades": {"description": "Title text displayed on the deposit intro screen encouraging users to fund their trading account"}, "onboarding_depositIntroDescription": "Securely add funds and easily withdraw with the payment method of your choice.", "@onboarding_depositIntroDescription": {"description": "Description text on the deposit intro screen explaining the security and flexibility of funding options"}, "onboarding_addFunds": "Add funds", "@onboarding_addFunds": {"description": "Primary button text on the deposit intro screen that navigates to deposit payment options"}, "onboarding_doThisStepLater": "Do this later", "@onboarding_doThisStepLater": {"description": "Secondary button text on the deposit intro screen that allows users to skip the funding step and proceed to the main app"}, "onboarding_enableSwapFreeFeature": "Choose the type of trading account that best suits your preferences and trading style.", "onboarding_maxLeverageNeeded": "What max leverage would you like?", "onboarding_maxLeverageNeededDescription": "Trade with real money and withdraw any profit you may take", "onboarding_chooseAccountCurrency": "Choose account currency", "onboarding_chooseAccountCurrencyDescription": "This will be the default currency for your account. You can’t change this later, so choose carefully.", "onboarding_currency": "<PERSON><PERSON><PERSON><PERSON>", "onboarding_selectCurrency": "Select Currency", "onboarding_personalizeYourAccount": "Personalise your account", "onboarding_setNicknameDescription": "Set a nickname to easily identify this account. You can change this later in settings.", "onboarding_accountNickname": "Account nickname", "onboarding_enterNickname": "Enter a nickname", "onboarding_enterNicknameForAccount": "Enter a nickname for your account", "onboarding_nameValidationDescription": "Please enter a name between 1-36 characters", "onboarding_nicknameReferenceText": "This nickname is for your reference only and won’t be shared with anyone.", "onboarding_somethingWentWrong": "Something went wrong", "onboarding_errorDescription": "We couldn’t complete your account setup.", "onboarding_goBack": "Go back", "onboarding_createPasswordForAccount": "Create a password for your account", "onboarding_passwordInfo": "Your password is private to you. <PERSON><PERSON><PERSON> will never ask for your password at any point.", "onboarding_nickname": "Nickname", "onboarding_type": "Type", "onboarding_standard": "Standard", "onboarding_premier": "Premier", "onboarding_accountLeverage": "Account leverage", "onboarding_swapFreeAccount": "Swap Free Account", "onboarding_yes": "Yes", "onboarding_no": "No", "onboarding_termsAndConditions": "Terms and Conditions", "onboarding_useLettersAndSpaces": "Please use only letters, numbers, and spaces.", "onboarding_selectCountry": "Select Country", "onboarding_useAnotherEmail": "Use another email", "onboarding_blacklistedCountryTitle": "Sorry!", "@onboarding_blacklistedCountryTitle": {"description": "Title for the blacklisted country bottom sheet"}, "onboarding_blacklistedCountryBody": "Unfortunately we cannot open an account for residents of your country.", "@onboarding_blacklistedCountryBody": {"description": "Body text for the blacklisted country bottom sheet"}, "onboarding_blacklistedCountryButtonText": "I understand", "@onboarding_blacklistedCountryButtonText": {"description": "Button text for the blacklisted country bottom sheet"}, "onboarding_progress_tracker_discoverMoreTitle": "Discover More", "onboarding_progress_tracker_discoverMoreItem1Title": "Competitive rates", "onboarding_progress_tracker_discoverMoreItem1Description": "Low-to-zero commission and tight spreads from 0.0 pips*.", "onboarding_progress_tracker_discoverMoreItem2Title": "Easy to use", "onboarding_progress_tracker_discoverMoreItem2Description": "In-depth data and analysis tools, easy to use for traders of all levels.", "onboarding_progress_tracker_discoverMoreItem3Title": "Refer a friend", "onboarding_progress_tracker_discoverMoreItem3Description": "Earn up to 100% of your friend's trading commission.", "onboarding_progress_tracker_discoverMoreItem4Title": "Signal center", "onboarding_progress_tracker_discoverMoreItem4Description": "Get up to 40 trade ideas per day with target entry point", "onboarding_backToHome": "Back to Home", "onboarding_invalidNameLength": "Invalid name length. Must be 1–36 characters.", "onboarding_fundAccount": "Fund account", "@onboarding_fundAccount": {"description": "Button text to fund a trading account"}, "onboarding_backToAccounts": "Back to Accounts", "@onboarding_backToAccounts": {"description": "Button text to navigate back to accounts screen"}, "onboarding_reachedMaxAccount": "You have reached the maximum number of accounts for this platform.", "onboarding_progress_tracker_exploreTheAppTitle": "Explore the app", "onboarding_progress_tracker_exploreTheAppDescription": "Discover features of Equiti app", "onboarding_progress_tracker_completePersonalInfo_title": "Complete sign-up", "onboarding_progress_tracker_completeFinancialInfo_title": "Complete sign-up", "onboarding_progress_tracker_completeAccountCreation_title": "Choose your account type", "onboarding_progress_tracker_depositInProgress_title": "Add funds", "onboarding_progress_tracker_depositInReview_title": "Processing", "onboarding_progress_tracker_placeTrade_title": "Place your first trade", "onboarding_progress_tracker_moreAboutUs_title": "More about us", "onboarding_progress_tracker_learnTradingBasics_title": "Learn trading basics", "onboarding_progress_tracker_completeVerification_title": "Upload photo", "onboarding_progress_tracker_verificationFailed_title": "Retry verification", "onboarding_progress_tracker_verificationInReview_title": "Waiting for approval", "onboarding_progress_tracker_verificationRejected_title": "We may review the outcome", "onboarding_progress_tracker_completePersonalInfo_description": "Provide basic details about yourself.", "onboarding_progress_tracker_completeFinancialInfo_description": "Share details about your financial background.", "onboarding_progress_tracker_completeAccountCreation_description": "Select an account that suits your needs.", "onboarding_progress_tracker_depositInProgress_description": "Choose your preferred payment method.", "onboarding_progress_tracker_depositInReview_description": "You'll receive confirmation once it's complete.", "onboarding_progress_tracker_placeTrade_description": "Explore the trading platform.", "onboarding_progress_tracker_moreAboutUs_description": "Learn more about how we work", "onboarding_progress_tracker_learnTradingBasics_description": "Read our educational articles", "onboarding_progress_tracker_completeVerification_description": "Provide a clear photo of your ID.", "onboarding_progress_tracker_verificationFailed_description": "Upload documents again and complete your verification.", "onboarding_progress_tracker_verificationInReview_description": "We'll notify you as soon as you can proceed.", "onboarding_progress_tracker_verificationRejected_description": "If further action is needed, we'll be in touch.", "onboarding_progressState_personalInfo": "Personal Info", "onboarding_progressState_financialInfo": "Financial Info", "onboarding_progressState_verifyIdentity": "Verify Identity", "onboarding_progressState_accountCreation": "Account Creation", "onboarding_progressState_addFunds": "Add Funds", "onboarding_progressState_placeTrade": "Place Trade", "onboarding_uaePassConfirmingDetailsTitle": "Confirming your details", "@onboarding_uaePassConfirmingDetailsTitle": {"description": "Title for the UAE Pass confirmation screen"}, "onboarding_uaePassConfirmingDetailsDescription": "We've securely retrieved your details from UAE PASS. These cannot be changed within the app, but will automatically stay up to date each time you log in with UAE PASS.", "@onboarding_uaePassConfirmingDetailsDescription": {"description": "Description explaining how UAE Pass details work"}, "onboarding_redirectCountryTitle": "We need to redirect you", "@onboarding_redirectCountryTitle": {"description": "Title for the redirect country bottom sheet"}, "onboarding_redirectCountryBody": "It looks like your selected country requires you to sign-up in your browser before using the app. Tap below to continue.", "@onboarding_redirectCountryBody": {"description": "Body text for the redirect country bottom sheet"}, "onboarding_signUpNow": "Sign-up now", "@onboarding_signUpNow": {"description": "Button text for signing up now in redirect country bottom sheet"}, "onboarding_swapAccountTermsAndConditionsLink": "https://eq-cdn.equiti-me.com/website/documents/SCA_Swap_Free_Terms__Conditions_29_Aug_2025.pdf", "onboarding_accountType": "Account type", "onboarding_accountNumber": "Account Number", "onboarding_serverName": "Server Name", "onboarding_platform": "Platform", "onboarding_detailsSentViaEmail": "The same account details were sent to you via email", "onboarding_swap_freeAccount": "Swap-free account", "onboarding_liveAccountCreated": "Live account successfully created", "onboarding_demoAccountCreated": "Demo account successfully created", "onboarding_tradingServer": "Trading Server", "onboarding_phoneNumberAlreadyInUseTitle": "Phone number already in use", "@onboarding_phoneNumberAlreadyInUseTitle": {"description": "Title shown when the entered phone number is already associated with another account"}, "onboarding_phoneNumberAlreadyInUseMessage": "This number is linked to an existing account. Please try a different one.", "@onboarding_phoneNumberAlreadyInUseMessage": {"description": "Message explaining that the phone number is linked to an existing account and user should try another"}, "onboarding_tryAgain": "try again", "@onboarding_tryAgain": {"description": "<PERSON><PERSON> text to try again after an error or conflict"}, "onboarding_errorInvalidPhoneNumber": "Invalid phone number", "@onboarding_errorInvalidPhoneNumber": {"description": "Error message when phone number format is invalid"}, "onboarding_errorDuplicatePhoneNumber": "This phone number is already registered", "@onboarding_errorDuplicatePhoneNumber": {"description": "Error message when phone number is already associated with another account"}, "onboarding_errorUnknownError": "An unknown error occurred", "@onboarding_errorUnknownError": {"description": "Generic error message for unknown errors"}, "onboarding_errorGeneralError": "Failed to verify phone number. Please try again.", "@onboarding_errorGeneralError": {"description": "General error message for phone number verification failures"}, "onboarding_loggingIn": "Please wait while we log you in", "onboarding_swapAccountTermsAndConditions": "By selecting a Swap-free account you agree to the terms and conditions for Swap-free accounts", "onboarding_signInWithEmail": "Sign in with email", "@onboarding_signInWithEmail": {"description": "<PERSON><PERSON> text for signing in with email"}, "onboarding_tryDifferentMethod": "Try a different method", "@onboarding_tryDifferentMethod": {"description": "Title for error bottom sheet when user needs to try different method"}, "onboarding_alreadySignedUpWithEmail": "It looks like you have already signed up for Equiti. Please login using your email.", "@onboarding_alreadySignedUpWithEmail": {"description": "Error message when user has already signed up with email"}, "onboarding_firstNameRequired": "First name is required. Please enter your first name.", "onboarding_lastNameRequired": "Last name is required. Please enter your last name."}