// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'package:duplo/duplo.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_steps/step/screenshot_verified.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Text and Chevron Display''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens('''Display text with a Chevron icon''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display text with a Chevron icon''');
        await theAppIsRendered(
          tester,
          TextChevronWidget(
            title: 'test',
            onPressed: () {
              print("text");
            },
          ),
          scenarios: [],
        );
        await screenshotVerified(tester, 'text_with_chevron_icon');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display text with a Chevron icon''', success);
      }
    });
    testGoldens('''Display long text with a Chevron icon''', (tester) async {
      var success = true;
      try {
        await beforeEach('''Display long text with a Chevron icon''');
        await theAppIsRendered(
          tester,
          TextChevronWidget(
            title:
                'test test test test test test test test test test test test test',
            onPressed: () {
              print("text");
            },
          ),
          scenarios: [],
        );
        await screenshotVerified(tester, 'long_text_with_chevron_icon');
      } on TestFailure {
        success = false;
        rethrow;
      } finally {
        await afterEach('''Display long text with a Chevron icon''', success);
      }
    });
    testGoldens(
      '''Display a customized title with a leading and Chevron icon''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Display a customized title with a leading and Chevron icon''',
          );
          await theAppIsRendered(
            tester,
            TextChevronWidget(
              title: '',
              trailingText: 'View all',
              leadingIcon: Icon(Icons.watch_later_outlined),
              titleWidget: Expanded(child: Text('Market: closed')),
              onPressed: () {
                print("customized text");
              },
            ),
            scenarios: [],
          );
          await screenshotVerified(
            tester,
            'customized_title_with_leading_and_chevron_icon',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Display a customized title with a leading and Chevron icon''',
            success,
          );
        }
      },
    );
  });
}
