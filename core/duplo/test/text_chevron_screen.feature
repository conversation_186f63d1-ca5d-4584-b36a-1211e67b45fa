import 'package:equiti_test/equiti_test.dart';
import 'package:duplo/duplo.dart';

Feature: Text and Chevron Display
  As a user
  I want to see text with a Chevron icon
  So that I can select an option

  @testMethodName: testGoldens
  Scenario: Display text with a Chevron icon
    Given The {TextChevronWidget(title: 'test',onPressed:(){print("text");} )} app is rendered {scenarios:[]}
    Then screenshot verified {'text_with_chevron_icon'}

  @testMethodName: testGoldens
  Scenario: Display long text with a Chevron icon
    Given The {TextChevronWidget(title: 'test test test test test test test test test test test test test',onPressed:(){print("text");} )} app is rendered {scenarios:[]}
    Then screenshot verified {'long_text_with_chevron_icon'}

  @testMethodName: testGoldens
  Scenario: Display a customized title with a leading and Chevron icon
    Given The {TextChevronWidget(title: '',trailingText: 'View all',leadingIcon:Icon(Icons.watch_later_outlined),titleWidget: Expanded(child: Text('Market: closed')),onPressed:(){print("customized text");} )} app is rendered {scenarios:[]}
    Then screenshot verified {'customized_title_with_leading_and_chevron_icon'}