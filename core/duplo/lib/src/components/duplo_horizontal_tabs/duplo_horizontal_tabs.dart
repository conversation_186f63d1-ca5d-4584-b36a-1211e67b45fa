import 'package:duplo/src/constants/duplo_radius.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:duplo/src/typography/text_styles.dart';
import 'package:flutter/cupertino.dart';

/// A reusable horizontal tabs component that provides a segmented control interface.
///
/// The DuploHorizontalTabs widget creates a segmented control with customizable
/// styling and supports different variants through factory constructors.
///
/// Features include:
/// * Generic type support for any enum or object type
/// * Customizable text and badge display
/// * Different styling variants (buttonBorder, etc.)
/// * Consistent theming with Duplo design system
/// * Support for badges/counts on tabs
///
/// Example usage:
/// ```dart
/// DuploHorizontalTabs<MyEnum>.buttonBorder(
///   options: [MyEnum.option1, MyEnum.option2],
///   selectedValue: MyEnum.option1,
///   onChanged: (value) => print('Selected: $value'),
///   textBuilder: (option) => option.name,
/// )
/// ```
class DuploHorizontalTabs<T extends Object> extends StatefulWidget {
  /// Creates a horizontal tabs widget with button border styling.
  ///
  /// This variant provides a bordered container with rounded corners,
  /// similar to the design used in account toggles and demo/live switches.
  factory DuploHorizontalTabs.buttonBorder({
    Key? key,
    required List<T> options,
    required T selectedValue,
    required ValueChanged<T> onChanged,
    required String Function(T) textBuilder,
    String? Function(T)? badgeBuilder,
    double borderRadius = DuploRadius.radius_lg_10,
    EdgeInsets padding = const EdgeInsets.all(4),
    EdgeInsets itemPadding = const EdgeInsets.all(8),
  }) {
    return DuploHorizontalTabs._(
      key: key,
      options: options,
      selectedValue: selectedValue,
      onChanged: onChanged,
      textBuilder: textBuilder,
      badgeBuilder: badgeBuilder,
      borderRadius: borderRadius,
      padding: padding,
      itemPadding: itemPadding,
      variant: _DuploHorizontalTabsVariant.buttonBorder,
    );
  }

  const DuploHorizontalTabs._({
    super.key,
    required this.options,
    required this.selectedValue,
    required this.onChanged,
    required this.textBuilder,
    this.badgeBuilder,
    this.borderRadius = DuploRadius.radius_lg_10,
    this.padding = const EdgeInsets.all(4),
    this.itemPadding = const EdgeInsets.all(8),
    required this.variant,
  });

  /// The list of options to display as tabs
  final List<T> options;

  /// The currently selected value
  final T selectedValue;

  /// Callback when a tab is selected
  final ValueChanged<T> onChanged;

  /// Function to build the text for each option
  final String Function(T) textBuilder;

  /// Optional function to build badge text for each option
  final String? Function(T)? badgeBuilder;

  /// Border radius for the container
  final double borderRadius;

  /// Padding inside the segmented control
  final EdgeInsets padding;

  /// Padding for each tab item
  final EdgeInsets itemPadding;

  /// The variant style to use
  final _DuploHorizontalTabsVariant variant;

  @override
  State<DuploHorizontalTabs<T>> createState() => _DuploHorizontalTabsState<T>();
}

class _DuploHorizontalTabsState<T extends Object>
    extends State<DuploHorizontalTabs<T>> {
  late T _selected;

  @override
  void initState() {
    super.initState();
    _selected = widget.selectedValue;
  }

  @override
  void didUpdateWidget(DuploHorizontalTabs<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedValue != _selected) {
      _selected = widget.selectedValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyle = context.duploTextStyles;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: Border.all(color: theme.border.borderSecondary, width: 1),
        color: theme.background.bgSecondaryAlt,
      ),
      child: CupertinoSlidingSegmentedControl<T>(
        backgroundColor: theme.background.bgSecondaryAlt,
        thumbColor: theme.foreground.fgPrimary,
        padding: widget.padding,
        groupValue: _selected,
        onValueChanged: (value) {
          if (value != null) {
            setState(() => _selected = value);
            widget.onChanged(value);
          }
        },
        children: {
          for (var option in widget.options)
            option: Padding(
              padding: widget.itemPadding,
              child: _buildTabContent(option, theme, textStyle),
            ),
        },
      ),
    );
  }

  Widget _buildTabContent(
    T option,
    DuploThemeData theme,
    TextStyles textStyle,
  ) {
    final isSelected = _selected == option;
    final badgeText = widget.badgeBuilder?.call(option);

    if (badgeText != null && badgeText.isNotEmpty) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: DuploText(
              text: widget.textBuilder(option),
              style: textStyle.textSm,
              color:
                  isSelected
                      ? theme.utility.utilityGray50
                      : theme.text.textTertiary,
              fontWeight: DuploFontWeight.semiBold,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
            decoration: BoxDecoration(
              color: theme.utility.utilityGray50,
              borderRadius: BorderRadius.circular(3),
              border: Border.all(color: theme.utility.utilityGray200, width: 1),
            ),
            child: DuploText(
              text: badgeText,
              style: textStyle.textXs,
              color: theme.text.textSecondary,
              fontWeight: DuploFontWeight.medium,
            ),
          ),
        ],
      );
    }

    return DuploText(
      text: widget.textBuilder(option),
      style: textStyle.textSm,
      color: isSelected ? theme.utility.utilityGray50 : theme.text.textTertiary,
      fontWeight: DuploFontWeight.semiBold,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }
}

enum _DuploHorizontalTabsVariant { buttonBorder }
