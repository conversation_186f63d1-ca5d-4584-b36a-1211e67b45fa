import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class DuploWarningContainer extends StatelessWidget {
  final String? title;
  final String? description;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  const DuploWarningContainer({
    super.key,
    this.title,
    this.description,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.fromLTRB(18, 20, 18, 10),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: context.duploTheme.utility.utilityError50,
        border: Border.all(
          color: context.duploTheme.utility.utilityError200,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Color(0x0C101828),
            blurRadius: 2,
            offset: Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.warning_amber,
            color: context.duploTheme.utility.utilityError600,
            size: 20,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  DuploText(
                    text: title,
                    style: context.duploTextStyles.textSm,
                    fontWeight: DuploFontWeight.semiBold,
                    color: context.duploTheme.text.textSecondary,
                  ),
                if (title != null) const SizedBox(height: 4),
                if (description != null)
                  DuploText(
                    text: description,
                    style: context.duploTextStyles.textSm,
                    maxLines: 10,
                    textAlign: TextAlign.start,
                    color: context.duploTheme.text.textSecondary,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
