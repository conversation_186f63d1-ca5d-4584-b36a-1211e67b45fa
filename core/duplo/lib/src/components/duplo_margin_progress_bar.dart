import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tap/duplo_tap.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/theming/duplo_theme_data.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

/// Represents the different states of the margin progress bar
enum _MarginProgressBarState {
  /// Account has zero balance - shows "Add Funds" message
  zeroBalance,

  /// Account has funds but no active trades - shows "Trade to Activate" message
  noActiveTrades,

  /// Account has active trades - shows actual margin level percentage
  activeTrades,
}

/// A reusable margin level progress bar that visualizes margin percentage
/// with indicators for margin call and stop out levels.
///
/// Supports three distinct states:
/// 1. Zero Balance: Shows "Add Funds" message with greyed-out progress bar
/// 2. No Active Trades: Shows "Trade to Activate" message with greyed-out progress bar
/// 3. Active Trades: Shows actual margin level with color-coded progress bar
class DuploMarginProgressBar extends StatelessWidget {
  /// Creates a DuploMarginProgressBar.
  ///
  /// The [marginLevel] represents the margin percentage (e.g., 150%).
  /// The bar is visually capped at 300% but will properly display any value.
  ///
  /// For enhanced state management, provide [balance] and [margin] parameters:
  /// - If balance == 0: Shows "Add Funds" state
  /// - If balance > 0 && margin == 0: Shows "Trade to Activate" state
  /// - If margin > 0: Shows active trades state with actual margin level
  const DuploMarginProgressBar({
    super.key,
    required this.marginLevel,
    this.balance,
    this.margin,
    this.customMessage,
    this.height = 8.0,
    this.padding,
    this.backgroundColor,
    this.customErrorColor,
    this.customWarningColor,
    this.customSafeColor,
    this.customProtectedColor,
    this.showStopOut = false,
    this.showMarginCall = false,
    this.showHeader = false,
    this.barPadding,
    this.onInfoPressed,
    this.showMarginTextBesideBar = false,
  });

  /// The margin level percentage (e.g., 150 for 150%)
  final double marginLevel;

  /// Account balance - used to determine zero balance state
  final double? balance;

  /// Used margin - used to determine if there are active trades
  final double? margin;

  /// Custom message to override default state messages
  final String? customMessage;

  /// The height of the progress bar
  final double height;

  /// Optional padding around the progress bar
  final EdgeInsetsGeometry? padding;

  /// Optional padding around the bar itself
  final EdgeInsetsGeometry? barPadding;

  /// Optional background color of the progress bar
  final Color? backgroundColor;

  /// Optional custom color for the danger zone (below stopOutLevel)
  final Color? customErrorColor;

  /// Optional custom color for the warning zone (between stopOutLevel and marginCallLevel)
  final Color? customWarningColor;

  /// Optional custom color for the safe zone (above marginCallLevel)
  final Color? customSafeColor;

  /// Optional custom color for the protected zone (300%+)
  final Color? customProtectedColor;

  /// Whether to show stop out indicator (default is true)
  final bool showStopOut;

  /// Whether to show margin call indicator (default is true)
  final bool showMarginCall;

  final bool showHeader;

  final VoidCallback? onInfoPressed;

  /// Whether to show the margin related text beside the bar
  final bool showMarginTextBesideBar;

  /// Stop out level (30%)
  static const double stopOutLevel = 30.0;

  /// Margin call level (100%)
  static const double marginCallLevel = 100.0;

  /// Caution level (200%)
  static const double cautionLevel = 200.0;

  /// Protected level (300%)
  static const double protectedLevel = 300.0;

  /// Flat level (0%)
  static const double flatLevel = 0.0;

  /// Visual cap level (300%)
  static const double maxVisualLevel = 300.0;

  /// Determines the current state of the margin progress bar
  _MarginProgressBarState get _currentState {
    // If balance and margin are provided, use enhanced state logic
    if (balance != null && margin != null) {
      if (balance! <= 0) {
        return _MarginProgressBarState.zeroBalance;
      } else if (margin! <= 0) {
        return _MarginProgressBarState.noActiveTrades;
      }
      return _MarginProgressBarState.activeTrades;
    }
    // Fallback to legacy behavior - always show active trades state
    return _MarginProgressBarState.activeTrades;
  }

  /// Gets the appropriate message for the current state
  String _getStateMessage(BuildContext context) {
    if (customMessage != null) {
      return customMessage!;
    }

    final l10n = EquitiLocalization.of(context);
    switch (_currentState) {
      case _MarginProgressBarState.zeroBalance:
        return l10n.duplo_addFunds;
      case _MarginProgressBarState.noActiveTrades:
        return l10n.duplo_tradeToActivate;
      case _MarginProgressBarState.activeTrades:
        return "${EquitiFormatter.decimalPatternDigits(value: marginLevel, digits: 2, locale: Localizations.localeOf(context).languageCode)}%";
    }
  }

  /// Determines if the progress bar should be in disabled/greyed-out state
  bool get _isDisabledState {
    return _currentState == _MarginProgressBarState.zeroBalance ||
        _currentState == _MarginProgressBarState.noActiveTrades;
  }

  /// Determines the color based on margin level and current state
  Color _getColorForMarginLevel(DuploThemeData theme) {
    // For disabled states, use greyed-out color
    if (_isDisabledState) {
      return Colors.transparent;
    }

    // For active trades, use existing color logic
    if (marginLevel >= protectedLevel) {
      // Protected (300%+)
      return customProtectedColor ?? theme.foreground.fgBrandSecondary;
    } else if (marginLevel >= cautionLevel) {
      // Safe (299-200%)
      return customSafeColor ?? theme.foreground.fgSuccessSecondary;
    } else if (marginLevel > marginCallLevel) {
      // Caution (199-101%)
      return customWarningColor ?? theme.foreground.fgWarningSecondary;
    } else if (marginLevel > stopOutLevel) {
      // Margin Call (100-31%)
      return customErrorColor ?? theme.foreground.fgErrorSecondary;
    } else if (marginLevel > flatLevel) {
      // Stop Out (30-1%)
      return customErrorColor ?? theme.utility.utilityError700;
    }
    // Flat (0%)
    return Colors.transparent;
  }

  /// Determines the text based on margin level and current state
  String _getTextForMarginLevel(EquitiLocalization localization) {
    if (marginLevel >= protectedLevel) {
      // Protected (300%+)
      return localization.trader_marginProtected;
    } else if (marginLevel >= cautionLevel) {
      // Safe (299-200%)
      return localization.trader_marginSafe;
    } else if (marginLevel > marginCallLevel) {
      // Caution (199-101%)
      return localization.trader_marginCaution;
    } else if (marginLevel > stopOutLevel) {
      // Margin Call (100-31%)
      return localization.trader_marginCallTitle;
    } else if (marginLevel > flatLevel) {
      // Stop Out (30-1%)
      return localization.trader_marginStopOut;
    }
    // Flat (0%)
    return '-';
  }

  /// Determines the text color for the state message
  Color _getTextColorForState(DuploThemeData theme) {
    // For "Add Funds" and "Trade to Activate" states, use primary text color
    if (_currentState == _MarginProgressBarState.zeroBalance ||
        _currentState == _MarginProgressBarState.noActiveTrades) {
      return theme.text.textPrimary;
    }

    // For active trades, use the margin level colors
    return _getColorForMarginLevel(theme);
  }

  /// Converts margin level to progress bar value (between 0.0 and 1.0)
  /// Higher margin levels result in fuller bars (inverse to actual risk)
  double _getProgressValue() {
    // For disabled states, show empty progress bar
    if (_isDisabledState) {
      return 0.0;
    }

    // For active trades, use existing logic
    if (marginLevel <= flatLevel) {
      return 0.0; // Flat state
    }
    // Cap the display at maxVisualLevel (300%)
    return marginLevel >= maxVisualLevel ? 1.0 : marginLevel / maxVisualLevel;
  }

  /// Returns the total height of this component
  double getHeight() {
    double totalHeight = height + 8; // The base progress bar height

    if (padding != null) {
      totalHeight += padding!.vertical;
    }

    // Add header height if shown
    if (showHeader) {
      totalHeight +=
          40.0; // 8.0 padding top + 24.0 for icon/text + 8.0 padding bottom
    }

    return totalHeight;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = context.duploTextStyles;
    final progressValue = _getProgressValue();
    final stateMessage = _getStateMessage(context);
    final localization = EquitiLocalization.of(context);

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showHeader)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  DuploText(
                    text: EquitiLocalization.of(context).duplo_marginLevel,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: duploTextStyles.textSm,
                    fontWeight: DuploFontWeight.medium,
                    color: theme.text.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  DuploTap(
                    onTap: onInfoPressed,
                    child: Assets.images.help.svg(
                      height: 12,
                      width: 12,
                      colorFilter: ColorFilter.mode(
                        theme.foreground.fgQuaternary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  const Spacer(),
                  DuploText(
                    text: stateMessage,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: duploTextStyles.textSm,
                    fontWeight: DuploFontWeight.medium,
                    color: _getTextColorForState(theme),
                  ),
                ],
              ),
            ),
          Padding(
            padding: barPadding ?? EdgeInsets.zero,
            child: LayoutBuilder(
              builder: (builderContext, constraints) {
                final availableWidth = constraints.maxWidth;

                return Stack(
                  children: [
                    // Base progress bar
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 2, bottom: 2),
                            child: LinearProgressIndicator(
                              value: progressValue,
                              minHeight: height,
                              borderRadius: BorderRadius.circular(999),
                              backgroundColor:
                                  backgroundColor ??
                                  theme.background.bgQuaternary,
                              color: _getColorForMarginLevel(theme),
                            ),
                          ),
                        ),
                        if (showMarginTextBesideBar) ...[
                          const SizedBox(width: 8),
                          DuploText(
                            text: _getTextForMarginLevel(localization),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: duploTextStyles.textXs,
                            color: _getTextColorForState(theme),
                          ),
                        ],
                      ],
                    ),

                    // Stop out indicator at 30%
                    if (showStopOut)
                      PositionedDirectional(
                        start: (stopOutLevel / maxVisualLevel) * availableWidth,
                        top: 0,
                        bottom: 0,
                        child: Container(
                          width: 6,
                          height: 12,
                          decoration: BoxDecoration(
                            color: theme.utility.utilityGray600,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: theme.foreground.fgWhite,
                              width: 1,
                            ),
                          ),
                        ),
                      ),

                    // Margin call indicator at 100%
                    if (showMarginCall)
                      PositionedDirectional(
                        start:
                            (marginCallLevel / maxVisualLevel) * availableWidth,
                        top: 0,
                        bottom: 0,
                        child: Container(
                          width: 6,
                          height: 12,
                          decoration: BoxDecoration(
                            color: theme.utility.utilityGray400,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: theme.foreground.fgWhite,
                              width: 1,
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
