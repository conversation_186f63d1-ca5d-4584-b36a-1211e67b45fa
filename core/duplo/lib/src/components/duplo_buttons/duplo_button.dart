// ignore_for_file: unused_element

import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_state.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button_utils/duplo_buttons_enum.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text_style.dart';
import 'package:flutter/material.dart';

/// A customizable button widget that supports different styles and states.
///
/// The DuploButton can be configured with various visual styles through factory
/// constructors like [DuploButton.defaultPrimary], [DuploButton.sellPrimary],
/// [DuploButton.buyPrimary], [DuploButton.secondary], [DuploButton.tertiary],
/// and [DuploButton.link].
///
/// Features include:
/// * Leading and trailing icons
/// * Loading states with custom loading text
/// * Disabled states
/// * Hover effects
/// * Error and success states
/// * Full width option
/// * Custom colors for various states
///
/// Example usage:
/// ```
/// DuploButton.defaultPrimary(
///   title: 'Click Me',
///   onTap: () => print('Button tapped'),
///   isLoading: false,
///   useFullWidth: true,
/// )
/// ```
class DuploButton extends StatefulWidget {
  /// Creates a primary button with default styling.
  ///
  /// Use this for the main call-to-action buttons in your interface.
  factory DuploButton.defaultPrimary({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    String? trailingIcon,
    bool useFullWidth = false,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    bool useMaterial = false,
    String? loadingText,
    String? semanticsIdentifier,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.defaultPrimary,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      isFocused: isFocused,
      useMaterial: useMaterial,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  /// Creates a primary bold button.
  ///
  /// This button is styled with a bold text and a primary color.
  /// It is typically used for important actions or calls-to-action.
  factory DuploButton.primaryBold({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    String? trailingIcon,
    bool useFullWidth = true,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    String? loadingText,
    bool useMaterial = false,
    bool useAssetColor = false,
    String? semanticsIdentifier,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.primaryBold,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      useAssetColor: useAssetColor,
      onTap: onTap,
      isLoading: isLoading,
      isFocused: isFocused,
      useMaterial: useMaterial,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  /// Creates a primary button styled for sell actions.
  ///
  /// Typically used for sell-related actions in trading interface.
  factory DuploButton.sellPrimary({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    String? trailingIcon,
    bool useFullWidth = true,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    bool isError = false,
    bool isSuccess = false,
    bool useMaterial = false,
    String? loadingText,
    String? semanticsIdentifier,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.sellPrimary,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      isFocused: isFocused,
      isError: isError,
      isSuccess: isSuccess,
      useMaterial: useMaterial,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  /// Creates a primary button styled for buy actions.
  ///
  /// Typically used for buy-related actions in trading or interface.
  factory DuploButton.buyPrimary({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    String? trailingIcon,
    bool useFullWidth = true,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    bool isError = false,
    bool isSuccess = false,
    bool useMaterial = false,
    String? loadingText,
    String? semanticsIdentifier,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.buyPrimary,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      isFocused: isFocused,
      isError: isError,
      isSuccess: isSuccess,
      useMaterial: useMaterial,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  /// Creates a secondary button.
  ///
  /// Use this for actions that are important but not the primary focus.
  factory DuploButton.secondary({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    String? trailingIcon,
    bool useFullWidth = false,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    bool useMaterial = false,
    String? loadingText,
    String? semanticsIdentifier,
    double? borderThickness,
    Color? iconColor,
    DuploTextStyle? textStyle,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.secondary,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      isFocused: isFocused,
      useMaterial: useMaterial,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
      borderThickness: borderThickness,
      iconColor: iconColor,
      textStyle: textStyle,
    );
  }

  /// Creates a tertiary button.
  ///
  /// Use this for less prominent actions or alternative options.
  factory DuploButton.tertiary({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    Color? iconColor,
    String? trailingIcon,
    bool useFullWidth = false,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    String? loadingText,
    bool useMaterial = false,
    String? semanticsIdentifier,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.tertiary,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      isFocused: isFocused,
      iconColor: iconColor,
      useMaterial: useMaterial,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  /// Creates a link-styled button.
  ///
  /// Use this for navigation actions or when you want the button to appear as a hyperlink.
  factory DuploButton.link({
    Key? key,
    required String title,
    required VoidCallback onTap,
    String? leadingIcon,
    String? trailingIcon,
    bool useFullWidth = false,
    bool isDisabled = false,
    bool isLoading = false,
    bool isFocused = false,
    Color? textColor,
    Color? iconColor,
    String? loadingText,
    String? semanticsIdentifier,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.link,
      title: title,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      useFullWidth: useFullWidth,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      textColor: textColor,
      iconColor: iconColor,
      isFocused: isFocused,
      loadingText: loadingText,
      semanticsIdentifier: semanticsIdentifier,
    );
  }

  factory DuploButton.applePay({
    Key? key,
    required VoidCallback onTap,
    bool isLoading = false,
    String? loadingText,
    bool? applePayBorderEnabled,
    bool? isApplePayDarkMode,
    bool isDisabled = false,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.applePay,
      useFullWidth: true,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      loadingText: loadingText,
      applePayBorderEnabled: applePayBorderEnabled,
      isPayButtonDarkMode: isApplePayDarkMode,
    );
  }

  factory DuploButton.applePayWithText({
    Key? key,
    required VoidCallback onTap,
    bool isLoading = false,
    String? loadingText,
    bool? applePayBorderEnabled,
    bool? isApplePayDarkMode,
    bool isDisabled = false,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.applePayWithText,
      useFullWidth: true,
      isDisabled: isDisabled,
      onTap: onTap,
      isLoading: isLoading,
      loadingText: loadingText,
      applePayBorderEnabled: applePayBorderEnabled,
      isPayButtonDarkMode: isApplePayDarkMode,
    );
  }

  factory DuploButton.googlePay({
    Key? key,
    required VoidCallback onTap,
    bool isLoading = false,
    String? loadingText,
    bool? isGooglePayDarkMode,
    bool isDisabled = false,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.googlePay,
      useFullWidth: true,
      onTap: onTap,
      isDisabled: isDisabled,
      isLoading: isLoading,
      leadingIcon: Assets.images.googleLogo.keyName,
      loadingText: loadingText,
      isPayButtonDarkMode: isGooglePayDarkMode,
    );
  }

  factory DuploButton.googlePayWithText({
    Key? key,
    required VoidCallback onTap,
    bool isLoading = false,
    String? loadingText,
    bool? isGooglePayDarkMode,
    bool isDisabled = false,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.googlePayWithText,
      useFullWidth: true,
      onTap: onTap,
      isLoading: isLoading,
      loadingText: loadingText,
      isDisabled: isDisabled,
      leadingIcon: Assets.images.googleLogo.keyName,
      isPayButtonDarkMode: isGooglePayDarkMode,
    );
  }

  factory DuploButton.uaePassSignup({
    Key? key,
    required VoidCallback onTap,
    bool isLoading = false,
    String? loadingText,
    bool? isGooglePayDarkMode,
    bool isDisabled = false,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.uaePassSignup,
      onTap: onTap,
    );
  }

  factory DuploButton.uaePassLogin({
    Key? key,
    required VoidCallback onTap,
    bool isLoading = false,
    String? loadingText,
    bool? isGooglePayDarkMode,
    bool isDisabled = false,
  }) {
    return DuploButton._(
      key: key,
      type: DuploButtonType.uaePassLogin,
      onTap: onTap,
    );
  }

  /// Private constructor used by the factory constructors.
  const DuploButton._({
    super.key,
    this.title,
    required this.type,
    this.leadingIcon,
    this.trailingIcon,
    this.textColor,
    this.iconColor,
    this.textFontWeight,
    this.textStyle,
    this.useFullWidth = false,
    this.isDisabled = false,
    this.disabledColor,
    this.disabledBackgroundColor,
    required this.onTap,
    this.isLoading = false,
    this.isFocused = false,
    this.backgroundColor,
    this.disabledBorderColor,
    this.borderColor,
    this.loadingText,
    this.hoverBackgroundColor,
    this.hoverBorderColor,
    this.hoverTextColor,
    this.hoverIconColor,
    this.successBackgroundColor,
    this.errorBackgroundColor,
    this.successColor,
    this.errorColor,
    this.isError = false,
    this.isSuccess = false,
    this.iconSize,
    this.loadingSize,
    this.useMaterial = false,
    this.useAssetColor = false,
    this.errorTextColor,
    this.successTextColor,
    this.errorIconColor,
    this.successIconColor,
    this.errorBorderColor,
    this.successBorderColor,
    this.semanticsIdentifier,
    this.isPayButtonDarkMode,
    this.applePayBorderEnabled,
    this.borderThickness,
  });

  /// Optional icon to display before the button text
  final String? leadingIcon;

  /// Optional icon to display after the button text
  final String? trailingIcon;

  /// The text to display on the button
  final String? title;

  /// Text to display when the button is in a loading state
  final String? loadingText;

  /// Color for the button text
  final Color? textColor;

  /// Color for the button icons
  final Color? iconColor;

  /// Color when the button is disabled
  final Color? disabledColor;

  /// Background color when the button is disabled
  final Color? disabledBackgroundColor;

  /// Default background color
  final Color? backgroundColor;

  /// Border color when disabled
  final Color? disabledBorderColor;

  /// Default border color
  final Color? borderColor;

  /// Background color on hover
  final Color? hoverBackgroundColor;

  /// Border color on hover
  final Color? hoverBorderColor;

  /// Text color on hover
  final Color? hoverTextColor;

  /// Icon color on hover
  final Color? hoverIconColor;

  /// Background color when in success state
  final Color? successBackgroundColor;

  /// Background color when in error state
  final Color? errorBackgroundColor;

  /// Text/icon color when in success state
  final Color? successColor;

  /// Text/icon color when in error state
  final Color? errorColor;

  /// Text/icon color when in error state
  final Color? errorTextColor;

  /// Text/icon color when in success state
  final Color? successTextColor;

  /// Text/icon color when in error state
  final Color? errorIconColor;

  /// Text/icon color when in success state
  final Color? successIconColor;

  /// Border color when in error state
  final Color? errorBorderColor;

  /// Border color when in success state
  final Color? successBorderColor;

  /// Font weight for the button text
  final DuploFontWeight? textFontWeight;

  /// Text style for the button text
  final DuploTextStyle? textStyle;

  /// Whether the button should take up the full width of its container
  final bool useFullWidth;

  /// Whether the button is disabled
  final bool isDisabled;

  /// Whether the button should use material design
  final bool useMaterial;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// Whether the button has focus
  final bool isFocused;

  /// Whether the button is in an error state
  final bool isError;

  /// Whether the button is in a success state
  final bool isSuccess;

  /// Callback function when the button is tapped
  final VoidCallback onTap;

  /// The type of button (primary, secondary, etc.)
  final DuploButtonType type;

  /// Size of the icons
  final double? iconSize;

  /// Size of the loading indicator
  final double? loadingSize;

  ///Leading and trailing icon uses default colors
  final bool useAssetColor;

  /// Semantics identifier for the button
  final String? semanticsIdentifier;

  /// Whether the button is in apple pay dark mode
  final bool? isPayButtonDarkMode;

  /// Whether the border of apple pay button is enabled
  final bool? applePayBorderEnabled;

  final double? borderThickness;

  @override
  State<DuploButton> createState() => DuploButtonState();
}
