import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_span.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

/// A reusable widget that displays financial information with formatted numbers
/// and appropriate color coding. Typically used for displaying equity, balance,
/// or other financial metrics with profit/loss indicators.
class DuploTopChartNumbersWidget extends StatelessWidget {
  /// The title text to display above the main amount
  final String title;

  /// The main amount to display (e.g., equity, balance)
  final double amount;

  /// The profit/loss amount to display
  final double profitLoss;

  /// The currency code to display (e.g., 'USD', 'EUR')
  final String currency;

  const DuploTopChartNumbersWidget({
    super.key,
    required this.title,
    required this.amount,
    required this.profitLoss,
    required this.currency,
  });

  @override
  Widget build(BuildContext context) {
    final textStyles = context.duploTextStyles;
    final theme = context.duploTheme;
    final locale = Localizations.localeOf(context).toString();

    final formattedAmount = EquitiFormatter.decimalPatternDigits(
      value: amount,
      digits: 2,
      locale: locale,
    );
    final whole = formattedAmount.split('.').firstOrNull!;
    final fraction = formattedAmount.split('.').elementAtOrNull(1);

    final formattedProfitLoss = EquitiFormatter.decimalPatternDigits(
      value: profitLoss,
      digits: 2,
      locale: locale,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: title,
          style: textStyles.textXs,
          color: theme.text.textPrimary,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Directionality(
              textDirection: TextDirection.ltr,
              child: DuploText.rich(
                spans: [
                  DuploTextSpan(
                    text: whole,
                    style: textStyles.displaySm,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  DuploTextSpan(
                    text: ".",
                    style: textStyles.textLg,
                    color: theme.text.textSecondary,
                  ),
                  DuploTextSpan(
                    text: fraction!,
                    style: textStyles.textLg,
                    color: theme.text.textSecondary,
                  ),
                  DuploTextSpan(
                    text: " ",
                    style: textStyles.textLg,
                    color: theme.text.textSecondary,
                  ),
                  DuploTextSpan(
                    text: currency,
                    style: textStyles.textLg,
                    color: theme.text.textSecondary,
                  ),
                ],
              ),
            ),
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            DuploText(
              text: EquitiLocalization.of(context).duplo_profit,
              style: textStyles.textSm,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textTertiary,
            ),
            const SizedBox(width: 8),
            Directionality(
              textDirection: TextDirection.ltr,
              child: DuploText.rich(
                spans: [
                  if (profitLoss > 0)
                    DuploTextSpan(
                      text: "+",
                      style: textStyles.textXs,
                      fontWeight: DuploFontWeight.medium,
                      color: theme.foreground.fgSuccessSecondary,
                    ),
                  DuploTextSpan(
                    text: "${formattedProfitLoss} ",
                    style: textStyles.textXs,
                    fontWeight: DuploFontWeight.medium,
                    color:
                        profitLoss > 0
                            ? theme.foreground.fgSuccessSecondary
                            : profitLoss < 0
                            ? theme.foreground.fgErrorSecondary
                            : theme.text.textPrimary,
                  ),
                  DuploTextSpan(
                    text: currency,
                    style: textStyles.textXs,
                    fontWeight: DuploFontWeight.medium,
                    color:
                        profitLoss > 0
                            ? theme.foreground.fgSuccessSecondary
                            : profitLoss < 0
                            ? theme.foreground.fgErrorSecondary
                            : theme.text.textPrimary,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
