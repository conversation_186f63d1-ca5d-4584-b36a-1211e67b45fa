part of 'create_account_bloc.dart';

@freezed
sealed class CreateAccountState with _$CreateAccountState {
  factory CreateAccountState({
    final List<TradingPlatform>? tradingPlatforms,
    final AccountCreationRequestModel? accountCreationRequestModel,
    final int? pageIndex,
    @Default(CreateAccountProgressState.dataLoading())
    final CreateAccountProgressState progressState,
  }) = _CreateAccountState;
}

@freezed
sealed class CreateAccountProgressState with _$CreateAccountProgressState {
  const factory CreateAccountProgressState.dataLoading() = DataLoadingState;
  const factory CreateAccountProgressState.dataLoaded() = DataLoadedState;
  const factory CreateAccountProgressState.dataLoadingError() =
      DataLoadingError;
  const factory CreateAccountProgressState.dataSubmitting() =
      DataSubmittingState;
  const factory CreateAccountProgressState.dataSubmitted({
    required AccountCreationResponseData res,
  }) = DataSubmittedState;
  const factory CreateAccountProgressState.dataSubmitError({String? message}) =
      DataSubmitError;

  const factory CreateAccountProgressState.dataUpdated() = DataUpdatedState;

  const factory CreateAccountProgressState.navigatedNext() = NavigatedNextState;

  const factory CreateAccountProgressState.navigatedBack() = NavigatedBackState;
}
