part of 'create_account_bloc.dart';

@freezed
sealed class CreateAccountEvent with _$CreateAccountEvent {
  const factory CreateAccountEvent.navigateToCreareAccountMainScreen({
    required domain.CreateAccountFlow createAccountFlow,
  }) = NavigateToCreateAccountMainScreen;

  const factory CreateAccountEvent.fetchAccountCreationData({
    required domain.CreateAccountFlow createAccountFlow,
  }) = FetchAccountCreationData;

  const factory CreateAccountEvent.selectPlatform({
    required AccountCreationPlatform platform,
  }) = SelectPlatform;

  const factory CreateAccountEvent.selectAccountType({
    required String accountType,
  }) = SelectAccountType;

  const factory CreateAccountEvent.selectCurrency({required String currency}) =
      SelectCurrency;

  const factory CreateAccountEvent.selectVariant({required bool swapFree}) =
      SelectVariant;

  const factory CreateAccountEvent.selectLeverage({required String leverage}) =
      SelectLeverage;

  const factory CreateAccountEvent.updateNickname({required String nickname}) =
      UpdateNickname;

  const factory CreateAccountEvent.updatePassword({required String password}) =
      UpdatePassword;

  const factory CreateAccountEvent.navigateToNext({int? pageIndex}) =
      NavigateToNext;

  const factory CreateAccountEvent.navigateToPrevious({int? pageIndex}) =
      NavigateToPrevious;

  const factory CreateAccountEvent.submitAccountCreationData({
    required domain.CreateAccountFlow createAccountFlow,
  }) = SubmitAccountCreationData;
}
