import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart' as domain;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding/src/analytics/onboarding_analytics.dart';
import 'package:onboarding/src/data/account_creation_response_model.dart';
import 'package:onboarding/src/domain/exceptions/post_account_creation_data_exception/post_account_creation_data_exception.dart';
import 'package:onboarding/src/domain/model/account_creation/account_creation_request_model.dart';
import 'package:onboarding/src/domain/usecase/submit_account_creation_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/account_creation/helpers/account_creation_helper.dart';
import 'package:prelude/prelude.dart';

part 'create_account_bloc.freezed.dart';
part 'create_account_event.dart';
part 'create_account_state.dart';

class CreateAccountBloc extends Bloc<CreateAccountEvent, CreateAccountState> {
  CreateAccountBloc(
    this._navigation,
    this._getCreateLiveAccountDataUseCase,
    this._getCreateDemoAccountDataUseCase,
    this._submitAccountCreationUseCase,
    this._onboardingAnalytics,
  ) : super(CreateAccountState()) {
    on<NavigateToCreateAccountMainScreen>(_navigateToCreateAccountMainScreen);
    on<FetchAccountCreationData>(_fetchAccountCreationData);
    on<SelectPlatform>(_selectPlatform);
    on<SelectAccountType>(_selectAccountType);
    on<SelectCurrency>(_selectCurrency);
    on<SelectVariant>(_selectVariant);
    on<SelectLeverage>(_selectLeverage);
    on<UpdateNickname>(_updateNickname);
    on<UpdatePassword>(_updatePassword);
    on<NavigateToNext>(_navigateToNext);
    on<NavigateToPrevious>(_navigateToBack);
    on<SubmitAccountCreationData>(_submitAccountCreationData);
  }

  final OnboardingNavigation _navigation;
  final GetCreateLiveAccountDataUseCase _getCreateLiveAccountDataUseCase;
  final GetCreateDemoAccountDataUseCase _getCreateDemoAccountDataUseCase;
  final SubmitAccountCreationUseCase _submitAccountCreationUseCase;
  final OnboardingAnalytics _onboardingAnalytics;

  void _navigateToCreateAccountMainScreen(
    NavigateToCreateAccountMainScreen event,
    Emitter<CreateAccountState> emit,
  ) {
    _navigation.navigateToCreateAccountMain(
      createAccountFlow: event.createAccountFlow,
    );
  }

  void _navigateToNext(NavigateToNext event, Emitter<CreateAccountState> emit) {
    emit(
      state.copyWith(
        pageIndex: event.pageIndex! + 1,
        progressState: CreateAccountProgressState.navigatedNext(),
      ),
    );
  }

  void _navigateToBack(
    NavigateToPrevious event,
    Emitter<CreateAccountState> emit,
  ) {
    emit(
      state.copyWith(
        pageIndex: event.pageIndex! - 1,
        progressState: CreateAccountProgressState.navigatedBack(),
      ),
    );
  }

  void _fetchAccountCreationData(
    FetchAccountCreationData event,
    Emitter<CreateAccountState> emit,
  ) async {
    emit(
      state.copyWith(progressState: CreateAccountProgressState.dataLoading()),
    );

    // Get broker settings type for getting the correct broker settings
    final brokerSettingsType = _getBrokerSettingsType(event.createAccountFlow);

    final accountCreationFetchResult = switch (event.createAccountFlow) {
      domain.CreateAccountFlow.demoAccount =>
        await _getCreateDemoAccountDataUseCase(
          brokerSettingsType: brokerSettingsType,
        ).run(),
      domain.CreateAccountFlow.firstLiveAccount ||
      domain.CreateAccountFlow.additionalLiveAccount =>
        await _getCreateLiveAccountDataUseCase(
          brokerSettingsType: brokerSettingsType,
        ).run(),
    };

    if (isClosed) return;

    accountCreationFetchResult.fold(
      (exception) {
        emit(
          state.copyWith(
            progressState: CreateAccountProgressState.dataLoadingError(),
          ),
        );
      },
      (response) {
        //setting default values for state
        final defaultAccountCreationRequestModel =
            _getDefaultAccountCreationRequestModel(response);

        // Configuring for specific flow
        final accountCreationModel = _setAdditionalDataForConfig(
          defaultAccountCreationRequestModel,
          event.createAccountFlow,
        );

        emit(
          state.copyWith(
            tradingPlatforms: response,
            accountCreationRequestModel: accountCreationModel,
            progressState: CreateAccountProgressState.dataLoaded(),
          ),
        );
      },
    );
  }

  void _selectPlatform(SelectPlatform event, Emitter<CreateAccountState> emit) {
    final selectedPlatform = state.tradingPlatforms!.firstOrNullWhere(
      (platform) => platform.code == event.platform,
    );

    final platformDefaults = _getDefaultsForPlatform(selectedPlatform!);

    emit(
      state.copyWith(
        accountCreationRequestModel: platformDefaults,
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _selectAccountType(
    SelectAccountType event,
    Emitter<CreateAccountState> emit,
  ) {
    final currentPlatform = AccountCreationHelper.findPlatformByCode(
      state.tradingPlatforms!,
      state.accountCreationRequestModel!.platform,
    );

    final newDefaults = _getDefaultsForAccountType(
      currentPlatform,
      event.accountType,
    );

    emit(
      state.copyWith(
        accountCreationRequestModel: newDefaults,
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _selectCurrency(SelectCurrency event, Emitter<CreateAccountState> emit) {
    final updatedModel = state.accountCreationRequestModel!.copyWith(
      currency: event.currency,
    );

    emit(
      state.copyWith(
        accountCreationRequestModel: updatedModel,
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _selectVariant(SelectVariant event, Emitter<CreateAccountState> emit) {
    final currentPlatform = AccountCreationHelper.findPlatformByCode(
      state.tradingPlatforms!,
      state.accountCreationRequestModel!.platform,
    );

    final currentAccountType = AccountCreationHelper.findAccountTypeByName(
      currentPlatform,
      state.accountCreationRequestModel!.platformAccountType,
    );

    final newDefaults = _getDefaultsForVariant(
      currentAccountType,
      event.swapFree,
    );

    emit(
      state.copyWith(
        accountCreationRequestModel: newDefaults,
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _selectLeverage(SelectLeverage event, Emitter<CreateAccountState> emit) {
    emit(
      state.copyWith(
        accountCreationRequestModel: state.accountCreationRequestModel!
            .copyWith(leverage: event.leverage),
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _updateNickname(UpdateNickname event, Emitter<CreateAccountState> emit) {
    emit(
      state.copyWith(
        accountCreationRequestModel: state.accountCreationRequestModel!
            .copyWith(accountNickname: event.nickname),
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _updatePassword(UpdatePassword event, Emitter<CreateAccountState> emit) {
    emit(
      state.copyWith(
        accountCreationRequestModel: state.accountCreationRequestModel!
            .copyWith(password: event.password),
        progressState: CreateAccountProgressState.dataUpdated(),
      ),
    );
  }

  void _submitAccountCreationData(
    SubmitAccountCreationData event,
    Emitter<CreateAccountState> emit,
  ) async {
    if (state.accountCreationRequestModel == null) {
      emit(
        state.copyWith(
          progressState: CreateAccountProgressState.dataSubmitError(
            message: 'Nothing to submit',
          ),
        ),
      );
      return;
    }

    emit(
      state.copyWith(
        progressState: CreateAccountProgressState.dataSubmitting(),
      ),
    );

    final accountCreationSubmitResult =
        await _submitAccountCreationUseCase(
          data: state.accountCreationRequestModel!,
        ).run();

    if (isClosed) return;

    accountCreationSubmitResult.fold(
      (err) {
        int? errorCode;
        String? errorMessage;
        //todo: handle error properly
        if (err is PostAccountCreationDataException) {
          errorCode = err.code;
          errorMessage = err.message;
          switch (err) {
            case PostAccountCreationDataUnknownError():
              emit(
                state.copyWith(
                  progressState: CreateAccountProgressState.dataSubmitError(
                    message: err.message,
                  ),
                ),
              );
          }
        } else {
          emit(
            state.copyWith(
              progressState: CreateAccountProgressState.dataSubmitError(),
            ),
          );
        }
        if (event.createAccountFlow ==
            domain.CreateAccountFlow.firstLiveAccount) {
          _onboardingAnalytics.firstAccountError(
            platform: state.accountCreationRequestModel?.platform.name ?? '',
            accountType:
                state.accountCreationRequestModel?.platformAccountType ?? '',
            accountLeverage: state.accountCreationRequestModel?.leverage ?? '',
            swapSetting:
                state.accountCreationRequestModel?.swapFreeAccount == true,
            code: errorCode,
            message: errorMessage,
          );
        }
      },
      (res) {
        if (event.createAccountFlow ==
            domain.CreateAccountFlow.firstLiveAccount) {
          _onboardingAnalytics.firstAccountComplete(
            platform: state.accountCreationRequestModel?.platform.name ?? '',
            accountType: res.data?.platformAccountType ?? '',
            accountLeverage: res.data?.leverage ?? 0,
            swapSetting: res.data?.swapFreeAccount == true,
          );
        }
        emit(
          state.copyWith(
            progressState: CreateAccountProgressState.dataSubmitted(
              res: res.data!,
            ),
          ),
        );
      },
    );
  }

  AccountCreationRequestModel _getDefaultAccountCreationRequestModel(
    List<TradingPlatform> platforms,
  ) {
    final defaultPlatform = AccountCreationHelper.getDefaultPlatform(platforms);
    final defaultAccountType = AccountCreationHelper.getDefaultAccountType(
      defaultPlatform,
    );
    final defaultVariant = AccountCreationHelper.getDefaultVariant(
      defaultAccountType,
    );
    final defaultCurrency = AccountCreationHelper.getDefaultCurrency(
      defaultAccountType,
    );
    final defaultLeverage = AccountCreationHelper.getDefaultLeverage(
      defaultVariant,
    );

    return AccountCreationRequestModel(
      platform: defaultPlatform.code,
      platformAccountType: defaultAccountType.name,
      currency: defaultCurrency.code,
      swapFreeAccount: defaultVariant?.swapFree,
      leverage: defaultLeverage?.value.toString(),
      accountNickname: '',
      password: state.accountCreationRequestModel?.password,
      accountType: state.accountCreationRequestModel?.accountType,
      isInitialAccount: state.accountCreationRequestModel?.isInitialAccount,
    );
  }

  AccountCreationRequestModel _getDefaultsForPlatform(
    TradingPlatform platform,
  ) {
    final defaultAccountType = AccountCreationHelper.getDefaultAccountType(
      platform,
    );
    final defaultVariant = AccountCreationHelper.getDefaultVariant(
      defaultAccountType,
    );
    final defaultCurrency = AccountCreationHelper.getDefaultCurrency(
      defaultAccountType,
    );
    final defaultLeverage = AccountCreationHelper.getDefaultLeverage(
      defaultVariant,
    );

    return AccountCreationRequestModel(
      platform: platform.code,
      platformAccountType: defaultAccountType.name,
      currency: defaultCurrency.code,
      swapFreeAccount: defaultVariant?.swapFree,
      leverage: defaultLeverage?.value.toString(),
      accountNickname: '',
      password: state.accountCreationRequestModel?.password,
      accountType: state.accountCreationRequestModel?.accountType,
      isInitialAccount: state.accountCreationRequestModel?.isInitialAccount,
    );
  }

  AccountCreationRequestModel _getDefaultsForAccountType(
    TradingPlatform platform,
    String accountType,
  ) {
    final selectedAccountType = AccountCreationHelper.findAccountTypeByName(
      platform,
      accountType,
    );
    final defaultVariant = AccountCreationHelper.getDefaultVariant(
      selectedAccountType,
    );
    final defaultCurrency = AccountCreationHelper.getDefaultCurrency(
      selectedAccountType,
    );
    final defaultLeverage = AccountCreationHelper.getDefaultLeverage(
      defaultVariant,
    );

    return AccountCreationRequestModel(
      platform: platform.code,
      platformAccountType: accountType,
      currency: defaultCurrency.code,
      swapFreeAccount: defaultVariant?.swapFree,
      leverage: defaultLeverage?.value.toString(),
      accountNickname: '',
      password: state.accountCreationRequestModel?.password,
      accountType: state.accountCreationRequestModel?.accountType,
      isInitialAccount: state.accountCreationRequestModel?.isInitialAccount,
    );
  }

  AccountCreationRequestModel _getDefaultsForVariant(
    AccountType accountType,
    bool swapFree,
  ) {
    final currentModel = state.accountCreationRequestModel!;
    final selectedVariant = AccountCreationHelper.getVariantBySwapFree(
      accountType,
      swapFree,
    );
    final defaultLeverage = AccountCreationHelper.getDefaultLeverage(
      selectedVariant,
    );

    return AccountCreationRequestModel(
      platform: currentModel.platform,
      platformAccountType: currentModel.platformAccountType,
      currency: currentModel.currency,
      swapFreeAccount: swapFree,
      leverage: defaultLeverage?.value.toString(),
      accountNickname: '',
      password: state.accountCreationRequestModel?.password,
      accountType: state.accountCreationRequestModel?.accountType,
      isInitialAccount: state.accountCreationRequestModel?.isInitialAccount,
    );
  }

  /// Determines the appropriate broker settings type based on the account creation flow
  BrokerSettingsType _getBrokerSettingsType(domain.CreateAccountFlow flow) {
    return switch (flow) {
      domain.CreateAccountFlow.firstLiveAccount =>
        BrokerSettingsType.initialAccount,
      domain.CreateAccountFlow.additionalLiveAccount ||
      domain
          .CreateAccountFlow
          .demoAccount => BrokerSettingsType.additionalAccount,
    };
  }

  /// setting data according to create account flow
  AccountCreationRequestModel _setAdditionalDataForConfig(
    AccountCreationRequestModel accountCreationModel,
    domain.CreateAccountFlow flow,
  ) {
    switch (flow) {
      case domain.CreateAccountFlow.additionalLiveAccount:
        return accountCreationModel.copyWith(isInitialAccount: false);
      case domain.CreateAccountFlow.demoAccount:
        return accountCreationModel.copyWith(accountType: 'demoTradingAccount');
      case domain.CreateAccountFlow.firstLiveAccount:
        return accountCreationModel;
    }
  }
}
