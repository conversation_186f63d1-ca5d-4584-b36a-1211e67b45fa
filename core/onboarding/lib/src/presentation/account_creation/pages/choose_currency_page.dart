// ignore_for_file: avoid-unsafe-collection-methods

import 'package:broker_settings/broker_settings.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';

class ChooseCurrencyPage extends StatelessWidget {
  const ChooseCurrencyPage({super.key, this.pageIndex});

  final int? pageIndex;

  List<AccountCurrency> getCurrencies(CreateAccountState state) {
    final AccountCreationPlatform selectedPlatform =
        state.accountCreationRequestModel!.platform;

    final List<AccountType> accountTypes =
        state.tradingPlatforms!
            .firstWhere((element) => element.code == selectedPlatform)
            .accountTypes;

    final List<AccountCurrency> currencies =
        accountTypes
            .firstWhere(
              (element) =>
                  element.name ==
                  state.accountCreationRequestModel!.platformAccountType,
            )
            .accountTypeCurrencies;

    return currencies;
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);

    return BlocBuilder<CreateAccountBloc, CreateAccountState>(
      buildWhen: (previous, current) => previous != current,
      builder: (_, state) {
        final currencies = getCurrencies(state);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_chooseAccountCurrency,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 8),
            DuploText(
              text: localization.onboarding_chooseAccountCurrencyDescription,
              style: textStyles.textSm,
              color: theme.text.textSecondary,
              textAlign: TextAlign.start,
            ),
            const SizedBox(height: 24),
            DuploDropDown.selector(
              hint: localization.onboarding_currency,
              dropDownItemModels:
                  currencies.map((item) {
                    return DropDownItemModel(
                      title: item.code,
                      image: FlagProvider.getFlagFromCurrencyCode(item.code),
                    );
                  }).toList(),
              bottomSheetTitle: localization.onboarding_selectCurrency,
              onChanged: (index) {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.selectCurrency(
                    currency: currencies[index].code,
                  ),
                );
              },
              selectedIndex: currencies.indexWhere(
                (currency) =>
                    state.accountCreationRequestModel?.currency ==
                    currency.code,
              ),
              context: context,
            ),
            Spacer(),
            DuploButton.defaultPrimary(
              semanticsIdentifier: "continue_button",
              title: localization.onboarding_continueButton,
              trailingIcon: onboarding.Assets.images.continueIc.keyName,
              onTap: () {
                context.read<CreateAccountBloc>().add(
                  CreateAccountEvent.navigateToNext(pageIndex: pageIndex),
                );
              },
              useFullWidth: true,
            ),
            const SizedBox(height: 32),
          ],
        );
      },
    );
  }
}
