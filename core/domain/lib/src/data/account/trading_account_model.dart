import 'package:domain/src/data/enums/account_type.dart';
import 'package:domain/src/data/enums/platform_account_type.dart';
import 'package:domain/src/data/enums/platform_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'trading_account_model.freezed.dart';
part 'trading_account_model.g.dart';

@freezed
abstract class TradingAccountModel with _$TradingAccountModel {
  const factory TradingAccountModel({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'accountId') required String recordId,
    @Json<PERSON>ey(name: 'platformAccountNumber') required String accountNumber,
    required int accountIdLong,
    String? dateCreated,
    @Default(PlatformAccountType.unknown)
    @JsonKey(
      name: 'platformAccountType',
      unknownEnumValue: PlatformAccountType.unknown,
    )
    PlatformAccountType platformAccountType,
    @<PERSON><PERSON><PERSON>ey(name: 'accountCurrency') required String homeCurrency,
    int? leverage,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'currentBalance') double? balance,
    double? credit,
    double? equity,
    double? margin,
    @J<PERSON><PERSON><PERSON>(name: 'serverCode') String? server,
    @Default(AccountType.unknown)
    @JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown)
    AccountType accountType,
    String? brokerId,
    String? name,
    @JsonKey(
      name: 'platformType',
      unknownEnumValue: PlatformType.unknown,
      fromJson: _platformTypeFromJson,
    )
    @Default(PlatformType.unknown)
    PlatformType platformType,
    String? clientId,
    @Default(false) bool isDemo,
    double? profit,
    String? accountStatus,
    String? leadSource,
    double? grossProfit,
    double? marginLevel,
    required String primaryEmail,
    String? accountGroup,
    String? classification,
    double? freeMargin,
    @Default(false) bool isSwapFree,
    double? balanceAlternateCurrency,
    double? marginAlternateCurrency,
    double? equityAlternateCurrency,
    double? profitAlternateCurrency,
    double? grossProfitAlternateCurrency,
    double? creditAlternateCurrency,
    String? accountCurrencyUsdPair,
    @Default("") String serverName,
    String? nickName,
    @Default("") String platformTypeName,
    @Default(false) bool hasOpenPositions,
  }) = _TradingAccountModel;

  factory TradingAccountModel.fromJson(Map<String, dynamic> json) =>
      _$TradingAccountModelFromJson(json);
}

// Add this helper function outside the class
PlatformType _platformTypeFromJson(Object? value) {
  if (value == null) return PlatformType.unknown;

  // Try to match the string value directly
  if (value is String) {
    for (final type in PlatformType.values) {
      if (type.name.toLowerCase() == value.toLowerCase()) {
        return type;
      }
    }

    switch (value) {
      case 'Equiti':
        return PlatformType.equiti;
      case 'MT4':
        return PlatformType.mt4;
      case 'MT5':
        return PlatformType.mt5;
      case 'EquitiTrader':
        return PlatformType.equitiTrader;
      default:
        return PlatformType.unknown;
    }
  }

  return PlatformType.unknown;
}
